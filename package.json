{"name": "conversion-finder-report", "private": true, "scripts": {"build": "next build", "dev": "next dev", "start-ampt-dev": "ampt run dev", "start-ampt": "ampt dev", "ampt:build": "ampt-next build", "ampt:dev": "next dev", "start": "next start", "lint": "next lint", "up": "ncu --reject typescript,next,@clerk/nextjs,mongodb,eslint -u && npm install", "clean": "rm -rf node_modules package-lock.json .next static && npm install", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@ampt/data": "^1.0.27", "@ampt/nextjs": "^3.0.20", "@ampt/sdk": "^1.0.20", "@better-auth/stripe": "^1.2.12", "@conversionfinder/conversion-finder-utils": "1.1.9", "@conversionfinder/shared-auth": "^1.0.0", "@deepgram/sdk": "^3.10.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fast-csv/format": "^5.0.0", "@fast-csv/parse": "^5.0.0", "@mui/icons-material": "^5.15.20", "@mui/joy": "^5.0.0-beta.52", "@mui/material": "^5.15.20", "@rjsf/antd": "^5.18.4", "@rjsf/core": "^5.18.4", "@rjsf/validator-ajv8": "^5.18.4", "@supabase/supabase-js": "^2.43.5", "@tanstack/react-query": "^5.45.1", "@tanstack/react-table": "^8.17.3", "@types/ua-parser-js": "^0.7.39", "ajv-errors": "^3.0.0", "aws4": "^1.13.2", "axios": "^1.7.2", "better-auth": "^1.2.5", "copy-to-clipboard": "^3.3.3", "dagre": "^0.8.5", "date-fns": "^3.6.0", "decimal.js": "^10.4.3", "elkjs": "^0.9.3", "fast-csv": "^5.0.1", "json-2-csv": "^5.5.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lodash": "^4.17.21", "luxon": "^3.4.4", "mailgun-js": "^0.22.0", "moment-timezone": "^0.5.47", "mongodb": "4.17.2", "next": "^14.1.4", "node-fetch": "^3.3.2", "openai": "^4.77.4", "pubsub-js": "^1.9.5", "radash": "^12.1.0", "react": "^18.3.1", "react-date-range": "^2.0.1", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-flow-renderer": "^10.3.17", "react-json-formatter": "^0.4.0", "react-loading": "^2.0.3", "react-number-format": "^5.4.0", "react-toastify": "^10.0.5", "react18-json-view": "^0.2.8", "shadcn": "^2.3.0", "simpler-state": "^1.2.1", "stream-concat": "^2.0.0", "stripe": "^18.3.0", "tailwindcss-animate": "^1.0.7", "telnyx": "^2.0.0", "ua-parser-js": "^2.0.4", "uuid": "^10.0.0", "vectra": "^0.9.0", "zod": "^3.24.1"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@tanstack/react-query-devtools": "^5.45.1", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.5", "@types/luxon": "^3.4.2", "@types/node": "^20", "@types/react": "^18", "@types/react-date-range": "^1.4.9", "@types/react-dom": "^18", "autoprefixer": "^10", "eslint": "^8", "eslint-config-next": "14.2.4", "jest": "^29.7.0", "postcss": "^8", "tailwindcss": "^3", "ts-jest": "^29.3.3", "typescript": "5.3.3"}, "main": "entrypoint.mjs", "type": "module", "ampt": {"app": "conversion-finder-report", "runtime": "nodejs18", "buildRuntime": "nodejs20", "org": "conversionfinder"}, "files": ["*"]}