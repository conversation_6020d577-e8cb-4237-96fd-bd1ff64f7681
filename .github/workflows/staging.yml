name: staging
on:
  push:
    branches:
      - staging

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    env:
      AMPT_API_KEY: ${{ secrets.AMPT_API_KEY }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20
      - uses: actions/cache@v4
        with:
          path: |
            ~/.npm
            ${{ github.workspace }}/.next/cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-${{ hashFiles('**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-
      - run: npm install
      - run: npm ci
      - run: npx @ampt/cli deploy staging
