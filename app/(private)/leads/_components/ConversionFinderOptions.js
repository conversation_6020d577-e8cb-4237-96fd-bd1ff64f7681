import * as React from 'react';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
import Radio from '@mui/joy/Radio';
import RadioGroup from '@mui/joy/RadioGroup';
import {Box} from "@mui/joy";

export default function ConversionFinderOptions({onChange}) {
    return (
        <Box sx={{marginTop: '20px', width:'220px'}}>
            <RadioGroup name="conversionFinderType" defaultValue="REVENUE">
                <List
                    sx={{
                        minWidth: 240,
                        width:'100%',
                        '--List-gap': '0.5rem',
                        '--ListItem-paddingY': '1rem',
                        '--ListItem-radius': '8px',
                        '--ListItemDecorator-size': '32px',
                    }}
                >
                    {['REVENUE', 'EXPENSE'].map((item) => (
                        <ListItem variant="outlined" key={item} sx={{boxShadow: 'sm', width:'220px'}}>
                            <Radio
                                overlay
                                value={item}
                                label={item}
                                onChange={(event) => onChange(event.target.value)}
                                sx={{flexGrow: 1, flexDirection: 'row-reverse'}}
                                slotProps={{
                                    action: ({checked}) => ({
                                        sx: (theme) => ({
                                            ...(checked && {
                                                inset: -1,
                                                border: '2px solid',
                                                borderColor: theme.vars.palette.primary[500],
                                            }),
                                        }),
                                    }),
                                }}
                            />
                        </ListItem>
                    ))}
                </List>
            </RadioGroup>
        </Box>
    );
}
