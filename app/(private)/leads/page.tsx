import React from "react";
import {getInitialConfigurationPage} from "@/app/_lib/utils/getInitialConfigurationPage";
import {TabsReportTableProvider} from "@/app/_components/reports/TabsReportTableProvider";

export default async function LeadsPage() {
    const tableName = 'leads';
    let {globalSearchConfig, tableConfig, defaultFilters} = await getInitialConfigurationPage(tableName);
    tableConfig = {...tableConfig, sort: {"date_created": -1}}

    return (
        <TabsReportTableProvider
            tableName={tableName}
            globalSearchConfig={globalSearchConfig}
            tableConfig={tableConfig}
            defaultFilters={defaultFilters}/>
    );
}
