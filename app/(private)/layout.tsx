"use client";
import "react-toastify/dist/ReactToastify.min.css";
import * as React from "react";
import { CssVarsProvider } from "@mui/joy/styles";
import CssBaseline from "@mui/joy/CssBaseline";
import Box from "@mui/joy/Box";
import Sidebar from "../_components/layout/Sidebar";
import Header from "../_components/layout/Header";
import { ConversionFinderLoading } from "@/app/_components/Loading/ConversionFinderLoading";
import { ToastContainer } from "react-toastify";
import { useSession } from "@/app/_lib/auth/auth-client";

const LoginLayout: React.FC<any> = ({ children }) => {
  const { data, isPending } = useSession();
  return (
    <CssVarsProvider disableTransitionOnChange>
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
      <ToastContainer />
      <ConversionFinderLoading />
      <CssBaseline />
      <Box sx={{ display: "flex", minHeight: "100dvh" }}>
        {!isPending && (
          <>
            <Header user={data?.user} />
            <Sidebar user={data?.user} />
          </>
        )}
        <Box
          component="main"
          className="MainContent"
          sx={{
            p: 2,
            flex: 1,
            display: "flex",
            flexDirection: "column",
            minWidth: 0,
            height: "100dvh",
            gap: 1,
          }}
        >
          <Box sx={{ overflowY: "auto", height: "100%" }}>{children}</Box>
        </Box>
      </Box>
    </CssVarsProvider>
  );
};

export default LoginLayout;
