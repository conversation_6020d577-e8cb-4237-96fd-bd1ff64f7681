"use client"
import React, {useEffect, useState} from "react";
import Typography from "@mui/joy/Typography";
import {TableMongoProvider} from "@/app/_components/mongo/TableMongoProvider";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import {settingsOnRouting} from "@/app/_components/utils/settingsOnRouting";
import {Tools} from "@/app/_components/tools/Tools";

export const VendorsProvider: React.FC<any> = ({
                                                   globalSearchConfig,
                                                   tableColumns,
                                                   dataTable,
                                                   tableName,
                                                   count,
                                                   tableConfig,
                                                   defaultFilters,
                                                   fullTableConfiguration
                                               }) => {
    const {setFiltersEntity, setDefaultFilters} = createTableEntities(tableName);
    const [showTable, setShowTable] = useState(false);

    useEffect(() => {
        settingsOnRouting(tableName, defaultFilters, setFiltersEntity, setDefaultFilters);
        setShowTable(true);
    }, []);

    return (
        <>
            <div className="flex justify-center mt-3">
                <Typography level="h2" fontSize="xl" sx={{mb: 0.5}}>
                    Vendors
                </Typography>
            </div>
            <Tools tableConfig={tableConfig} additionalTools={[]} hasTitle={true} tableColumns={tableColumns}/>
            {showTable && <TableMongoProvider
                globalSearchConfig={globalSearchConfig}
                tableColumns={tableColumns}
                dataTable={JSON.parse(dataTable)}
                dbTableName={tableName}
                count={count}
                tableDetails={tableConfig}
                defaultFilters={defaultFilters}
                fullTableConfiguration={fullTableConfiguration}
                dbName={'conversion_finder'}
            />}
        </>
    );
};
