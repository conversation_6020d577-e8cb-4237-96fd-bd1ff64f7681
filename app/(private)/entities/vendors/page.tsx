import React from "react";
import {getInitialConfigurationPage} from "@/app/_lib/utils/getInitialConfigurationPage";
import { VendorsProvider } from "./_components/VendorsProvider";

export default async function VendorsPage() {
    const tableName = 'vendors';
    let {
        globalSearchConfig,
        tableConfig,
        defaultFilters,
        tableConfiguration
    } = await getInitialConfigurationPage(tableName)
    tableConfig = {...tableConfig, sort: {"vendor": 1}}


    return (<>
        <VendorsProvider
            globalSearchConfig={globalSearchConfig}
            tableColumns={tableConfig.tableColumns}
            dataTable={JSON.stringify(tableConfig.data)}
            tableName={tableName}
            count={tableConfig.count}
            tableConfig={tableConfig}
            fullTableConfiguration={tableConfiguration}
        />
    </>)
}
