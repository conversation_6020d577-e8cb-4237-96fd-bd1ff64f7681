import React, {Suspense} from "react";
import {QuickFormProvider} from "@/app/(private)/quickForm/_components/QuickFormProvider";
import Box from "@mui/joy/Box";
import Typography from "@mui/joy/Typography";

export default async function QuickFormPage() {
    return (
        <Suspense fallback={
            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh'}}>
                <Typography>Loading...</Typography>
            </Box>
        }>
            <QuickFormProvider/>
        </Suspense>
    )
}
