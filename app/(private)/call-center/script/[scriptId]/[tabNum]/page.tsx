import React from "react";
import {ConfigurationTabs} from "@/app/_components/callScriptsConf/ConfigurationTabs";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";
import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect";

export default async function Page({params}) {
    const {db} = await callCenterConnect();
    const user: any = await getUserSession()

    const {scriptId, tabNum} = params;
    const {metadata, id, role, dev} = user || {metadata: null, id: null};
    const jsonMetadata = metadata ? JSON.parse(metadata) : null;
    const allMetadata = {
        metadata: jsonMetadata,
        id: id,
        role: role,
        dev: dev
    }

    const getDataConfLeads = await db.collection('configuration').findOne({configuration: 'table_leads'})

    return (
        <ConfigurationTabs
            userMetadata={allMetadata}
            callCampaignId={scriptId}
            confCallLeads={getDataConfLeads?.data}
            tabNum={tabNum}
            userId={id}
        />
    )
}
