import { getInitialConfigurationPage } from "@/app/_lib/utils/getInitialConfigurationPage";
import React from "react";
import {ScriptsProvider} from "@/app/(private)/call-center/script/_components/scriptsProvider";

export default async function ScriptsPage() {
    const tableName = 'campaigns';
    let { globalSearchConfig, tableConfig, defaultFilters } = await getInitialConfigurationPage('call-campaigns');

    tableConfig = { ...tableConfig, sort: { "name": 1 } };
    defaultFilters = {
        ...defaultFilters,
        'schemaVersion-eq': { value: 2, filterType: 'eq', label: 'schemaVersion' }
    };

    return (
        <ScriptsProvider
            globalSearchConfig={globalSearchConfig}
            tableColumns={tableConfig.tableColumns}
            dataTable={JSON.stringify(tableConfig.data)}
            tableName={tableName}
            count={tableConfig.count}
            tableConfig={tableConfig}
            defaultFilters={defaultFilters}
        />
    );
}
