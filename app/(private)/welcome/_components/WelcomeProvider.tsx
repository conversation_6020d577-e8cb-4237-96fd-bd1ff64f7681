"use client"
import type React from "react"
import {useEffect, useState} from "react"
import Image from 'next/image';
import Box from "@mui/joy/Box"
import {Card, CardContent, Grid, Typography} from "@mui/joy"
import {setLoading} from "@/app/_components/Loading/loadingState"
import Button from "@mui/joy/Button"
import DialogTitle from "@mui/joy/DialogTitle"
import {useRouter} from "next/navigation"
import {useQuery} from "@tanstack/react-query"
import {menuIcons} from "@/app/_components/utils/menuIcons"
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage"
import _ from "lodash";
import ReplayIcon from '@mui/icons-material/Replay';
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
interface MenuItem {
  route: string
  label: string
  subRoutes?: MenuItem[]
  subitems?: MenuItem[]
}
export const WelcomeProvider: React.FC<any> = ({user}) => {
  const router = useRouter()
  const [mailSent, setMailSent] = useState(false);
  useEffect(() => {
    setLoading(false)
  }, [])
  // Verificar si el stripeClient está funcionando
  // useEffect(() => {
  //   const checkStripeClient = async () => {
  //     try {
  //       const response = await fetch('/api/betterAuth/getStripeService', {
  //         method: 'GET',
  //         headers: {
  //           'Content-Type': 'application/json',
  //         },
  //       });
  //       const data = await response.json();
  //       console.log(':white_check_mark: Welcome: Stripe client is working:', data);
  //     } catch (error) {
  //       console.error(':x: Welcome: Error checking Stripe client:', error);
  //     }
  //   };
  //   checkStripeClient();
  // }, []);
  const getMenu = async () => {
    const urlMenu = "/api/public/permissions/menu/user"
    const menuResponse = await fetch(urlMenu, {
      method: "GET",
    })
    return await menuResponse.json()
  }
  const {
    data: menus,
    error,
    isFetching,
  } = useQuery({
    queryKey: [`menu-user-welcome`],
    queryFn: getMenu,
    refetchOnWindowFocus: false,
    enabled: true,
    retry: 3,
  })
  const handleNavigation = (route: string) => {
    setLoading(true)
    router.push(route)
  }
  const sendSupportEmail = async () => {
    setLoading(true)
    const response = await fetch("/api/betterAuth/sendMailRequestAccess", {
      method: 'POST',
      body: JSON.stringify({url: "/welcome"}),
    });
    if (response.ok) {
      showSuccess('Mail Send, please wait for response');
    } else {
      showError("Something wrong happened, please retry later")
    }
    setMailSent(true)
    setLoading(false)
  }
  const handleRedirect = () => {
    setLoading(true);
    router.push("/");
  };
  const renderGroupedOptionsWithCards = (menuItems: any[]) => {
    const singleItems = menuItems.filter((item) => !item.subRoutes || item.subRoutes.length === 0)
    const groupItems = menuItems.filter((item) => item.subRoutes && item.subRoutes.length > 0)
    return (
        <>
          {singleItems.length > 0 && (
              <Grid container spacing={2} sx={{mb: 3}}>
                {singleItems.map((item, index) => (
                    <Grid
                        xs={12} sm={4} key={`single-${item.route}-${index}`}
                        sx={{display: "flex", justifyContent: "center"}}
                    >
                      <Button
                          fullWidth
                          variant="soft"
                          color="neutral"
                          startDecorator={menuIcons[item.route] || null}
                          onClick={() => handleNavigation(item.route)}
                          sx={{
                            width: "250px",
                            padding: 2,
                            borderRadius: 4,
                            fontWeight: "bold",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                          }}
                      >
                        {item.label}
                      </Button>
                    </Grid>
                ))}
              </Grid>
          )}
          {groupItems.map((item, index) => (
              <Card key={`card-group-${item.route}-${index}`} sx={{mb: 3}}>
                <CardContent>
                  <Box sx={{display: "flex", alignItems: "center", gap: 1, mb: 2}}>
                    {menuIcons[item.route] || null}
                    <Typography level="title-lg">{item.label}</Typography>
                  </Box>
                  <Grid container spacing={2}>
                    {item.subRoutes?.map((subItem: any, subIndex: number) => (
                        <Grid sx={{display: "flex", justifyContent: "center"}} xs={12} sm={4}
                              key={`card-submenu-${subItem.route}-${subIndex}`}>
                          <Button
                              fullWidth
                              variant="soft"
                              color="neutral"
                              startDecorator={menuIcons[subItem.route] || null}
                              onClick={() => handleNavigation(subItem.route)}
                              sx={{
                                padding: 2,
                                width: "250px",
                                borderRadius: 4,
                                fontWeight: "bold",
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                              }}
                          >
                            {subItem.label}
                          </Button>
                        </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
          ))}
        </>
    )
  }
  function groupRoutes(data: MenuItem[]): Array<{
    route: string;
    label: string;
    subRoutes?: { route: string; label: string }[]
  }> {
    return _.map(data, (item: MenuItem) => {
      if (_.has(item, 'subitems')) {
        return {
          route: item.route,
          label: item.label,
          subRoutes: _.map(item.subitems, (sub: MenuItem) => ({
            route: sub.route,
            label: sub.label
          }))
        };
      }
      return {
        route: item.route,
        label: item.label
      };
    });
  }
  return (
      <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "100vh",
            padding: 2,
            marginTop: {xs: 2, sm: 1},
            flexDirection: "column",
          }}>
        <Image
            src="/android-chrome-192x192.png"
            alt="Logo"
            width={48}
            height={48}
            style={{objectFit: 'contain'}}
            priority
        />
        <Typography level={"h2"}>AurionX</Typography>
        {/*{(user?.role === 'admin' && user?.dev) && <TestDBConnection/>}*/}
        <Box
            sx={{
              width: "100%",
              maxWidth: 1000,
              padding: 3,
              backgroundColor: "#ffffff",
              borderRadius: 8,
              boxShadow: "0 4px 10px rgba(0, 0, 0, 0.1)",
              marginTop: 2,
            }}
        >
          {isFetching ? (
              <LoadingMessage message="Loading menu..."/>
          ) : (
              <>
                <DialogTitle sx={{display: "flex", justifyContent: "center", marginBottom: 3}}>
                  Available options
                </DialogTitle>
                {(menus && menus.length > 0) ?
                    renderGroupedOptionsWithCards(groupRoutes(menus)) :
                    <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          padding: 2,
                          marginTop: {xs: 2, sm: 1},
                          flexDirection: 'column',
                        }}>
                      <Typography level="h4" mb={2}>
                        No Access
                      </Typography>
                      <Typography level="body-lg" mb={3}>
                        It looks like you dont have any role assigned.
                      </Typography>
                      <Typography level="body-lg" mb={3}>
                        An email will be sent to our support team to grant you access.
                      </Typography>
                      <Button onClick={sendSupportEmail} disabled={mailSent} variant="soft" color='neutral'>
                        {(mailSent) ? "Mail sent" : "Send Email to Support"}
                      </Button>
                      {!mailSent &&
                          <Typography level="body-sm" mt={3}>
                            If you already sent the email, please wait for the response.
                          </Typography>}
                      {mailSent && <>
                        <Typography level="body-sm" mt={3} mb={3}>
                          If you confirm the permissions already sets, you can reload the page.
                        </Typography>
                        <Button onClick={handleRedirect} variant="soft" color='neutral'>
                          <ReplayIcon/>
                        </Button>
                      </>}
                    </Box>}
              </>
          )}
        </Box>
      </Box>
  )
}
