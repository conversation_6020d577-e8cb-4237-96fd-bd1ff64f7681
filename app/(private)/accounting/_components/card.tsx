import * as React from 'react';
import Card from '@mui/joy/Card';
import CardContent from '@mui/joy/CardContent';
import Typography from '@mui/joy/Typography';
import {Box} from "@mui/material";

const AccountingCard: React.FC<any> = ({sx, icon, label, quantity}) => {
    return (
        <Card variant="solid"  sx={{
            ...sx,
            backgroundColor: 'rgb(255, 255, 255)',
            color: 'rgb(33, 43, 54)',
            transition: 'box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms',
            boxShadow: 'rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px -4px',
            borderRadius: '16px',
            alignItems: 'center',
            padding: '24px'
        }}>
            <CardContent orientation="horizontal" sx={{width: '100%'}}>
                <Box sx={{display: 'flex', justifyContent: 'space-around', width: '100%', alignItems:'center'}}>
                    <Box>
                        <Typography level="h4">{label}</Typography>
                        <Typography level="h2">{quantity}</Typography>
                    </Box>
                    <Box>
                        {icon}
                    </Box>
                </Box>
            </CardContent>
        </Card>
    );
}
export default AccountingCard;
