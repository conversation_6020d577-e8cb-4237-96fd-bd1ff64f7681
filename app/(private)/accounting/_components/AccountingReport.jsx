'use client'
import React, {useState} from "react";
import {Box, Grid, Table, Tooltip} from "@mui/joy"
import Typography from "@mui/joy/Typography";
import ISOWeekFilter from "@/app/_components/filters/IsoWeeKFilter";
import {useQueries} from "@tanstack/react-query";
import SelectFilterComponent from "@/app/_components/table/filters/SelectFilterComponent";
import {formatNumber} from "@/app/_lib/utils/formatNumber.js";
import Loading from "react-loading";
import _ from "lodash";
import AccountingCheckFilter from "@/app/_components/accounting/AccountingCheckFilter.jsx";
import {OptionsAccountingOpts} from "@/app/_components/queries/OptionsAccountingOpts";
import {AccReportData} from "@/app/_components/queries/AccReportData";
import {useEntity} from "simpler-state";
import {
    accountingReportFiltersEntity,
    setAccountingReportFiltersEntity
} from "@/app/_components/table/states/accountingReportsState.js";
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import {useRouter} from "next/navigation";
import {getISOWeeksInMonth} from "@/app/_components/utils/getISOWeeksInMonth.js";
import {
    setAccountingInvoiceIds,
    setAccountingInvoiceLabels
} from "@/app/_lib/_states/accounting/accountingReportInvoices.js";
import {DebugFilters} from "@/app/_components/debug/DebugFilters";
import {cleanAccountingInvoiceFilter} from "@/app/_lib/utils/filters/index.js";

const ACCOUNTING_TYPE = {REVENUE: 'REVENUE', EXPENSE: 'EXPENSE'}
const COLLECTIONS = {Leads: 'leads', Transfers: 'transfers', Postbacks: 'postbacks'}
const tableBodyStyles = {
    '--TableCell-headBackground': 'var(--joy-palette-background-level1)',
    '--Table-headerUnderlineThickness': '1px',
    '--TableRow-hoverBackground': 'var(--joy-palette-background-level1)',
    '--TableCell-paddingY': '4px',
    '--TableCell-paddingX': '8px',
    minWidth: '800px'
}

const LoadingComponent = () => {
    return <Box className="accounting-loading-container"
                sx={{display: 'flex', justifyContent: 'center', marginTop: '50px'}}>
        <Loading color={'black'} width={'30px'} height={'30px'} type={'spin'}/>
    </Box>
}
export default function AccountingReport() {
    const [refreshLabels, setRefreshLabels] = useState(0);
    const accountingReportFilters = useEntity(accountingReportFiltersEntity);
    const router = useRouter();
    const changeFilter = (filterValue, filterProperty) => {
        const accountingFilters = {...accountingReportFilters.accountingFilters, [filterProperty]: {$in: filterValue}};
        setAccountingReportFiltersEntity({...accountingReportFilters, accountingFilters: accountingFilters})
    }

    const changeRangeDateFilter = (dates) => {
        const year = dates.year;
        const week = dates.week;
        const filters = {...accountingReportFilters.accountingFilters, year, week: {$in: week}};
        setAccountingReportFiltersEntity({...accountingReportFilters, accountingFilters: filters, month: dates.month})
    }

    const getLabelFilter = (key) => {
        const labels = {
            campaign_key: "Campaign Key",
            pub_id: 'Pubid',
            vendor: 'Vendor',
            client: 'Client',
            sub_id: 'Subid',
            label: 'Accounting Label'
        }
        return labels[key];
    }

    const applyFilterColumn = (value, key) => {
        const keyMapping = {
            vendor: 'vendor_id',
            client: 'client_id',
            label: 'labelAmount',
        };

        const filterProperty = keyMapping[key] || key;
        let filters = {
            ...accountingReportFilters.accountingFilters,
            [filterProperty]: value === 'NULL' ? null : value
        };
        if (value === 'NA_FILTER') {
            filters = _.omit(filters, filterProperty);
        }
        setAccountingReportFiltersEntity({...accountingReportFilters, accountingFilters: filters})
    };

    const getAccountingForDefaultValue = () => {
        const sourceTable = accountingReportFilters.accountingFilters.source_table.$in;
        return _.keys(_.pickBy(COLLECTIONS, (value) => _.includes(sourceTable, value)));
    }

    const getAccountingTypeDefaultValue = () => {
        const accountingType = accountingReportFilters.accountingFilters.type.$in;
        const ACCOUNTING_TYPE = {Revenue: 'REVENUE', Expense: 'EXPENSE'}
        return _.keys(_.pickBy(ACCOUNTING_TYPE, (value) => _.includes(accountingType, value)));
    }

    const getDefaultIsoWeekDateFilter = () => {
        return {
            weekNumber: accountingReportFilters.accountingFilters.week.$in,
            year: accountingReportFilters.accountingFilters.year,
            week: accountingReportFilters.accountingFilters.week.$in[0],
            month: accountingReportFilters.month
        }
    }

    const getDefaultValue = (key) => {
        const selectFilters = {vendor: 'vendor_id', label: 'labelAmount', client: 'client_id'}
        const keyFilter = selectFilters[key] ? selectFilters[key] : key;
        return accountingReportFilters.accountingFilters[keyFilter];
    }

    const goToInvoices = (item) => {
        const {accountingFilters} = accountingReportFilters
        const months = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ]
        const weeks = getISOWeeksInMonth(accountingFilters.year, accountingReportFilters.month)
        const weekFilter = _.find(weeks, (week) => week[0] === accountingFilters.week.$in[0]);
        const labelWeek = accountingFilters.week.$in.length > 1 ? `${months[accountingReportFilters.month]} all weeks` : `${weekFilter[1]}`
        let label = `year ${accountingFilters.year} ${labelWeek}`;
        if (_.has(accountingFilters, 'client_id')) {
            const client = optionSelectValues['client'].find(client => client.value === accountingFilters['client_id']);
            label = label + `client: ${client.label}`
        }
        if (_.has(accountingFilters, 'campaign_key')) {
            label = label + `campaign key: ${accountingFilters['campaign_key']}`
        }
        if (_.has(accountingFilters, 'pub_id')) {
            label = label + `pubid: ${accountingFilters['pub_id']}`
        }
        cleanAccountingInvoiceFilter()
        setAccountingInvoiceLabels(`${item.source_table}-${item.labelAmount}-${item.source_table} ${label}`)
        setAccountingInvoiceIds(item.invoice_ids);
        router.push(`/accounting/invoices`);

    }

    const [
        {data: optionSelectValues, isLoading: selectsIsLoading},
        {data: accountingReportData, isLoading: accountingIsLoading}
    ] = useQueries({
        queries: [
            OptionsAccountingOpts(refreshLabels, accountingReportFilters.accountingType, accountingReportFilters.collection, true),
            AccReportData(accountingReportFilters.accountingFilters)
        ]
    });

    return <>
        <Box>
            <DebugFilters filters={accountingReportFilters}/>
            <Box sx={{display: 'flex', flexDirection: 'column', height: {md: '85vh', sm: '100%'}}}>
                <Box sx={{textAlign: 'center', mb: 2}}>
                    <Typography level="h3">
                        Accounting Report
                    </Typography>
                </Box>
                {selectsIsLoading ?
                    <LoadingComponent/>
                    : <Box>
                        <Box className="accounting-summary-container p-5">
                            <Grid className="accounting-filters-container">
                                <Grid container spacing={4} sx={{display: 'flex', alignItems: 'center'}}>

                                    <Grid className="accounting-type-container" sx={{width: '250px'}}>
                                        <Typography level="body-md" fontWeight="lg" sx={{marginBottom: '5px'}}>
                                            Accounting type
                                        </Typography>
                                        <AccountingCheckFilter options={['Revenue', 'Expense']}
                                                               defaultValues={getAccountingTypeDefaultValue()}
                                                               onChange={(event) => changeFilter(_.map(event, _.toUpper), 'type')}
                                        />
                                    </Grid>
                                    <Grid className="accounting-type-container">
                                        <Typography level="body-md" fontWeight="lg" sx={{marginBottom: '5px'}}>
                                            Accounting for
                                        </Typography>
                                        <AccountingCheckFilter options={['Leads', 'Transfers', 'Postbacks']}
                                                               defaultValues={getAccountingForDefaultValue()}
                                                               onChange={(event) => changeFilter(_.map(event, _.toLower), 'source_table')}

                                        />
                                    </Grid>
                                    <Grid className="date-filter">
                                        <Typography level="body-md" fontWeight="lg" sx={{marginBottom: '5px'}}>
                                            Date
                                        </Typography>
                                        <ISOWeekFilter onFilter={changeRangeDateFilter} showApplyFiltersButton={false}
                                                       defaultIsoWeek={getDefaultIsoWeekDateFilter()}
                                                       showAllWeeks={true}
                                                       destroyComponent={() => {
                                                       }}
                                                       showAllOnChange={true}
                                        />
                                    </Grid>
                                    {optionSelectValues && Object.entries(optionSelectValues).map(([key, value]) => (
                                        <Grid key={`filter-${key}`} className="accounting-type-container"
                                              size={{sm: 3, xs: 12}}
                                              sx={{}}>
                                            <Typography level="body-md" fontWeight="lg" sx={{marginBottom: '5px'}}>
                                                {getLabelFilter(key)}
                                            </Typography>
                                            <SelectFilterComponent columnFilterValue={getDefaultValue(key)}
                                                                   applyFilterColumn={(event) => applyFilterColumn(event, key)}
                                                                   column={{columnDef: {meta: {config: {selectOptions: optionSelectValues[key] || []}}}}}
                                                                   dbTableName={""}></SelectFilterComponent>
                                        </Grid>
                                    ))}
                                </Grid>
                            </Grid>
                        </Box>
                        {accountingIsLoading && !selectsIsLoading ? <LoadingComponent/>
                            : <Box sx={{overflow: 'auto', flexGrow: 1}} className="accounting-summary-table mt-5">
                                <Table stickyHeader sx={tableBodyStyles}>
                                    <thead className="bg-gray-50 sticky top-0 z-10">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            style={{width: '15%'}}>
                                            Accounting Label
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            style={{width: '15%'}}>
                                            Accounting For
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            style={{width: '15%'}}>
                                            Accounting Type
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            style={{width: '15%'}}>
                                            Total Items
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            style={{width: '15%'}}>
                                            Total Amount
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            style={{width: '15%'}}>
                                            Detail
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                    {accountingReportData && accountingReportData.map((item, index) => (
                                        <tr key={`accounting-table-${index}`}>
                                            <td style={{verticalAlign: 'top'}}>
                                                {item.labelAmount}
                                            </td>
                                            <td style={{verticalAlign: 'top'}}>
                                                {item.source_table}
                                            </td>
                                            <td style={{verticalAlign: 'top'}}>
                                                {item.type}
                                            </td>
                                            <td style={{verticalAlign: 'top'}}>
                                                {formatNumber(item.items)}
                                            </td>
                                            <td style={{verticalAlign: 'top'}}>
                                                ${formatNumber(item.amount / 100)}
                                            </td>
                                            <td style={{verticalAlign: 'top'}}>
                                                <Tooltip title={'Go to invoices'} variant="soft"
                                                         placement="bottom">
                                                    <RemoveRedEyeIcon style={{cursor: 'pointer'}}
                                                                      onClick={() => goToInvoices(item)}/>
                                                </Tooltip>
                                            </td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </Table>
                            </Box>
                        }
                    </Box>}
            </Box>
        </Box>
    </>
}
