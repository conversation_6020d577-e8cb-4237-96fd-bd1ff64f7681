import {Box, Button} from "@mui/joy";
import React from "react";
import Modal from '@mui/joy/Modal';
import ModalClose from '@mui/joy/ModalClose';
import Sheet from '@mui/joy/Sheet';
import Typography from '@mui/joy/Typography';
import Input from "@mui/joy/Input";
import FormControl from "@mui/joy/FormControl";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import Decimal from "decimal.js";
import {setLoading} from "@/app/_components/Loading/loadingState";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {InputReactNumberFormat} from "@/app/_components/inputReactNumberFormat/inputReactNumberFormat";

export const NewRvnExpComponent: React.FC<any> = ({dbTableName, totalEventsWithoutRvnE}) => {
    const [openRevModal, setOpenRevModal] = React.useState<boolean>(false);
    const [amount, setAmount] = React.useState<any>(0);
    const [clientAmount, setClientAmount] = React.useState<any>(0);
    const [invoiceLabel, setInvoiceLabel] = React.useState<any>('');
    const {filtersEntity} = createTableEntities(dbTableName);

    const getAmountForEvent = () => {
        const amountEachEvent = amount > 0 ? amount / totalEventsWithoutRvnE : 0;
        return new Decimal(amountEachEvent).toNumber().toFixed(2)
    }

    const onCloseModal = () => {
        setOpenRevModal(false);
        setAmount(0)
        setInvoiceLabel('')
        setClientAmount(0)
    }

    const applyRvnExp = () => {
        setOpenRevModal(false);
        setLoading(true)
        let amountEachEvent = amount / totalEventsWithoutRvnE;
        const decimalAmountEachEvent = new Decimal(amountEachEvent).toNumber().toFixed(2)
        const payload = {
            filters: filtersEntity.get(),
            amount,
            clientAmount,
            amountPerEvent: Number.parseFloat(decimalAmountEachEvent),
            totalEvents: totalEventsWithoutRvnE,
            label: invoiceLabel
        }

        fetch("/api/mongo/applyNewRvnExp", {
            method: 'POST',
            body: JSON.stringify(payload),
        }).then(res => {
            setLoading(false)
            showSuccess(`$${amount} were applied to ${totalEventsWithoutRvnE} events`)
            setAmount(0)
            setInvoiceLabel('');
        }).catch(error => {
            setLoading(false);
            console.error(error);
            showError(`Error to apply Rvn/Exp`)
        });
    }

    const onChangeAmount = (event: any) => {
        if (event && event != '') {
            setAmount(Number.parseFloat(event))
        } else {
            setAmount('')
        }
    }

    const onChangeClientAmount = (event: any) => {
        if (event && event != '') {
            setClientAmount(Number.parseFloat(event))
        } else {
            setClientAmount('')
        }
    }
    return <>
        <Box>
            <Button variant='outlined' color='neutral' sx={{height: '30px'}} onClick={() => setOpenRevModal(true)}>New
                Rvn/Exp To {totalEventsWithoutRvnE} events</Button>
        </Box>
        <Box>
            <Modal
                aria-labelledby="modal-title"
                aria-describedby="modal-desc"
                open={openRevModal}
                onClose={() => setOpenRevModal(false)}
                sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}
            >
                <Sheet
                    variant="outlined"
                    sx={{
                        maxWidth: 500,
                        width: '100%',
                        borderRadius: 'md',
                        p: 5,
                        boxShadow: 'lg',
                    }}
                >
                    <ModalClose onClick={onCloseModal} variant="plain" sx={{m: 1}}/>
                    <Typography
                        component="h2"
                        id="modal-title"
                        level="h4"
                        textColor="inherit"
                        fontWeight="lg"
                        mb={1}
                        mt={2}
                    >
                        New Rev/Exp
                    </Typography>
                    <FormControl sx={{flex: 1}} size="lg">
                        <Typography level="body-lg">
                            Invoice
                        </Typography>
                        <Input
                            size="lg"
                            type='text'
                            placeholder="Label"
                            value={invoiceLabel}
                            onChange={(event) => setInvoiceLabel(event.target.value)}
                        />
                    </FormControl>
                    <FormControl sx={{flex: 1}} size="lg">
                        <Typography level="body-lg" sx={{marginTop: '20px'}}>
                            Amount
                        </Typography>
                        <InputReactNumberFormat value={amount} changeValue={onChangeAmount}/>
                    </FormControl>

                    <FormControl sx={{flex: 1}} size="lg">
                        <Typography level="body-lg" sx={{marginTop: '20px'}}>
                            Client Amount
                        </Typography>
                        <InputReactNumberFormat value={clientAmount} changeValue={onChangeClientAmount}/>
                    </FormControl>

                    {invoiceLabel && invoiceLabel != '' && typeof amount != 'string' ?
                        <>
                            <Box sx={{marginTop: '20px'}}>
                                <Typography level="body-lg">
                                    Do you want apply <span style={{fontWeight: 'bold'}}>${amount}</span> to <span
                                    style={{fontWeight: 'bold'}}>{totalEventsWithoutRvnE}</span> events
                                </Typography>
                                <Typography sx={{marginTop: '10px'}} level="body-lg">
                                    <span style={{fontWeight: 'bold'}}>${getAmountForEvent()}</span> for each lead
                                </Typography>

                            </Box>
                            <Box sx={{textAlign: 'right', marginTop: '10px'}}>
                                <Button variant='outlined' onClick={onCloseModal} color='danger'
                                        sx={{height: '30px'}}>Cancel</Button>
                                <Button variant='outlined' color='neutral'
                                        disabled={totalEventsWithoutRvnE <= 0}
                                        sx={{height: '30px', marginLeft: '10px'}} onClick={applyRvnExp}>Apply</Button>
                            </Box>
                        </>
                        : null
                    }
                </Sheet>
            </Modal>
        </Box>
    </>
}
