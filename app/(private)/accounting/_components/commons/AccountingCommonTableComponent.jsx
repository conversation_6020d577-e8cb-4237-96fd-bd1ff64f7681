"use client"

import React, {Fragment} from "react"
import Box from "@mui/joy/Box";
import Typography from "@mui/joy/Typography";
import {Table} from "@mui/joy";
import {formatCurrency} from "@/app/_lib/utils/formatCurrency.js";

const tableBodyStyles = {
    '--TableCell-headBackground': 'var(--joy-palette-background-level1)',
    '--Table-headerUnderlineThickness': '1px',
    '--TableRow-hoverBackground': 'var(--joy-palette-background-level1)',
    '--TableCell-paddingY': '4px',
    '--TableCell-paddingX': '8px',
    minWidth: '1200px',
    marginBottom:'50px'
}


export default function AccountingCommonTableComponent({
                                                           data = [],
                                                           loading = false,
                                                           availableLabels = [],
                                                           onChangeAccountingSelect
                                                       }) {

    const calculateClientNetBalance = () => {
        const balanceMap = {}
        data.forEach((typeData) => {
            const multiplier = typeData.type === "REVENUE" ? 1 : -1
            typeData.accountingItems.forEach((client) => {
                if (!balanceMap[client.row_labels]) {
                    balanceMap[client.row_labels] = availableLabels.reduce((acc, col) => {
                        acc[col] = 0
                        return acc
                    }, {})
                }

                availableLabels.forEach((col) => {
                    balanceMap[client.row_labels][col] += (Number(client[col]) || 0) * multiplier
                })
            })
        })

        return balanceMap
    }



    const sortedData = [...data].sort((a, b) => {
        if (a.type === "REVENUE" && b.type === "EXPENSE") return -1
        if (a.type === "EXPENSE" && b.type === "REVENUE") return 1
        return a.type.localeCompare(b.type)
    })

    const clientNetBalance = calculateClientNetBalance()

    function getLabel(column) {
        if (column === "grand_total") {
            return "GRAND TOTAL"
        }
        return column.toUpperCase();
    }

    return (
        <Box sx={{margin: "0 auto"}}>
            {sortedData && sortedData.length > 0 ?
                <Table stickyHeader sx={tableBodyStyles}>
                    <thead className="bg-gray-50 sticky top-0 z-10">
                    <tr>
                        <th style={{width: "200px", padding: "12px 16px", textAlign: "left"}}
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ROW
                            LABELS
                        </th>
                        {availableLabels.map((column, index) => (
                            <th key={`column-${column}-${index}`} style={{padding: "12px 16px", textAlign: "left"}}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {getLabel(column)}
                            </th>
                        ))}
                    </tr>
                    </thead>
                    <tbody>
                    {sortedData.map((typeData) => (
                        <Fragment key={`box-${typeData.type}-index`}>
                            <tr key={typeData.type}>
                                <td
                                    colSpan={availableLabels.length + 1}
                                    style={{padding: "12px 16px", backgroundColor: "#f5f5f5"}}
                                >
                                    <Typography level="title-md">{typeData.type}</Typography>
                                </td>
                            </tr>
                            {typeData?.accountingItems
                                .sort((a, b) => a.row_labels.localeCompare(b.row_labels))
                                .map((client, index) => (
                                    <tr key={`${typeData.type}-${client.row_labels}-${index}`}>
                                        <td style={{paddingLeft: "32px"}}>{client.row_labels}</td>
                                        {availableLabels.map((column) => (
                                            <td key={column}>${formatCurrency(client[column] / 100)}</td>
                                        ))}
                                    </tr>
                                ))}

                            <tr>
                                <td style={{padding: "12px 16px", backgroundColor: "#f0f0f0", fontWeight: 500}}>
                                    Subtotal {typeData.type}
                                </td>
                                {availableLabels.map((column) => {
                                    const total = typeData.accountingItems.reduce((sum, client) => sum + (Number(client[column]) || 0), 0)
                                    return (
                                        <td key={column} style={{backgroundColor: "#f0f0f0", fontWeight: 500}}>
                                            ${formatCurrency(total / 100)}
                                        </td>
                                    )
                                })}
                            </tr>
                        </Fragment>
                    ))}
                    <tr>
                        <td
                            colSpan={availableLabels.length + 1}
                            style={{
                                padding: "16px",
                                backgroundColor: "#f5f5f5",
                                borderTop: "4px solid #fff",
                                fontWeight: 500,
                            }}
                        >
                            <Typography level="title-md">Balance by client (Revenue - Expense)</Typography>
                        </td>
                    </tr>

                    {Object.keys(clientNetBalance)
                        .sort()
                        .map((clientName) => {
                            const balance = clientNetBalance[clientName]
                            return (
                                <tr key={`net-${clientName}`}>
                                    <td style={{paddingLeft: "32px"}}>{clientName}</td>
                                    {availableLabels.map((column) => {
                                        const value = balance[column]
                                        const color = value > 0 ? "success" : value < 0 ? "danger" : undefined

                                        return (
                                            <td key={column}>
                                                <Typography color={color}
                                                            fontWeight={column === "grand_total" ? "md" : undefined}>
                                                    ${formatCurrency(value/100)}
                                                </Typography>
                                            </td>
                                        )
                                    })}
                                </tr>
                            )
                        })}
                    </tbody>
                </Table>
                : <Typography level="title-md" sx={{textAlign: 'center'}}>
                    No data found
                </Typography>
            }
        </Box>
    )
}

