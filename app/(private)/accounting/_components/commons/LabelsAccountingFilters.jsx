import Box from "@mui/joy/Box";
import Checkbox from "@mui/joy/Checkbox";
import {useEffect, useState} from "react";
import {sortLabels} from "@/app/_lib/utils/accounting/sortLabels.js";

export const LabelsAccountingFilters = ({availableLabels, onChangeAccountingSelect}) => {

    const [availableColumns, setAvailableColumns] = useState(sortLabels(availableLabels))
    const [selectedColumns, setSelectedColumns] = useState(sortLabels(availableLabels))

    useEffect(() => {
        if (onChangeAccountingSelect) onChangeAccountingSelect(selectedColumns);
    }, [selectedColumns]);

    const handleColumnToggle = (column) => {
        setSelectedColumns((prev) => (prev.includes(column) ? prev.filter((col) => col !== column) : [...prev, column]))
    }

    const handleToggleAll = () => {
        setSelectedColumns(selectedColumns.length === availableColumns.length ? [] : [...availableColumns])
    }

    const getLabel = (column) => {
        if (column === "grand_total") return "GRAND TOTAL"
        return column.toUpperCase();
    }

    return <>
        <Box sx={{mt: 5}}>
            <Box sx={{display: "flex", flexWrap: "wrap", gap: 2, mt: 1}}>
                {availableColumns.map((column, index) => (
                    <Checkbox
                        key={`column-${column}-${index}`}
                        sx={{
                            ml: {
                                md: 2,
                                sm: 0
                            }
                        }}
                        label={getLabel(column)}
                        checked={selectedColumns.includes(column)}
                        onChange={() => handleColumnToggle(column)}
                    />
                ))}
            </Box>
        </Box>
    </>
}
