"use client"
import * as React from 'react';
import {useEffect, useState} from 'react';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import AccountingCard from "@/app/(private)/accounting/_components/card";
import LeaderboardIcon from "@mui/icons-material/Leaderboard";
import QueryStatsIcon from "@mui/icons-material/QueryStats";
import {formatCurrency} from "@/app/_lib/utils/formatCurrency";
import _ from "lodash";

const SideClientAccountingDetails: React.FC<any> = ({
                                                        totalEvents,
                                                        dataDashboard,
                                                        totalEventsWithoutRvn,
                                                        totalAccountingAmount,
                                                        getTotalEvents,
                                                        getEventsWithoutRvn,
                                                        goToAccounting,
                                                        accountingCardConfig
                                                    }) => {
    const [dashboardTotalEventsWithoutRvn, setDashboardTotalEventsWithoutRvn] = useState(0);
    const [dashboardTotalAccountingAmount, setDashboardTotalAccountingAmount] = useState(0);

    useEffect(() => {
        if (!_.isEmpty(dataDashboard)) {
            setDashboardTotalEventsWithoutRvn(dataDashboard?.totalEventsWithoutRvn ?? 0)
            setDashboardTotalAccountingAmount(dataDashboard?.totalAccountingAmount ?? 0)
        } else {
            setDashboardTotalEventsWithoutRvn(totalEventsWithoutRvn ?? 0)
            setDashboardTotalAccountingAmount(totalAccountingAmount ?? 0)
        }
    }, [dataDashboard]);

    const styles = {
        grid: {display: 'flex', justifyContent: 'center'},
        accountingCard: {width: '320px', cursor: 'pointer'},
        iconCard: {fontSize: '40px'}
    }

    return (
        <Box sx={{flexGrow: 1, marginTop: '20px'}}>
            <Grid container spacing={2} sx={{maxWidth: '1800px'}}>
                {accountingCardConfig && accountingCardConfig.showTotalEvents &&
                    <Grid item xs={12} md={6} lg={4} sx={styles.grid}>
                        <Box onClick={getTotalEvents}>
                            <AccountingCard icon={<LeaderboardIcon style={{...styles.iconCard, color: 'red'}}/>}
                                            sx={styles.accountingCard}
                                            label={'Total Events '} quantity={totalEvents}

                            />
                        </Box>
                    </Grid>
                }
                {accountingCardConfig && accountingCardConfig.totalAccountingAmount &&
                    <Grid item xs={12} md={6} lg={4} sx={styles.grid}>
                        <AccountingCard icon={<QueryStatsIcon style={{...styles.iconCard, color: 'green'}}/>}
                                        sx={{...styles.accountingCard, cursor: 'auto'}} label={'Total Amount'}
                                        quantity={`$ ${formatCurrency(dashboardTotalAccountingAmount ?? 0)}`}/>
                    </Grid>
                }
            </Grid>
        </Box>
    );
}

export default SideClientAccountingDetails;
