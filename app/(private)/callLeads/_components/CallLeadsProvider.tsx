"use client";
import React from "react";
import Typography from "@mui/joy/Typography";
import { FormControl, FormLabel, Input, Button } from "@mui/joy";
import { showError, showSuccess } from "@/app/_components/alerts/toast/ToastMessages";
import { setDataChangeNodeScriptsCount } from "@/app/_components/table/states/nodeSriptsStates";

interface CallLeadsProviderProps {
    chatData?: any;
    userId?: string;
    onMetadataUpdated?: (metadata: any) => void;
    confCallLeads: any
}

export const CallLeadsProvider: React.FC<CallLeadsProviderProps> = ({
                                                                        chatData,
                                                                        userId,
                                                                        onMetadataUpdated,
                                                                        confCallLeads,
                                                                    }) => {
    const data = chatData?.chat || {};
    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const updatedData: Record<string, any> = {};
        confCallLeads.configuration.forEach(({ accessorKey, filterComponent, config }) => {
            if (!config.show) return;
            const rawValue = formData.get(accessorKey);

            if (filterComponent === "DateFilterComponent" && rawValue) {
                const [year, month, day] = rawValue.toString().split("-");
                updatedData[accessorKey] = `${month}/${day}/${year}`;
            } else {
                updatedData[accessorKey] = rawValue?.toString() ?? "";
            }
        });

        if (data._id) {
            updatedData._id = data._id;
        }

        const updateMetadata = {
            chatData: updatedData,
            userId: userId,
        };

        const updateMongo = {
            collection: "leads",
            filter: { _id: updatedData._id },
            update: { $set: updatedData },
            options: {
                upsert: true
            }
        };

        try {
            const getRespUpd = await fetch("/api/betterAuth/updateChatData", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(updateMetadata),
            });

            if (!getRespUpd.ok) {
                const { error } = await getRespUpd.json();
                showError(error || "Something wrong happened");
                return;
            }

            const { metadata, message } = await getRespUpd.json();

            onMetadataUpdated?.(metadata);

        } catch (err) {
            console.error("Error updating user metadata:", err);
            showError("Error updating metadata");
            return;
        }

        try {
            const updateResult = await fetch("/api/mongo/call-center/update-data", {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(updateMongo),
            });

            if (updateResult.status !== 200) {
                const errorResponse = await updateResult.text();
                console.error("Error updating parent node:", errorResponse);
                showError("Error updating parent node: " + errorResponse);
            } else {
                setDataChangeNodeScriptsCount();
            }
        } catch (err) {
            console.error("Error fetching to update Mongo:", err);
            showError("Error updating data in Mongo");
        }
    };

    return (
        <>
            <div className="flex justify-center mt-3">
                <Typography level="h2" fontSize="xl" sx={{ mb: 0.5 }}>
                    Client info
                </Typography>
            </div>

            <div className="flex flex-col items-center mt-4">
                <form onSubmit={handleSubmit} className="w-full max-w-md">
                    {confCallLeads?.configuration?.map(({ accessorKey, header, filterComponent, config }) => {
                        if (!config.show) return null;

                        let inputType = filterComponent === "DateFilterComponent" ? "date" : "text";
                        const defaultVal = filterComponent === "DateFilterComponent"
                            ? transformDate(data[accessorKey])
                            : data[accessorKey] ?? "";

                        return (
                            <FormControl key={accessorKey} sx={{ mb: 2 }}>
                                <FormLabel>{header}</FormLabel>
                                <Input
                                    name={accessorKey}
                                    type={inputType}
                                    disabled={!config.editable}
                                    defaultValue={defaultVal}
                                />
                            </FormControl>
                        );
                    })}

                    <div className="flex justify-end">
                        <Button type="submit" variant="solid" color="primary">
                            Update
                        </Button>
                    </div>
                </form>
            </div>
        </>
    );
};

function transformDate(dob?: string) {
    if (!dob) return "";
    const [month, day, year] = dob.split("/");
    if (!year || !month || !day) return "";
    return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
}
