import React from "react";
import {getInitialConfigurationPage} from "@/app/_lib/utils/getInitialConfigurationPage";
import {TabsReportTableProvider} from "@/app/_components/reports/TabsReportTableProvider";

export default async function TransfersPage() {
    const tableName = 'transfers';
    let {globalSearchConfig, tableConfig, defaultFilters} = await getInitialConfigurationPage(tableName)
    tableConfig = {...tableConfig, sort: {"completed_date": -1}}

    return (
        <TabsReportTableProvider
            tableName={tableName}
            globalSearchConfig={globalSearchConfig}
            tableConfig={tableConfig}
            defaultFilters={defaultFilters}/>
    )
}
