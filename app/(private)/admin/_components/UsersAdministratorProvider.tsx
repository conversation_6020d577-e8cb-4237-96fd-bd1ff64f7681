'use client'
import React, {useEffect, useState} from "react";
import Typography from "@mui/joy/Typography";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {useEntity} from "simpler-state";
import {
    setFormPermissions,
    setUserIdForEdit,
    userIdForEdit,
    setActiveTabUserMetadata,
    setOriginalTemplate,
    setRolSelected,
    setTimezoneSelected,
    setTabsAdminData
} from "@/app/_components/table/states/adminEditStates";
import {showWarning} from "@/app/_components/alerts/toast/ToastMessages";
import {Autocomplete, AutocompleteOption, Button, Grid, IconButton} from "@mui/joy";
import _ from "lodash";
import {AdminMetadata} from "@/app/_components/queries/AdminMetadata";
import {ViewDataUserAdminComponent} from "@/app/_components/admin/ViewDataUserAdminComponent";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";
import {loading, setLoading} from "@/app/_components/Loading/loadingState";
import {GetAllUsers} from "@/app/_components/queries/GetAllUsers";
import RefreshIcon from '@mui/icons-material/Refresh';
import CheckOutlinedIcon from "@mui/icons-material/CheckOutlined";

export const UsersAdministratorProvider: React.FC<any> = () => {
    const userSelected = useEntity(userIdForEdit)
    const [selectedUser, setSelectedUser] = useState(null);
    const [isDisabled, setIsDisabled] = useState(false);
    const queryClient = useQueryClient();

    useEffect(() => {
        if (loading.get()) setLoading(false)
    }, [])

    useEffect(() => {
        if (userSelected && allUsers) {
            setIsDisabled(true);
            const userFound = allUsers.find((user: any) => user.id === userSelected)
            setSelectedUser({label: userFound?.email, id: userFound?.id});
        } else {
            setIsDisabled(false);
            setSelectedUser("");
        }
    }, [userSelected]);

    const handleChangeUser = (event: React.SyntheticEvent | null, userSelect: any | null) => {
        if (_.isString(userSelect)) {
            showWarning('User not found, please select a valid user')
            setUserIdForEdit(null)
        } else {
            if (userSelected) {
                queryClient.invalidateQueries({queryKey: ["user_admin_edit"]});
            }
            setIsDisabled(true);
            setSelectedUser(userSelect);
            setUserIdForEdit(userSelect?.id)
        }
    };

    const resetSelection = () => {
        setIsDisabled(true);

        setSelectedUser("");
        setUserIdForEdit(null);
        setFormPermissions(null);
        setOriginalTemplate(null);
        setActiveTabUserMetadata('roles');
        setRolSelected(null);
        setTimezoneSelected(null);

        queryClient.invalidateQueries({queryKey: ["user_admin_edit"]});
        queryClient.removeQueries({queryKey: ["user_admin_edit"]});

        setTimeout(() => {
            setIsDisabled(false);
        }, 300);
    };

    const {data: dataUserSel, isFetching: gettingUser} = useQuery(AdminMetadata(userSelected));
    const {data: allUsers, isFetching: gettingAllUsers} = useQuery(GetAllUsers());

    return (<>
        <div className="flex flex-col mt-3">
            {(gettingAllUsers || gettingUser) ? <LoadingMessage message="Loading data..."/> : <>
                <Grid container spacing={2}>
                    {(allUsers && allUsers.length > 0) ? <>
                            <Grid xs={12} md={userSelected ? 4 : 12} lg={userSelected ? 4 : 12}>
                                <div className="flex justify-center mt-3">
                                    <Typography level="title-md">
                                        List email users available
                                    </Typography>
                                    {userSelected && (
                                        <IconButton variant="soft" size="sm" color="neutral" sx={{ml: 2}}
                                                    onClick={resetSelection}>
                                            <RefreshIcon/>
                                        </IconButton>
                                    )}
                                </div>
                                <div className="flex justify-center mt-3">
                                    <Autocomplete
                                        options={allUsers.map((option: any) => {
                                            return {label: option.email, id: option.id}
                                        })}
                                        placeholder="Users available"
                                        freeSolo
                                        disableClearable={true}
                                        value={selectedUser}
                                        onChange={handleChangeUser}
                                        disabled={isDisabled}
                                        sx={{height: "32px", width: "220px", fontSize: '12px'}}
                                        renderOption={(props: any, option: any) => {
                                            const {key, ...rest} = props;
                                            return <AutocompleteOption key={key}  {...rest}>
                                                <Typography level="body-xs">
                                                    {option.label}
                                                </Typography>
                                            </AutocompleteOption>
                                        }}
                                    />
                                </div>
                            </Grid>
                            {userSelected &&
                                <ViewDataUserAdminComponent
                                    data={dataUserSel}
                                    userSelected={userSelected}
                                    resetSelection={resetSelection}
                                />}
                        </> :
                        <div className="flex justify-center"
                             style={{position: 'absolute', width: '97%', marginTop: '20px'}}>
                            <Typography level="h4">
                                No users for show
                            </Typography>
                        </div>
                    }
                </Grid>
            </>}
        </div>
    </>)
}

