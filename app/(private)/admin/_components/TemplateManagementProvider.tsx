'use client'
import React, {useState} from "react";
import Typography from "@mui/joy/Typography";
import {Autocomplete, AutocompleteOption, Grid, Modal} from "@mui/joy";
import {styledName} from "@/app/_components/utils/styledName";
import {buildSchemaTableConfig} from "@/app/_components/admin/funcs/buildSchemaUserPMD";
import _ from "lodash";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import Sheet from "@mui/joy/Sheet";
import Button from "@mui/joy/Button";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import DoDisturbAltOutlinedIcon from '@mui/icons-material/DoDisturbAltOutlined';
import ModalDialog from "@mui/joy/ModalDialog";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {FormUserAdmin} from "@/app/_components/admin/userAdmin/FormUserAdmin";
import {
    formPermissions,
    setFormPermissions,
    setRolSelected,
    setTabsAdminData
} from "@/app/_components/table/states/adminEditStates";
import {useEntity} from "simpler-state";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {DateTime} from "luxon";
import {Templates} from "@/app/_components/queries/Templates";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";

export const TemplateManagementProvider: React.FC<any> = ({user}) => {
    const formDataTemplate = useEntity(formPermissions)
    const [templateSel, setTemplateSel] = useState(null);
    const [updateAtFormat, setUpdateAtFormat] = useState(null);
    const [titleAC, setTextAC] = useState("Select the role to edit it or type the name of the new template.");
    const [textSubmit, setTextSubmit] = useState("");
    const [dataSchema, setDataSchema] = useState(null);
    const [loadingButton, setLoadingButton] = useState(false);
    const [disableInput, setDisableInput] = useState(false);
    const [openModal, setOpenModal] = useState(false);
    const [nonMatchingValue, setNonMatchingValue] = useState("");
    const queryClient = useQueryClient();

    const saveTemplateAsRole = async (nameTemplate: string) => {
        const objTemplate = {
            permissions: formDataTemplate,
            role: nameTemplate,
            userId: user?.id
        }
        const getRespTemplate = await fetch("/api/mongo/saveOrUpdRoleTemplate", {
            method: 'POST',
            body: JSON.stringify(objTemplate),
        });
        if (getRespTemplate.ok) {
            showSuccess("Your template has been saved");
            invalidateQuery();
        } else {
            const {message} = await getRespTemplate.json();
            showError(message || "Something wrong happened")
        }
    }

    const deleteTemplateRole = async () => {
        setLoadingButton(true)
        const objTemplate = {
            roleId: templateSel._id
        }
        const getRespTemplate = await fetch("/api/mongo/deleteTemplateRole", {
            method: 'POST',
            body: JSON.stringify(objTemplate),
        });
        if (getRespTemplate.ok) {
            showSuccess("Your template has been deleted");
            invalidateQuery();
        } else {
            const {message} = await getRespTemplate.json();
            setLoadingButton(false)
            showError(message || "Something wrong happened")
        }
    }

    const resetValuesToReselect = () => {
        setDisableInput(false)
        setLoadingButton(false)
        setOpenModal(false)
        setTemplateSel(null)
        setTextSubmit("")
        setTextAC("Select the role to edit it or type the name of the new template.")
        setDataSchema(null)
        setUpdateAtFormat(null)
    }

    const setTypeBodyModal = (isDelete: boolean) => {
        if (isDelete) {
            setOpenModal(true)
        } else {
            setLoadingButton(true)
            const checkNameTemplate = (_.isString(templateSel)) ? templateSel : templateSel?.role
            saveTemplateAsRole(checkNameTemplate).then()
        }
    }

    const {data, isFetching} = useQuery(Templates(() => resetValuesToReselect()));

    const handleChangeTemplate = (event: React.SyntheticEvent | null, valueInput: any | null) => {
        if (!_.isEmpty(valueInput)) {
            const getPermissions = (_.isString(valueInput)) ? data?.noneRole?.permissions : valueInput?.permissions
            const getFormData = buildSchemaTableConfig(data, getPermissions, null)
            setTabsAdminData(data?.tabs)
            setFormPermissions(getFormData?.formData)
            setDataSchema(getFormData)
            if (!_.isString(valueInput)) {
                const getDateUpd = DateTime.fromISO(valueInput?.updatedAt)
                setUpdateAtFormat(getDateUpd.toFormat('DD TTT'))
                setRolSelected(valueInput)
                setTextSubmit("Update Template")
                setTextAC("Template Selected")
            } else {
                setTextSubmit("New Template")
                setTextAC("Name new template")
            }
            setDisableInput(true)
        }
        setTemplateSel(valueInput)
    };

    const invalidateQuery = () => {
        setNonMatchingValue("")
        queryClient.invalidateQueries({queryKey: ["templates_management_tab"]});
    }

    const getBodyModal = () => {
        return (
            <>
                <Typography id="filter-modal" level="h2">
                    Are you sure you want to delete {templateSel?.role} ?
                </Typography>
                <Sheet sx={{display: 'flex', flexDirection: 'column', gap: 2}}>
                    <Button disabled={loadingButton} loading={loadingButton} color={'primary'} variant={'outlined'}
                            onClick={() => deleteTemplateRole()}>
                        Yes
                    </Button>
                    <Button disabled={loadingButton} loading={loadingButton} color={'primary'} variant={'outlined'}
                            onClick={() => setOpenModal(false)}>
                        No
                    </Button>
                </Sheet>
            </>
        )
    }

    return (<>
        <div className="flex flex-col mt-3">
            {(isFetching) ? <LoadingMessage message="Loading templates..."/> :
                (data?.roles && data?.roles.length > 0) ?
                    <Grid container spacing={2}>
                        <Grid xs={12}
                              md={(templateSel && !_.isString(templateSel)) ? 8 : 12}
                              lg={(templateSel && !_.isString(templateSel)) ? 8 : 12}>
                            <div className="flex justify-center mt-3">
                                <Typography level="title-md">
                                    {titleAC}
                                </Typography>
                            </div>
                            <div className="flex justify-center mt-3 mb-5">
                                <div className="flex flex-col items-center">
                                    <Autocomplete
                                        value={templateSel}
                                        options={data?.roles.map((option: any) => {
                                            return {label: styledName(option.role), ...option}
                                        })}
                                        disableClearable={true}
                                        disabled={disableInput}
                                        placeholder="Roles available"
                                        freeSolo
                                        blurOnSelect={true}
                                        onChange={(event, value) => {
                                            setNonMatchingValue("");
                                            handleChangeTemplate(event, value);
                                        }}
                                        onInputChange={(event, newInputValue) => {
                                            if (newInputValue && !data?.roles.some((role: any) =>
                                                role.role.toLowerCase() === newInputValue.toLowerCase())) {
                                                setNonMatchingValue(newInputValue);
                                            } else {
                                                setNonMatchingValue("");
                                            }
                                        }}
                                        sx={{height: "32px", width: "200px"}}
                                        renderOption={(props: any, option: any) => {
                                            const {key, ...rest} = props;
                                            return <AutocompleteOption key={key}  {...rest}>
                                                <Typography level="body-md">
                                                    {option.label}
                                                </Typography>
                                            </AutocompleteOption>
                                        }}
                                    />

                                    {nonMatchingValue && !disableInput && (
                                        <Button
                                            startDecorator={<SaveOutlinedIcon/>}
                                            size="sm"
                                            variant="soft"
                                            color="neutral"
                                            sx={{mt: 1}}
                                            onClick={() => handleChangeTemplate(null, nonMatchingValue)}>
                                            Create {nonMatchingValue} template
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </Grid>
                        {(templateSel && !_.isString(templateSel)) &&
                            <Grid xs={12} md={4} lg={4}>
                                <div className="flex justify-center mt-4">
                                    <Typography level="body-md">
                                        Total Users: {templateSel?.users.length || 0}
                                    </Typography>
                                </div>
                                {templateSel?.editable &&
                                    <div className="flex justify-center">
                                        <Typography level="body-md">
                                            Last Update: {updateAtFormat}
                                        </Typography>
                                    </div>}
                            </Grid>}
                        {templateSel && <Grid xs={12}>
                            <div className="flex justify-center">
                                <Button startDecorator={<SaveOutlinedIcon/>}
                                        variant="outlined" color="success"
                                        loading={loadingButton}
                                        sx={{marginRight: "15px"}}
                                        onClick={() => setTypeBodyModal(false)}>
                                    {textSubmit}
                                </Button>
                                <Button startDecorator={<DoDisturbAltOutlinedIcon/>}
                                        variant="outlined" color="warning"
                                        loading={loadingButton}
                                        sx={{marginRight: "15px"}}
                                        onClick={() => invalidateQuery()}>
                                    Cancel
                                </Button>
                                {templateSel?.editable &&
                                    <Button startDecorator={<DeleteOutlinedIcon/>}
                                            variant="outlined" color="danger"
                                            onClick={() => setTypeBodyModal(true)}>
                                        Delete Template
                                    </Button>}
                                <Modal open={openModal}
                                       onClose={(event, reason) => {
                                           if (reason === 'backdropClick') return;
                                           setOpenModal(false)
                                       }}>
                                    <ModalDialog aria-labelledby="filter-modal">
                                        {getBodyModal()}
                                    </ModalDialog>
                                </Modal>
                            </div>
                        </Grid>}
                    </Grid> : !isFetching && (data?.roles && data?.roles.length === 0) ?
                        <div className="flex justify-center"
                             style={{position: 'absolute', width: '97%', marginTop: '20px'}}>
                            <Typography level="h4">
                                {`No roles found`}
                            </Typography>
                        </div> : <></>
            }
        </div>
        <div className="flex flex-col mt-10">
            {(dataSchema) ?
                <FormUserAdmin widgets={dataSchema?.widgets} uiSchema={dataSchema?.uiSchema}
                               schema={dataSchema?.schema} rolUser={null} resetSelection={resetValuesToReselect}/> :
                templateSel &&
                <div className="flex justify-center"
                     style={{position: 'absolute', width: '97%', marginTop: '20px'}}>
                    <Typography level="h4">
                        {`This Template can't be built correctly, refresh or contact support, Thanks.`}
                    </Typography>
                </div>
            }
        </div>
    </>)
}
