'use client'
import React, {useEffect} from "react";
import {settingsOnRouting} from "@/app/_components/utils/settingsOnRouting";
import Typography from "@mui/joy/Typography";
import {HeadAdminView} from "@/app/_components/admin/tableConf/HeadAdminView";
import {EditFormAdmin} from "@/app/_components/admin/tableConf/EditFormAdmin";

export const TableConfigurationProvider: React.FC<any> = ({collData}) => {
    useEffect(() => {
        settingsOnRouting()
    }, []);


    return (<>
        <div
            className="flex flex-col mt-3">
            <HeadAdminView collectionData={collData}/>
            <div className="flex justify-center mt-5">
                <EditFormAdmin/>
            </div>
        </div>
    </>)
}

