import React from "react";
import {getInitialConfigurationPage} from "@/app/_lib/utils/getInitialConfigurationPage";
import {DevPageProvider} from "@/app/(private)/devPage/_components/DevPageProvider";

export default async function DevPage() {
    const tableName = 'testTable';
    let {globalSearchConfig, tableConfig, defaultFilters} = await getInitialConfigurationPage(tableName)

    return (<>
        <DevPageProvider
            globalSearchConfig={globalSearchConfig}
            tableColumns={tableConfig.tableColumns}
            dataTable={JSON.stringify(tableConfig.data)}
            tableName={tableName}
            count={tableConfig.count}
            tableConfig={tableConfig}
            defaultFilters={defaultFilters}
        />
    </>)
}