'use client';

import React, {useState} from "react";
import {Tab, <PERSON>b<PERSON>ist, <PERSON>b<PERSON>anel, Tabs} from "@mui/joy";
import {
    GeneralPerformanceProvider
} from "@/app/(private)/reports/vendorPerformance/_components/GeneralPerformanceProvider";
import {
    LeadAgingPerformanceProvider
} from "@/app/(private)/reports/vendorPerformance/_components/LeadAgingPerformanceProvider";
import {loading, setLoading} from "@/app/_components/Loading/loadingState";

export const TabsPerformanceProvider: React.FC<any> = ({summaryReportConf, userMetadata, vendorsData, timezone}) => {
    const [activeTab, setActiveTab] = useState(0);

    const changeTab = (event: any, newValue: number) => {
        if (loading.get()) setLoading(false)
        setActiveTab(newValue)
    }

    return (
        <Tabs defaultValue={0} value={activeTab}
              onChange={changeTab}
              sx={{'& .MuiTabs-flexContainer': {justifyContent: 'center'}}}>
            <TabList sticky='top' sx={{
                display: 'inline-flex',
                justifyContent: 'center',
                '& .MuiTab-root': {
                    minWidth: 5,
                    '@media (max-width: 600px)': {
                        fontSize: '0.75rem',
                        minWidth: 0,
                        padding: '6px 12px',
                    }
                },
                zIndex: 8
            }}>
                <Tab value={0} sx={{minWidth: 20}}>General Report</Tab>
                <Tab value={1} sx={{minWidth: 20}}>Lead Aging Report</Tab>
            </TabList>
            <TabPanel value={0}>
                <GeneralPerformanceProvider
                    userMetadata={userMetadata}
                    summaryReportConf={summaryReportConf}
                    vendorsData={vendorsData}
                    timezone={timezone}
                />
            </TabPanel>
            <TabPanel value={1}>
                <LeadAgingPerformanceProvider
                    userMetadata={userMetadata}
                    summaryReportConf={summaryReportConf}
                    vendorsData={vendorsData}
                    timezone={timezone}
                />
            </TabPanel>
        </Tabs>
    );

}
