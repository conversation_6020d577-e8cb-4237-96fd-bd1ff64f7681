"use client"
import React, {useEffect, useState} from "react";
import _ from "lodash";
import {TableSummaryReport} from "@/app/_components/reports/TableSummaryReport";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {DateTime} from "luxon";
import {showError} from "@/app/_components/alerts/toast/ToastMessages";
import {tryit} from "radash";
import {fetchDataInParallel} from "@/app/_components/reports/func/fetchDataInParallel";
import {HeaderPerformanceReports} from "@/app/_components/reports/performance/HeaderPerformanceReports";
import {downloadCSV} from "@/app/_components/reports/func/downloadReportCsv";
import {searchSaveReport} from "@/app/_components/reports/func/searchSaveReport";
import {combinePerformanceReports} from "@/app/_components/reports/func/combinePerformanceReports";
import {isCurrentMonth} from "@/app/_components/reports/func/isCurrentMonth";
import {getYearsDates} from "@/app/_components/reports/func/getYearsDates";
import {Box, Typography} from "@mui/joy";
import {ProgressLoadingReport} from "@/app/_components/reports/ProgressLoadingReport";

export const GeneralPerformanceProvider: React.FC<any> = ({
                                                              userMetadata,
                                                              summaryReportConf,
                                                              vendorsData,
                                                              timezone
                                                          }) => {
    const [optionRep, setOptionRep] = useState(summaryReportConf?.select_keys || []);
    const [optionsCond, setOptionsCond] = useState(summaryReportConf?.options || []);
    const [requiredKey, setRequiredKey] = useState(summaryReportConf?.required_key || null);
    const [optionsReport, setOptionsReport] = useState(summaryReportConf?.options_report || []);
    const [optionsCmp, setOptionsCmp] = useState(summaryReportConf?.campaigns || []);
    const [optionDetail, setOptionDetail] = useState(summaryReportConf?.select_keys?.drill_down ?? null);
    const [option3rdLvl, setOption3rdLvl] = useState(summaryReportConf?.select_keys?.drill_down?.details ?? null);
    const [rangeDate, setRangeDate] = useState({startDate: '', endDate: ''});
    const [weeksNumbers, setWeeksNumbers] = useState([]);
    const [selectVendor, setSelectVendor] = useState(null);
    const [partialChunks, setPartialChunks] = useState<any[]>([]);
    const [fetchProgress, setFetchProgress] = useState<number>(0);
    const [progressLabel, setProgressLabel] = useState<string>('');
    const queryClient = useQueryClient();

    useEffect(() => {
        const myTimeZone = 'America/Chicago';
        const todayDate = DateTime.now().setZone(myTimeZone).toISODate();
        setRangeDate({startDate: todayDate, endDate: todayDate})
    }, []);

    useEffect(() => {
        setPartialChunks([]);
        setFetchProgress(0);
        setProgressLabel('');
    }, [selectVendor, rangeDate]);

    const onChangeVendor = (event: any, newVendor: any) => {
        setSelectVendor(newVendor)
    }

    const reFetch = async () => {
        setPartialChunks([]);
        await queryClient.invalidateQueries({queryKey: ['get-performance-data']});
    }

    const getDateRangeFromWeeks = (selectedWeeks: any) => {
        if (selectedWeeks.length === 0) return null;
        const sortedWeeks = selectedWeeks.sort((a: any, b: any) => a.weekNumber - b.weekNumber);
        const startDate = DateTime.fromISO(sortedWeeks[0].startDate).toFormat('yyyy-MM-dd');
        const endDate = DateTime.fromISO(sortedWeeks[sortedWeeks.length - 1].endDate).toFormat('yyyy-MM-dd');
        const weekNumbers = sortedWeeks.map((week: any) => week.weekNumber);
        return {startDate, endDate, weekNumbers};
    };

    const changeRangeDateFilter = (event: any) => {
        setRangeDate({startDate: event.startDate, endDate: event.endDate});
        setWeeksNumbers(event?.weeks ?? []);
    }

    const getDataCollections = async () => {
        if (!_.isEmpty(selectVendor) && !_.isEmpty(rangeDate.startDate)) {
            const checkMonth = isCurrentMonth(rangeDate);
            if (!checkMonth) {
                const years = getYearsDates(rangeDate)
                const [errorSearchRep, saveReport] = await tryit(async () => {
                    return searchSaveReport("general", payload.vendor, weeksNumbers, years);
                })();
                if (!_.isEmpty(saveReport)) {
                    return combinePerformanceReports(saveReport, 'campaignKey', 'subId');
                }
            }
            const payload = {
                vendor: selectVendor,
                optionsReport,
                rangeDate,
                context: {timezone}
            };

            const [error, finalResponse] = await tryit(async () => {
                return fetchDataInParallel(
                    optionsReport,
                    payload,
                    (updater) => {
                        setPartialChunks(prev => updater(prev));
                    },
                    {
                        onProgressUpdate: (percent, label) => {
                            setFetchProgress(percent);
                            setProgressLabel(label);
                        }
                    }
                );
            })();

            if (error) {
                const errorMessage = error.message || 'Something wrong happened';
                showError(errorMessage)
                return {report: [], counts: {}}
            }
            return finalResponse;
        } else {
            return {report: [], counts: {}}
        }
    };

    const processData = async (payload: any) => {
        const getProDataPer = await fetch("/api/mongo/reports/performance/processFinalData", {
            method: 'POST',
            body: JSON.stringify(payload),
        });
        if (getProDataPer.ok) {
            const {response} = await getProDataPer.json();
            return response || [];
        } else {
            const {error} = await getProDataPer.json();
            showError(error || "Something wrong happened")
            return []
        }
    };

    const handleDownloadCSV = async () => {
        const valuesSelected = {
            principal: optionRep,
            secondary: optionDetail,
            third: option3rdLvl
        }
        await downloadCSV(dataPerformance?.report, optionsCond, valuesSelected, "performance", null, rangeDate, true)
    }

    const {data: dataPerformance, dataUpdatedAt, isFetching: isFetchProcess} = useQuery({
        queryKey: ['get-performance-data', selectVendor, rangeDate],
        queryFn: () => getDataCollections(),
        enabled: !!selectVendor,
        refetchOnWindowFocus: false
    });

    const sxBox = (isStreaming: boolean) => {
        return {
            mt: '25px',
            overflow: (isStreaming) ? 'hidden' : 'scroll',
            height: {
                xs: '75vh',
                sm: '75vh',
                md: '80vh',
                lg: '85vh'
            },
            width: {
                xs: '90%',
                sm: '97%',
                md: '98%',
                lg: '99%'
            },
            position: 'relative',
            '@media (max-width: 600px)': {
                width: '95%'
            }
        }
    }

    return (<>
        <HeaderPerformanceReports
            title="General Performance"
            vendorsData={vendorsData}
            rangeDate={rangeDate}
            userMetadata={userMetadata}
            isFetchProcess={isFetchProcess}
            changeRangeDateFilter={changeRangeDateFilter}
            onChangeVendor={onChangeVendor}
            allData={dataPerformance?.report}
            countsData={dataPerformance?.counts}
            reFetch={reFetch}
            dataUpdatedAt={dataUpdatedAt}
            handleDownloadCSV={handleDownloadCSV}
        />
        {(!_.isEmpty(dataPerformance?.report)) ?
            <Box sx={sxBox(false)}>
                <TableSummaryReport principalValueSel={optionRep} detailValueSel={optionDetail}
                                    optionsCond={optionsCond} summaryRes={dataPerformance?.report}
                                    tableName={"performance"}
                                    thirdLevelSel={option3rdLvl} requiredKey={requiredKey}
                                    isMatchRep={true} isPerformance={true}/>
            </Box> :
            (isFetchProcess) ? (<Box sx={sxBox(true)}>
                    <ProgressLoadingReport progressLabel={progressLabel} fetchProgress={fetchProgress}/>
                    <TableSummaryReport principalValueSel={optionRep} detailValueSel={optionDetail}
                                        optionsCond={optionsCond} summaryRes={partialChunks} tableName={"performance"}
                                        thirdLevelSel={option3rdLvl} requiredKey={requiredKey}
                                        isMatchRep={true} isPerformance={true}/>
                </Box>) :
                (!_.isEmpty(selectVendor)) ?
                    <div className="flex justify-center mt-10">
                        <Typography level="title-lg">
                            No data to display.
                        </Typography>
                    </div> :
                    <div className="flex justify-center mt-10">
                        <Typography level="title-lg">
                            Please select a vendor.
                        </Typography>
                    </div>}
    </>)
}
