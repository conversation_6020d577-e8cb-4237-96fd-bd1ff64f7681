"use client"
import React, {useEffect, useState} from "react";
import Typography from "@mui/joy/Typography";
import _ from "lodash";
import {DateTime} from "luxon";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {showError} from "@/app/_components/alerts/toast/ToastMessages";
import {parallel, tryit} from "radash";
import {fetchTransfersBuckets} from "@/app/_components/reports/func/aging/fetchTransfersBuckets";
import {groupDataAgingPerformance} from "@/app/_components/reports/func/aging/groupDataAgingPerformance";
import {fetchPostbacksBuckets} from "@/app/_components/reports/func/aging/fetchPostbacksBuckets";
import {buildPostbacksResponse} from "@/app/_components/reports/func/aging/buildPostbacksResponse";
import {TableSummaryReport} from "@/app/_components/reports/TableSummaryReport";
import Loading from "react-loading";
import {downloadCSV} from "@/app/_components/reports/func/downloadReportCsv";
import {HeaderPerformanceReports} from "@/app/_components/reports/performance/HeaderPerformanceReports";
import {searchSaveReport} from "@/app/_components/reports/func/searchSaveReport";
import {combinePerformanceReports} from "@/app/_components/reports/func/combinePerformanceReports";
import {isCurrentMonth} from "@/app/_components/reports/func/isCurrentMonth";
import {getYearsDates} from "@/app/_components/reports/func/getYearsDates";
import {splitDateRangeIntoHours} from "@/app/_components/reports/func/splitDateRangeIntoHours";
import {fetchWithRetry} from "@/app/_components/reports/func/fetchWithRetry";
import {Box} from "@mui/joy";

const sxBox = {
    mt: '25px',
    overflow: 'scroll',
    height: {
        xs: '75vh',
        sm: '75vh',
        md: '80vh',
        lg: '85vh'
    },
    width: {
        xs: '90%',
        sm: '97%',
        md: '98%',
        lg: '99%'
    },
    position: 'relative',
    '@media (max-width: 600px)': {
        width: '95%'
    }

}

export const LeadAgingPerformanceProvider: React.FC<any> = ({
                                                                userMetadata,
                                                                summaryReportConf,
                                                                vendorsData,
                                                                timezone
                                                            }) => {
    const {aging} = summaryReportConf
    const [optionRep, setOptionRep] = useState(aging?.select_keys || []);
    const [optionsCond, setOptionsCond] = useState(summaryReportConf?.options || []);
    const [requiredKey, setRequiredKey] = useState(aging?.required_key || null);
    const [optionsReport, setOptionsReport] = useState(summaryReportConf?.options_report || []);
    const [weeksNumbers, setWeeksNumbers] = useState([]);
    const [selectVendor, setSelectVendor] = useState(null);
    const [messageLoading, setMessageLoading] = useState(null);
    const [rangeDate, setRangeDate] = useState({startDate: '', endDate: ''});
    const queryClient = useQueryClient();

    useEffect(() => {
        const myTimeZone = 'America/Chicago';
        const todayDate = DateTime.now().setZone(myTimeZone).toISODate();
        setRangeDate({startDate: todayDate, endDate: todayDate})
    }, []);

    const reFetch = async () => {
        await queryClient.invalidateQueries({
            queryKey: ['get-lead-aging-data', selectVendor, rangeDate]
        });
    }

    const onChangeVendor = (event: any, newVendor: any) => {
        setSelectVendor(newVendor)
    }

    const changeRangeDateFilter = (event: any) => {
        setRangeDate({startDate: event.startDate, endDate: event.endDate});
        setWeeksNumbers(event?.weeks ?? []);
    }

    const getDataLeadAging = async () => {
        if (!_.isEmpty(selectVendor) && !_.isEmpty(rangeDate.startDate)) {
            const payload = {
                vendor: selectVendor,
                optionsReport,
                rangeDate,
                context: {timezone}
            };
            let finalResults = []
            const checkMonth = isCurrentMonth(rangeDate);
            if (!checkMonth) {
                const years = getYearsDates(rangeDate)
                const [errorSearchRep, saveReport] = await tryit(async () => {
                    setMessageLoading('Searching Report...');
                    return searchSaveReport("aging", payload.vendor, weeksNumbers, years);
                })();
                if (!_.isEmpty(saveReport)) {
                    const getCombinedRep = combinePerformanceReports(saveReport, 'bucket', null);
                    return getCombinedRep?.report;
                }
            }
            const dayChunks = splitDateRangeIntoHours(rangeDate.startDate, rangeDate.endDate, timezone);

            const chunkPayloads = dayChunks.map(dr => ({
                ...payload,
                rangeDate: dr
            }));

            const allChunkLeads = await parallel(5, chunkPayloads, async (chunk) => {
                return getLeadsData(chunk);
            });

            const leadsData = _.flatten(allChunkLeads);
            if (leadsData.length > 0) {
                const objectTotal = {
                    bucket: 'Total',
                    leads: leadsData.length,
                    transfers: 0,
                    "transfers>120": 0,
                    "transfers<120": 0,
                    postbacks: 0
                }
                finalResults.push(objectTotal);
                const leadsChunks = chunkArray(leadsData);
                setMessageLoading("Fetching Transfers data...")
                const [errorTransfers, transfersResults] = await tryit(async () => {
                    return fetchTransfersBuckets(leadsChunks, optionsReport);
                })();
                if (!errorTransfers) {
                    setMessageLoading("Fetching Postbacks data...")
                    const groupTransfersResults = groupDataAgingPerformance(transfersResults, finalResults);
                    const [errorPostbacks, postbacksResults] = await tryit(async () => {
                        return fetchPostbacksBuckets(groupTransfersResults, optionsReport);
                    })();
                    if (!errorPostbacks) {
                        setMessageLoading("Processing final data...")
                        finalResults = buildPostbacksResponse(postbacksResults, finalResults);
                        return finalResults
                    } else {
                        return []
                    }
                } else {
                    return []
                }
            } else {
                return []
            }
        } else {
            return [];
        }
    }

    const getLeadsData = async (payload: any) => {
        setMessageLoading("Fetching Leads data...");
        try {
            const getLeadDataFetch = await fetchWithRetry("/api/mongo/reports/performance/aging/getLeads", {
                method: 'POST',
                body: JSON.stringify(payload),
                headers: {'Content-Type': 'application/json'}
            });
            return await getResponseFetch(getLeadDataFetch);
        } catch (err: any) {
            showError(err.message || "Error fetching leads with retry");
            return [];
        }
    };

    const getResponseFetch = async (dataResponse: any) => {
        if (dataResponse.ok) {
            const {response} = await dataResponse.json();
            return response || [];
        } else {
            const {error} = await dataResponse.json();
            showError(error || "Something wrong happened")
            return []
        }
    }

    const chunkArray = (array: Array<any>) => {
        const size = 50000;
        const ids = array.map(item => item._id);
        const chunks = [];
        for (let i = 0; i < ids.length; i += size) {
            chunks.push(ids.slice(i, i + size));
        }
        return chunks;
    }

    const handleDownloadCSV = async () => {
        const valuesSelected = {
            principal: optionRep,
            secondary: null,
            third: null
        }
        await downloadCSV(dataLeadAging, optionsCond, valuesSelected, "performance", null, rangeDate, true)
    }

    const {data: dataLeadAging, dataUpdatedAt, isFetching: isFetchProcess} = useQuery({
        queryKey: ['get-lead-aging-data', selectVendor, rangeDate],
        queryFn: async () => {
            return await getDataLeadAging();
        },
        enabled: !!selectVendor,
        refetchOnWindowFocus: false
    });


    return (<>
        <HeaderPerformanceReports
            title="Lead Aging Performance"
            vendorsData={vendorsData}
            rangeDate={rangeDate}
            userMetadata={userMetadata}
            isFetchProcess={isFetchProcess}
            dataUpdatedAt={dataUpdatedAt}
            changeRangeDateFilter={changeRangeDateFilter}
            onChangeVendor={onChangeVendor}
            allData={dataLeadAging}
            countsData={null}
            reFetch={reFetch}
            handleDownloadCSV={handleDownloadCSV}
        />
        {(!_.isEmpty(dataLeadAging)) ?
            <Box sx={sxBox}>
                <TableSummaryReport principalValueSel={optionRep} detailValueSel={null}
                                    optionsCond={optionsCond} summaryRes={dataLeadAging} tableName={"performance"}
                                    thirdLevelSel={null} isMatchRep={true} isPerformance={true}/>
            </Box> :
            (isFetchProcess) ?
                <div className="flex flex-col items-center justify-center mt-10">
                    <Loading color={'black'} width={'30px'} height={'30px'} type={'spin'}/>
                    <Typography level="title-lg" sx={{mt: 2}}>
                        {messageLoading}
                    </Typography>
                </div> :
                <div className="flex justify-center mt-10">
                    <Typography level="title-lg">
                        No data to show
                    </Typography>
                </div>}
    </>);
}
