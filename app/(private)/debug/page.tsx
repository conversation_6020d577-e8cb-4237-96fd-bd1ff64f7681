'use client'
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import {useRouter} from "next/navigation";
import React, {useEffect} from "react";
import {Box} from "@mui/joy";
import Typography from "@mui/joy/Typography";
import LinearProgress from "@mui/joy/LinearProgress";
import {useEntity} from "simpler-state";
import {debugFilters} from "@/app/_components/table/states/debugFiltersState";
import _ from "lodash";
import {setTabChangeOnSummary} from "@/app/_components/table/states/changeTabSummaryState";
import {setSummaryFilters} from "@/app/_components/table/states/accountingFiltersState";
import {setAccountingReportFiltersEntity} from "@/app/_components/table/states/accountingReportsState";

export default function DebugPage() {

    const debugFiltersEntity = useEntity(debugFilters);
    const {
        setFiltersEntity,
        setDefaultFilters,
        setColumnsVisibilityEntityConfiguration
    } = createTableEntities(debugFiltersEntity?.collection);

    const router = useRouter();

    useEffect(() => {
        if (debugFiltersEntity && !_.isEmpty(debugFiltersEntity) && !_.isEmpty(debugFiltersEntity?.filters)) {
            const {pathname} = debugFiltersEntity;
            if (debugFiltersEntity.pathname.includes('/accounting/summary')) {
                setSummaryFilters(debugFiltersEntity.filters)
            } else if (debugFiltersEntity.pathname.includes('/accounting/reports')) {
                setAccountingReportFiltersEntity(debugFiltersEntity.filters);
            } else {
                setTabChangeOnSummary(1)
                setFiltersEntity(debugFiltersEntity?.filters);
                setDefaultFilters(debugFiltersEntity?.filters);
                if(debugFiltersEntity.options) setColumnsVisibilityEntityConfiguration(debugFiltersEntity.options)

            }
            setTimeout(() => {
                router.push(pathname);
            }, 2000)
        }
    }, [debugFiltersEntity]);

    return (
        <>
            <Box className="debug-container p-5"
                 sx={
                     {
                         display: 'flex',
                         alignItems: 'center',
                         justifyContent: 'center',
                         height: '100%'
                     }
                 }>
                <Box>
                    <LinearProgress sx={{margin: 'auto'}}/>
                    <Typography
                        sx={{margin: 'auto'}}
                        color="neutral"
                        level="body-lg"
                        noWrap={false}
                        variant="plain">
                        Applying debug filters
                    </Typography>
                </Box>
            </Box>
        </>
    );
}
