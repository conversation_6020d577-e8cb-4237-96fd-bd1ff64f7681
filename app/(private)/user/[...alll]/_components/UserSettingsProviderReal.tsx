"use client";

import React, { useState } from "react";
import { UserSettings } from "@conversionfinder/shared-auth/components";
import { CssVarsProvider } from "@mui/joy/styles";
import { theme } from "@conversionfinder/shared-auth/theme";
import { loading, setLoading } from "@/app/_components/Loading/loadingState";
import { authClient } from "@/app/_lib/auth/auth-client";
import {
  showError,
  showSuccess,
} from "@/app/_components/alerts/toast/ToastMessages";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  useSessionsQuery,
  useRevokeSessionMutation,
  useRevokeAllSessionsMutation,
} from "@/app/_lib/config/get-ActiveSessionsSection";

// Interfaces locales basadas en better-auth
interface User {
  id: string;
  email: string;
  name?: string;
}

interface Session {
  id: string;
  token: string;
  userAgent: string;
  createdAt: string;
  expiresAt: string;
}

interface Passkey {
  id: string;
  name: string;
  createdAt: string;
  deviceType?: string;
}

interface UserSettingsActions {
  editName: (name: string) => Promise<void>;
  revokeSession: (token: string) => Promise<void>;
  revokeAllSessions: () => Promise<void>;
  registerPasskey: () => Promise<void>;
  deletePasskey: (passkeyId: string) => Promise<void>;
  deleteAccount: () => Promise<void>;
}

export const UserSettingsProviderReal: React.FC<{ user: User }> = ({
  user,
}) => {
  const queryClient = useQueryClient();

  // Estados de carga para cada acción
  const [isUpdatingName, setIsUpdatingName] = useState(false);
  const [isRevokingSession, setIsRevokingSession] = useState(false);
  const [isRevokingAllSessions, setIsRevokingAllSessions] = useState(false);
  const [isRegisteringPasskey, setIsRegisteringPasskey] = useState(false);
  const [isDeletingPasskey, setIsDeletingPasskey] = useState(false);
  const [isDeletingAccount, setIsDeletingAccount] = useState(false);

  // Queries para obtener datos reales usando la nueva configuración
  const { data: sessionsData = [], isLoading: isLoadingSessions } =
    useSessionsQuery();
  const revokeSessionMutation = useRevokeSessionMutation();
  const revokeAllSessionsMutation = useRevokeAllSessionsMutation();

  const { data: passkeysData = [], isLoading: isLoadingPasskeys } = useQuery({
    queryKey: ["passkeys"],
    queryFn: async () => {
      const { data, error } = await authClient.passkey.listUserPasskeys();
      if (error) {
        console.error("Error fetching passkeys:", error);
        return [];
      }
      return data || [];
    },
  });

  console.log("Loading sessions", sessionsData || []);

  // Mapear datos de BetterAuth a los tipos de la librería
  const sessions: Session[] = sessionsData.map((session) => ({
    id: session.id,
    token: session.token,
    userAgent: session.userAgent || "Unknown",
    createdAt: session.createdAt,
    expiresAt: session.expiresAt,
  }));

  const passkeys: Passkey[] = passkeysData.map((passkey) => ({
    id: passkey.id,
    name: passkey.name || `Passkey ${passkey.id.slice(0, 8)}`,
    createdAt: passkey.createdAt.toISOString(),
    deviceType: passkey.deviceType,
  }));

  // Mutations para las acciones reales
  const updateNameMutation = useMutation({
    mutationFn: (name: string) => authClient.updateUser({ name }),
    onSuccess: () => {
      showSuccess("Nombre actualizado exitosamente");
      // Invalidar queries relacionadas con el usuario si es necesario
    },
    onError: (error) => {
      showError("Error al actualizar el nombre");
      console.error("Error updating name:", error);
    },
  });

  const registerPasskeyMutation = useMutation({
    mutationFn: async () => {
      const result = await authClient.passkey.addPasskey();
      if (result?.error) {
        throw new Error(result.error.message || "Error al registrar passkey");
      }
      return result;
    },
    onSuccess: () => {
      showSuccess("Passkey registrado exitosamente");
      queryClient.invalidateQueries({ queryKey: ["passkeys"] });
    },
    onError: (error) => {
      showError("Error al registrar passkey");
      console.error("Passkey registration error:", error);
    },
  });

  const deletePasskeyMutation = useMutation({
    mutationFn: (passkeyId: string) =>
      authClient.passkey.deletePasskey({ id: passkeyId }),
    onSuccess: () => {
      showSuccess("Passkey eliminado exitosamente");
      queryClient.invalidateQueries({ queryKey: ["passkeys"] });
    },
    onError: (error) => {
      showError("Error al eliminar passkey");
      console.error("Passkey deletion error:", error);
    },
  });

  const deleteAccountMutation = useMutation({
    mutationFn: () => authClient.deleteUser(),
    onSuccess: () => {
      showSuccess("Cuenta eliminada exitosamente");
      // Aquí podrías redirigir al logout o mostrar un mensaje de confirmación
    },
    onError: (error) => {
      showError("Error al eliminar la cuenta");
      console.error("Account deletion error:", error);
    },
  });

  // Implementación de las acciones que requiere la librería
  const actions: UserSettingsActions = {
    editName: async (name: string) => {
      setIsUpdatingName(true);
      try {
        await updateNameMutation.mutateAsync(name);
      } finally {
        setIsUpdatingName(false);
      }
    },

    revokeSession: async (token: string) => {
      setIsRevokingSession(true);
      try {
        await revokeSessionMutation.mutateAsync(token);
        showSuccess("Sesión revocada exitosamente");
      } finally {
        setIsRevokingSession(false);
      }
    },

    revokeAllSessions: async () => {
      setIsRevokingAllSessions(true);
      try {
        await revokeAllSessionsMutation.mutateAsync();
        showSuccess("Todas las sesiones revocadas exitosamente");
      } finally {
        setIsRevokingAllSessions(false);
      }
    },

    registerPasskey: async () => {
      setIsRegisteringPasskey(true);
      try {
        await registerPasskeyMutation.mutateAsync();
      } finally {
        setIsRegisteringPasskey(false);
      }
    },

    deletePasskey: async (passkeyId: string) => {
      setIsDeletingPasskey(true);
      try {
        await deletePasskeyMutation.mutateAsync(passkeyId);
      } finally {
        setIsDeletingPasskey(false);
      }
    },

    deleteAccount: async () => {
      setIsDeletingAccount(true);
      try {
        await deleteAccountMutation.mutateAsync();
      } finally {
        setIsDeletingAccount(false);
      }
    },
  };

  React.useEffect(() => {
    if (loading.get()) {
      setLoading(false);
    }
  }, []);

  return (
    <CssVarsProvider theme={theme} defaultMode="light">
      <UserSettings
        user={user}
        sessions={sessions}
        passkeys={passkeys}
        actions={actions}
        isLoading={false}
        isLoadingSessions={isLoadingSessions}
        isLoadingPasskeys={isLoadingPasskeys}
        isUpdatingName={isUpdatingName}
        isRevokingSession={isRevokingSession}
        isRevokingAllSessions={isRevokingAllSessions}
        isRegisteringPasskey={isRegisteringPasskey}
        isDeletingPasskey={isDeletingPasskey}
        isDeletingAccount={isDeletingAccount}
        config={{
          showDeleteAccount: true,
          maxNameLength: 32,
          dateFormat: "MM/dd/yyyy hh:mm a",
          timezone: "America/Chicago",
        }}
      />
    </CssVarsProvider>
  );
};
