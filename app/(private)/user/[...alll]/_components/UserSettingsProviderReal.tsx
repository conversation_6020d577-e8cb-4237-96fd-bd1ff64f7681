"use client";

import React, { useState } from "react";
import { CssVarsProvider } from "@mui/joy/styles";
import { Box, Container, Typography, Divider } from "@mui/joy";
import { loading, setLoading } from "@/app/_components/Loading/loadingState";
import { authClient } from "@/app/_lib/auth/auth-client";
import {
  showError,
  showSuccess,
} from "@/app/_components/alerts/toast/ToastMessages";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  useSessionsQuery,
  useRevokeSessionMutation,
  useRevokeAllSessionsMutation,
} from "@/app/_lib/config/get-ActiveSessionsSection";
import { UserNameSection } from "@/app/_components/betterAuth/UserNameAccountSection";
import { ActiveSessionsSection } from "@/app/_components/betterAuth/ActiveSessionsSection";
import { PasskeySection } from "@/app/_components/betterAuth/PasskeySection";

// Interfaces locales basadas en better-auth
interface User {
  id: string;
  email: string;
  name?: string;
}

export const UserSettingsProviderReal: React.FC<{ user: User }> = ({
  user,
}) => {
  const queryClient = useQueryClient();

  React.useEffect(() => {
    if (loading.get()) {
      setLoading(false);
    }
  }, []);

  return (
    <CssVarsProvider disableTransitionOnChange>
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Typography level="h1" sx={{ mb: 4, textAlign: "center" }}>
          Configuración de Usuario
        </Typography>

        <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
          {/* Sección de Nombre de Usuario */}
          <UserNameSection user={user} />

          <Divider />

          {/* Sección de Sesiones Activas */}
          <ActiveSessionsSection />

          <Divider />

          {/* Sección de Passkeys */}
          <PasskeySection />
        </Box>
      </Container>
    </CssVarsProvider>
  );
};
