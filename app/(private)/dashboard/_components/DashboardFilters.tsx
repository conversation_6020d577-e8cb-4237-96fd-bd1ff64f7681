import Sheet from "@mui/joy/Sheet";
import Input from "@mui/joy/Input";
import SearchIcon from "@mui/icons-material/Search";
import IconButton from "@mui/joy/IconButton";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import Modal from "@mui/joy/Modal";
import ModalDialog from "@mui/joy/ModalDialog";
import ModalClose from "@mui/joy/ModalClose";
import Typography from "@mui/joy/Typography";
import Divider from "@mui/joy/Divider";
import Box from "@mui/joy/Box";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import * as React from "react";
import {Button, Select} from "@mui/joy";
import Option from '@mui/joy/Option';
import {useEntity} from "simpler-state";
import {dashboardFilters} from "@/app/_lib/_states/dashboardFilters";

export const DashboardFilters: React.FC<any> = ({changeFilters}) => {
    const [open, setOpen] = React.useState(false);
    const filters: any = useEntity(dashboardFilters);
    const getDefaultValue = (column: string) => {
        return filters[column]?.value && filters[column]?.value != 'NA_FILTER' ? filters[column]?.value : ''
    }

    const getDefaultRangeDate = (column: string) => {
        return filters[column] && filters[column].value ? {
            startDate: filters[column].value.start,
            endDate: filters[column].value.end
        } : {startDate: '', endDate: ''}
    }

    const getIlikeDefaultValue = (column: string) => {
        const value = getDefaultValue(column);
        if (!value) return ''
        return value.replaceAll('%', '');
    }

    const changeRangeDateFilter = (event: any) => {
        changeFilters('record_date', 'between', {start: `${event.startDate} 00:00`, end: `${event.endDate} 23:59`})
    }
    const renderFilters = () => (
        <>
            <FormControl sx={{flex: 1}} size="sm">
                <FormLabel>Search by Lead id</FormLabel>
                <Input
                    onChange={(event) =>
                        changeFilters('lead_id', 'ilike', `%${event.target.value}%`)}
                    defaultValue={getIlikeDefaultValue('lead_id-ilike')}
                    size="sm" placeholder="Search"
                    startDecorator={<SearchIcon/>}/>
            </FormControl>
            <FormControl size="sm">
                <FormLabel>Search by vendor key</FormLabel>
                <Select
                    value={getDefaultValue('vendor_key-eq')}
                    size="sm"
                    placeholder="Filter by vendor key"
                    onChange={(event, value) =>
                        changeFilters('vendor_key', 'eq', value)}
                    slotProps={{button: {sx: {whiteSpace: 'nowrap'}}}}
                >
                    <Option value="">All</Option>
                    <Option value="a87a90e6d32f6af374a7c6fb04f807f4">a87a90e6d32f6af374a7c6fb04f807f4</Option>
                    <Option value="15c96ab5693550dbb1ad7bcce11fbf39">15c96ab5693550dbb1ad7bcce11fbf39</Option>
                    <Option value="71a2c591c6a003f0405d909984f106d2">71a2c591c6a003f0405d909984f106d2</Option>
                    <Option value="94578946868fb1e50c9b1cde6632f55f">94578946868fb1e50c9b1cde6632f55f</Option>
                    <Option value="fd4c69297db19f946b025a669856ddb9">fd4c69297db19f946b025a669856ddb9</Option>
                    <Option value="eb41334b6a660265e169dc9759561000">eb41334b6a660265e169dc9759561000</Option>
                    <Option value="e80efd5c5c6a6020dea658fba51b5f50">e80efd5c5c6a6020dea658fba51b5f50</Option>
                </Select>
            </FormControl>
            <Box sx={{display: 'flex'}}>
                <FormControl size="sm">
                    <FormLabel>Record date Gte</FormLabel>
                    {/*<DateRangeComponent inputStyles={{height: '32px', background: '#fbfcfe', fontSize: '14px'}}*/}
                    {/*                    onApplyFilters={changeRangeDateFilter}*/}
                    {/*                    defaultValues={getDefaultRangeDate('record_date-between')}/>*/}
                </FormControl>
            </Box>
            <Box sx={{display: 'flex'}}>
                <FormControl size="sm">
                    <FormLabel>Amount gte</FormLabel>
                    <Input
                        value={getDefaultValue('amount-gte')}
                        onChange={(event) =>
                            changeFilters('amount', 'gte', event.target.value)}
                        sx={{width: '120px'}}
                        size="sm"
                        placeholder="Amount"
                        startDecorator={'$'}
                    />
                </FormControl>
                <FormControl size="sm" sx={{marginLeft: '10px'}}>
                    <FormLabel>Amount lte</FormLabel>
                    <Input
                        value={getDefaultValue('amount-lte')}
                        onChange={(event) =>
                            changeFilters('amount', 'lte', event.target.value)}
                        sx={{width: '120px'}}
                        size="sm"
                        placeholder="Amount"
                        startDecorator={'$'}
                    />
                </FormControl>
            </Box>
            <FormControl size="sm">
                <FormLabel>Source</FormLabel>
                <Select
                    size="sm"
                    value={getDefaultValue('source_table-eq')}
                    placeholder="Filter by status"
                    onChange={(event, value) =>
                        changeFilters('source_table', 'eq', value)}
                    slotProps={{button: {sx: {whiteSpace: 'nowrap'}}}}
                >
                    <Option value="">All</Option>
                    <Option value="leads">Leads</Option>
                    <Option value="transfers">Transfers</Option>
                </Select>
            </FormControl>
            <FormControl size="sm">
                <FormLabel>Event</FormLabel>
                <Select
                    value={getDefaultValue('event-eq')}
                    size="sm"
                    placeholder="Filter by Event"
                    onChange={(event, value) =>
                        changeFilters('event', 'eq', value)}
                    slotProps={{button: {sx: {whiteSpace: 'nowrap'}}}}
                >
                    <Option value="">All</Option>
                    <Option value="LEAD_INSERT">Lead Insert</Option>
                    <Option value="TRANSFER_INSERT">Transfer Insert</Option>
                    <Option value="RETAINED_INSERT">Retained Insert</Option>
                </Select>
            </FormControl>
        </>
    );

    return (
        <>
            <Sheet
                className="dashboard-search-filters-mobile"
                sx={{
                    display: {xs: 'flex', sm: 'none'},
                    my: 1,
                    gap: 1,
                }}
            >
                <Input
                    size="sm"
                    placeholder="Search"
                    startDecorator={<SearchIcon/>}
                    sx={{flexGrow: 1}}
                />
                <IconButton
                    size="sm"
                    variant="outlined"
                    color="neutral"
                    onClick={() => setOpen(true)}
                >
                    <FilterAltIcon/>
                </IconButton>
                <Modal open={open} onClose={() => setOpen(false)}>
                    <ModalDialog aria-labelledby="filter-modal" layout="fullscreen">
                        <ModalClose/>
                        <Typography id="filter-modal" level="h2">
                            Filters
                        </Typography>
                        <Divider sx={{my: 2}}/>
                        <Sheet sx={{display: 'flex', flexDirection: 'column', gap: 2}}>
                            {renderFilters()}
                            <Button color={'primary'} variant={'outlined'} onClick={() => setOpen(false)}>
                                Close
                            </Button>
                        </Sheet>
                    </ModalDialog>
                </Modal>
            </Sheet>
            <Box
                className="SearchAndFilters-tabletUp"
                sx={{
                    borderRadius: 'sm',
                    py: 2,
                    display: {xs: 'none', sm: 'flex'},
                    flexWrap: 'wrap',
                    gap: 1.5,
                    '& > *': {
                        minWidth: {xs: '120px', md: '160px'},
                    },
                }}
            >
                {renderFilters()}
            </Box>

        </>
    );
}
