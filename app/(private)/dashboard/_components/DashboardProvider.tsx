"use client"
import SideClientAccountingDetails from "@/app/(private)/accounting/_components/SideClientAccountingDetails";
import React, {useEffect, useState} from "react";
import Box from "@mui/joy/Box";
import {DashboardFilters} from "@/app/(private)/dashboard/_components/DashboardFilters";
import Typography from "@mui/joy/Typography";
import _ from "lodash";
import {useEntity} from "simpler-state";
import {dashboardFilters, setDashboardFilters} from "@/app/_lib/_states/dashboardFilters";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import {setLoading} from "@/app/_components/Loading/loadingState";
import {setSidebarRoutes} from "@/app/_lib/_states/sidebarRoutes";
import {formatNumber} from "@/app/_lib/utils/formatNumber";
import {useQuery} from "@tanstack/react-query";
import Loading from "react-loading";
import {settingsOnRouting} from "@/app/_components/utils/settingsOnRouting";
import {DashboardData} from "@/app/_components/queries/DashboardData";

export const DashboardProvider: React.FC<any> = ({
                                                     totalEvents,
                                                     reportTablesConf,
                                                     userMetadata,
                                                     totalEventsWithoutRvn,
                                                     totalAccountingAmount,
                                                     accountingTableName
                                                 }) => {
    const [dashboardTotalEvents, setTotalEvents] = useState(totalEvents);
    const {setFiltersEntity, setDefaultFilters} = createTableEntities(accountingTableName);
    const filters = useEntity(dashboardFilters);

    useEffect(() => {
        settingsOnRouting('dashboard', filters, setFiltersEntity, setDefaultFilters)
        buildTables();
    }, []);

    const buildTables = () => {
        if (reportTablesConf && reportTablesConf.length <= 0) return [];
        const tables = [];
        reportTablesConf.forEach((itemConf: any, index: number) => {
            if (itemConf && itemConf?.summaryReportConf?.select_keys.length > 0) {
                itemConf?.summaryReportConf?.select_keys.forEach((itemSelKey: any) => {
                    const newSumConf = {...itemConf?.summaryReportConf}
                    newSumConf.valueSel = itemSelKey;
                    const tableInfToPush = _.omit(itemConf, "summaryReportConf");
                    tableInfToPush.summaryReportConf = newSumConf
                    tables.push(tableInfToPush);
                });
            }
        });
    };

    const changeFilters = (property: string, operator: string, value: string) => {
        if (value === '' || !value) {
            value = 'NA_FILTER';
        }
        setDashboardFilters({...filters, [`${property}-${operator}`]: {value, filterType: operator}});
    };

    const onGoToAccounting = (card: string) => {
        setLoading(true);
        if (card === 'eventsWithoutRvn') {
            setFiltersEntity({...filters, 'rvn_exp_id-is': {value: null, filterType: 'is'}});
            setSidebarRoutes('accounting');
        }
    };

    const {data, isFetching} = useQuery(DashboardData(filters));

    return (
        <>
            {isFetching ?
                <div className="flex justify-center mt-20">
                    <Loading color={'black'} width={'30px'} height={'30px'} type={'spin'}/>
                </div> :
                <Box sx={{padding: '30px', maxWidth: '1800px'}}>
                    <Box>
                        <Typography level="h2" component="h1">
                            Dashboard
                        </Typography>
                    </Box>
                    <Box>
                        <DashboardFilters changeFilters={changeFilters}/>
                    </Box>
                    <SideClientAccountingDetails
                        totalEvents={formatNumber(dashboardTotalEvents)}
                        dataDashboard={data}
                        totalEventsWithoutRvn={totalEventsWithoutRvn}
                        totalAccountingAmount={totalAccountingAmount}
                        goToAccounting={onGoToAccounting}
                        accountingCardConfig={{
                            showTotalEventsWithoutRvn: true,
                            totalAccountingAmount: true,
                            showTotalEvents: true
                        }}
                    />
                </Box>}
        </>
    );
};
