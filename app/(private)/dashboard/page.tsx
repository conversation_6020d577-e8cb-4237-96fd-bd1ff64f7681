import React from "react";
import {DashboardProvider} from "@/app/(private)/dashboard/_components/DashboardProvider";
import {getColumnSum} from "@/app/_lib/nova/functions/getColumnSum";
import getTotalEventsWithoutRvn from "@/app/_lib/nova/functions/getTotalEventsWithoutRvn";
import {getCount} from "@/app/_lib/nova/functions/getCount";
import {getInitialConfigurationPage} from "@/app/_lib/utils/getInitialConfigurationPage";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";

export default async function DashboardPage() {
    const accountingTableName = 'accounting';
    const user: any = await getUserSession()
    const {metadata} = user || {metadata: null};
    const jsonMetadata = metadata ? JSON.parse(metadata) : null;
    const payloadTotalEvents = {
        filters: {},
        options: {
            skip: 0,
            limit: 1
        },
        collection: 'accounting',
        query: {
            _id: 1
        },
        authMetadata: jsonMetadata
    }

    const {tableConfig: getLeadConf} = await getInitialConfigurationPage("leads")
    const {tableConfig: getTransConf} = await getInitialConfigurationPage("transfers")
    const {tableConfig: getPostBackConf} = await getInitialConfigurationPage("postbacks")
    const repLeadConf = {...getLeadConf, table: "leads"}
    const repTransConf = {...getTransConf, table: "transfers"}
    const repPostBackConf = {...getPostBackConf, table: "postbacks"}

    const reportTablesConf = [repLeadConf, repTransConf, repPostBackConf]

    const {response} = await getCount(payloadTotalEvents)
    const totalEventsWithoutRvn: any = await getTotalEventsWithoutRvn({});
    const totalAccountingAmount: any = await getColumnSum('amount', 'accounting');

    return (
        <>
            <DashboardProvider accountingTableName={accountingTableName}
                               totalEvents={response?.count}
                               reportTablesConf={reportTablesConf}
                               userMetadata={jsonMetadata}
                               totalEventsWithoutRvn={totalEventsWithoutRvn}
                               totalAccountingAmount={totalAccountingAmount}/>
        </>
    )
}
