import React from "react";
import {getInitialConfigurationPage} from "@/app/_lib/utils/getInitialConfigurationPage";
import {TabsReportTableProvider} from "@/app/_components/reports/TabsReportTableProvider";

export default async function RetainersPage() {
    const tableName = 'postbacks';
    let {globalSearchConfig, tableConfig, defaultFilters} = await getInitialConfigurationPage(tableName)
    tableConfig = {...tableConfig, sort: {"date_retained": -1}}

    return (
        <TabsReportTableProvider
            tableName={tableName}
            globalSearchConfig={globalSearchConfig}
            tableConfig={tableConfig}
            defaultFilters={defaultFilters}/>
    );
}
