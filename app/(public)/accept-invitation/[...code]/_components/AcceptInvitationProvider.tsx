'use client';

import React, {useState} from "react";
import {useRouter} from "next/navigation";
import {authClient} from "@/app/_lib/auth/auth-client";
import {<PERSON>, But<PERSON>, Card, Divider, Stack, Typography} from "@mui/joy";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import GroupAddIcon from '@mui/icons-material/GroupAdd';
import CancelIcon from '@mui/icons-material/Cancel';
import DialogTitle from "@mui/joy/DialogTitle";
import {useQuery} from "@tanstack/react-query";
import {InvitationDetails} from "@/app/_components/queries/InvitationDetails";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";
import Image from "next/image";

interface AcceptInvitationProviderProps {
    code: string[];
}

export const AcceptInvitationProvider: React.FC<AcceptInvitationProviderProps> = ({code}) => {
    const router = useRouter();
    const [loading, setLoading] = useState(false);

    const {data: invitationDetails, isFetching, error} = useQuery(InvitationDetails(code[0]));

    const handleAcceptInvitation = async () => {
        try {
            setLoading(true);
            await authClient.organization.acceptInvitation({
                invitationId: code[0]
            });
            showSuccess("Invitation accepted successfully");
            router.push("/welcome");
        } catch (error) {
            console.error("Error accepting invitation:", error);
            showError("Failed to accept invitation");
        } finally {
            setLoading(false);
        }
    };

    const handleDeclineInvitation = async () => {
        try {
            setLoading(true);
            await authClient.organization.rejectInvitation({
                invitationId: code[0]
            });
            showSuccess("Invitation declined");
            router.push("/");
        } catch (error) {
            console.error("Error declining invitation:", error);
            showError("Failed to decline invitation");
        } finally {
            setLoading(false);
        }
    };

    if (error) {
        return (
            <Card variant="outlined" sx={{
                maxWidth: 400, mx: "auto", mt: 4, p: 3,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
            }}>
                <Image
                    src="/android-chrome-192x192.png"
                    alt="Logo"
                    width={48}
                    height={48}
                    style={{objectFit: 'contain'}}
                    priority
                />
                <DialogTitle sx={{mb: 3}}>AurionX</DialogTitle>
                <Typography level="h4" color="danger" textAlign="center">
                    Invalid or expired invitation
                </Typography>
                <Typography
                    level="body-sm"
                    color="danger"
                    sx={{
                        mt: 1,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5
                    }}>
                    Error: {error instanceof Error ? error.message : 'An error occurred while fetching invitation details'}
                </Typography>
                <Button
                    fullWidth
                    color="neutral"
                    onClick={() => router.push("/welcome")}
                    sx={{mt: 2}}
                >
                    Return to Home
                </Button>
            </Card>
        );
    }

    return (<>
        {isFetching ? <LoadingMessage message="Loading Invitation..."/> :
            <Card variant="outlined" sx={{maxWidth: 400, mx: "auto", mt: 4, p: 3}}>
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: 2,
                        marginTop: {xs: 2, sm: 1},
                        flexDirection: 'column',
                    }}>
                    <Image
                        src="/android-chrome-192x192.png"
                        alt="Logo"
                        width={48}
                        height={48}
                        style={{objectFit: 'contain'}}
                        priority
                    />
                    <DialogTitle sx={{mb: 3}}>AurionX</DialogTitle>
                    <Typography level="h4" textAlign="center" mb={2}>
                        Organization Invitation
                    </Typography>

                    {invitationDetails && (
                        <Stack spacing={2}>
                            <Box
                                sx={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    flexDirection: 'column',
                                    textAlign: 'center',
                                    width: '100%'
                                }}>
                                <Typography level="body-md">
                                    You have been invited to join the organization: {" "}
                                    <strong>{invitationDetails.organizationName}</strong>
                                    {" "}of user:{" "}
                                    <strong>{invitationDetails.inviterEmail}</strong>
                                </Typography>

                                <Typography level="body-sm" color="neutral" sx={{mt: 2}}>
                                    Invited by: {invitationDetails?.email}
                                </Typography>
                            </Box>

                            <Stack direction="row" spacing={2} sx={{mt: 3}}>
                                <Button
                                    fullWidth
                                    color="success"
                                    startDecorator={<GroupAddIcon/>}
                                    onClick={handleAcceptInvitation}
                                    loading={loading}
                                >
                                    Accept
                                </Button>
                                <Button
                                    fullWidth
                                    color="danger"
                                    variant="soft"
                                    startDecorator={<CancelIcon/>}
                                    onClick={handleDeclineInvitation}
                                    loading={loading}
                                >
                                    Decline
                                </Button>
                            </Stack>
                        </Stack>
                    )}
                </Box>
            </Card>}
    </>);
};
