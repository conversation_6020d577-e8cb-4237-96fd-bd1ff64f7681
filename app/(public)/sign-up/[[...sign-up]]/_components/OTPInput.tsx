'use client';

import React, {useEffect, useRef, useState} from 'react';
import {Box, CircularProgress, Input, Typography} from '@mui/joy';
import {authClient} from "@/app/_lib/auth/auth-client";
import {styledName} from "@/app/_components/utils/styledName";
import {useRouter} from "next/navigation";
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

interface OTPInputProps {
    length?: number;
    email: string;
}

export const OTPInput: React.FC<OTPInputProps> = ({length = 6, email}) => {
    const router = useRouter();
    const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
    const [errorOTP, setErrorOTP] = useState("");
    const [loadingAuth, setLoadingAuth] = useState(false);
    const [stepOtp, setStepOtp] = useState<string[]>(new Array(length).fill(''));
    const [attemptCount, setAttemptCount] = useState(0);
    const maxAttempts = 3;

    useEffect(() => {
        if (inputRefs.current[0]) {
            inputRefs.current[0].focus();
        }
    }, []);

    const handleChange = (index: number, value: string) => {
        if (!/^[0-9]?$/.test(value)) return;

        const newOtp = [...stepOtp];
        newOtp[index] = value;
        setStepOtp(newOtp);
        setErrorOTP("");

        if (value && index < length - 1) {
            inputRefs.current[index + 1]?.focus();
        }

        if (newOtp.every(digit => digit !== '')) {
            handleVerifyOTP(newOtp.join(''));
        }
    };

    const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Backspace') {
            if (!stepOtp[index] && index > 0) {
                inputRefs.current[index - 1]?.focus();
            } else {
                const newOtp = [...stepOtp];
                newOtp[index] = '';
                setStepOtp(newOtp);
            }
        } else if (e.key === 'ArrowLeft' && index > 0) {
            inputRefs.current[index - 1]?.focus();
        } else if (e.key === 'ArrowRight' && index < length - 1) {
            inputRefs.current[index + 1]?.focus();
        }
    };

    const handlePaste = (e: React.ClipboardEvent) => {
        e.preventDefault();
        const pasteData = e.clipboardData.getData('text').trim().slice(0, length);

        if (/^\d+$/.test(pasteData)) {
            const newOtp = [...pasteData.split('').slice(0, length)];
            while (newOtp.length < length) newOtp.push('');

            setStepOtp(newOtp);
            handleVerifyOTP(newOtp.join(''));

            const lastIndex = Math.min(pasteData.length, length) - 1;
            if (lastIndex >= 0) {
                inputRefs.current[lastIndex]?.focus();
            }
        }
    };

    const handleVerifyOTP = async (otp: string) => {
        if (loadingAuth || !otp || otp.length !== length) return;
        if (attemptCount >= maxAttempts) {
            setErrorOTP("Maximum attempts reached. Please request a new code.");
            return;
        }

        setLoadingAuth(true);
        setErrorOTP("");

        try {
            const {data, error} = await authClient.signIn.emailOtp({
                email,
                otp
            });

            if (error) {
                console.error("Error verifying OTP:", error);
                const errorMessage = styledName(error.message || "Invalid verification code");
                setErrorOTP(errorMessage);
                setAttemptCount(prev => prev + 1);

                setStepOtp(new Array(length).fill(''));
                inputRefs.current[0]?.focus();
                return;
            }
            router.push("/welcome");
        } catch (error) {
            console.error("Error verifying OTP:", error);
            setLoadingAuth(false);
            setErrorOTP("An unexpected error occurred. Please try again.");
            setStepOtp(new Array(length).fill(''));
            inputRefs.current[0]?.focus();
        }
    };

    return (
        <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2}}>
            <Box sx={{
                display: 'flex',
                gap: 1,
                opacity: loadingAuth ? 0.7 : 1,
                transition: 'opacity 0.2s'
            }}>
                {stepOtp.map((digit, index) => (
                    <Input
                        key={index}
                        value={digit}
                        onChange={(e) => handleChange(index, e.target.value)}
                        onKeyDown={(e) => handleKeyDown(index, e)}
                        onPaste={handlePaste}
                        disabled={loadingAuth}
                        slotProps={{
                            input: {
                                // @ts-ignore
                                ref: (el) => (inputRefs.current[index] = el),
                                inputMode: "numeric",
                                pattern: "[0-9]*",
                                maxLength: 1,
                                "aria-label": `Digit ${index + 1} of verification code`
                            },
                        }}
                        sx={{
                            width: 45,
                            height: 45,
                            textAlign: 'center',
                            '& input': {
                                textAlign: 'center',
                                fontSize: '1.2rem',
                                padding: 0
                            },
                            '&:focus-within': {
                                borderColor: 'primary.500',
                                boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.2)'
                            }
                        }}
                    />
                ))}
            </Box>

            {loadingAuth && (
                <CircularProgress size="sm"/>
            )}

            {errorOTP && (
                <Typography
                    level="body-sm"
                    color="danger"
                    startDecorator={<ErrorOutlineIcon/>}
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5
                    }}
                >
                    {errorOTP}
                </Typography>
            )}

            {attemptCount > 0 && (
                <Typography level="body-sm" color="neutral">
                    Attempts remaining: {maxAttempts - attemptCount}
                </Typography>
            )}
        </Box>
    );
};
