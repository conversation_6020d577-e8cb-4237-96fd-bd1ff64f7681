'use client';

import React, {useEffect, useState} from "react";
import { Box, Button, Input, Sheet, Typography } from "@mui/joy";
import { useRouter } from "next/navigation";
import Image from 'next/image';
import {loading, setLoading} from "@/app/_components/Loading/loadingState";
import DialogTitle from "@mui/joy/DialogTitle";
import { OTPInput } from "@/app/(public)/sign-up/[[...sign-up]]/_components/OTPInput";
import { authClient } from "@/app/_lib/auth/auth-client";
import { styledName } from "@/app/_components/utils/styledName";
import EmailIcon from '@mui/icons-material/Email';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import PersonAddIcon from '@mui/icons-material/PersonAdd';

export default function CustomSignUpProvider() {
    const router = useRouter();
    const [email, setEmail] = useState("");
    const [step, setStep] = useState("email");
    const [errorMail, setErrorMail] = useState("");
    const [loadingMail, setLoadingMail] = useState(false);

    useEffect(() => {
        if (loading.get()) setLoading(false)
    }, [])

    const handleSignIn = () => {
        setLoading(true);
        router.push("/sign-in");
    }

    const handleSendOTP = async () => {
        if (!email || !email.includes('@')) {
            setErrorMail("Please enter a valid email address");
            return;
        }

        try {
            setLoadingMail(true);
            setErrorMail("");

            const { data, error } = await authClient.emailOtp.sendVerificationOtp({
                email,
                type: "sign-in"
            });

            if (error) {
                console.error("Error sending OTP:", error);
                const getError = styledName(error.message || "Error sending code");
                setErrorMail(getError);
                return;
            }

            setStep("otp");
        } catch (error) {
            console.error("Error sending OTP:", error);
            setErrorMail("An unexpected error occurred");
        } finally {
            setLoadingMail(false);
        }
    };

    const onKeyUp = (event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter') {
            handleSendOTP();
        }
    }

    const handleBack = () => {
        setStep("email");
        setErrorMail("");
    };

    return (
        <Box sx={{
            width: '100%',
            height: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.level1'
        }}>
            <Sheet
                sx={{
                    width: { xs: '90%', sm: 400 },
                    maxWidth: 400,
                    p: 4,
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: 2,
                    borderRadius: "xl",
                    boxShadow: "lg",
                    bgcolor: "background.surface",
                }}>
                <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', position: 'relative' }}>
                    {step === "otp" && (
                        <Button
                            variant="plain"
                            color="neutral"
                            startDecorator={<ArrowBackIcon />}
                            onClick={handleBack}
                            sx={{ position: 'absolute', left: 0 }}
                        >
                            Back
                        </Button>
                    )}
                    <Image
                        src="/android-chrome-192x192.png"
                        alt="Logo"
                        width={48}
                        height={48}
                        style={{ objectFit: 'contain' }}
                        priority
                    />
                </Box>

                <DialogTitle level="h3">AurionX</DialogTitle>

                <Typography level="h4" sx={{ fontWeight: "bold", textAlign: 'center' }}>
                    {step === "email" ? "Create a new account" : "Enter verification code"}
                </Typography>

                <Typography level="body-sm" sx={{ textAlign: 'center', mb: 1 }}>
                    {step === "email" ? (
                        <>
                            Already have an account?{" "}
                            <Typography
                                component="a"
                                color="neutral"
                                fontWeight="bold"
                                sx={{ cursor: 'pointer' }}
                                onClick={handleSignIn}>
                                Sign in
                            </Typography>
                        </>
                    ) : (
                        `We've sent a verification code to ${email}`
                    )}
                </Typography>

                {step === 'email' ? (
                    <>
                        <Input
                            placeholder="Enter your email"
                            fullWidth
                            disabled={loadingMail}
                            error={!!errorMail}
                            onKeyUp={onKeyUp}
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            startDecorator={<EmailIcon />}
                            sx={{ mt: 2 }}
                        />
                        <Button
                            variant="solid"
                            color="neutral"
                            fullWidth
                            size="lg"
                            onClick={handleSendOTP}
                            loading={loadingMail}
                            startDecorator={<PersonAddIcon />}
                        >
                            Create Account
                        </Button>
                    </>
                ) : (
                    <Box sx={{ width: '100%', mt: 2 }}>
                        <OTPInput email={email} />
                    </Box>
                )}

                {errorMail && (
                    <Typography
                        level="body-sm"
                        color="danger"
                        sx={{
                            mt: 1,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5
                        }}
                    >
                        {errorMail}
                    </Typography>
                )}
            </Sheet>
        </Box>
    );
}
