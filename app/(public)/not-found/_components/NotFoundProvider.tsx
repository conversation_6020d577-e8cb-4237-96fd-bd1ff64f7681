'use client'

import {<PERSON>, Button, Typography} from "@mui/joy";
import React, {useEffect, useState} from "react";
import DoNotDisturbAltOutlinedIcon from '@mui/icons-material/DoNotDisturbAltOutlined';
import EastOutlinedIcon from '@mui/icons-material/EastOutlined';
import {loading, setLoading} from "@/app/_components/Loading/loadingState";
import MailOutlineOutlinedIcon from '@mui/icons-material/MailOutlineOutlined';
import {useRouter, useSearchParams} from "next/navigation";
import _ from "lodash";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";

export const NotFoundProvider: React.FC<any> = () => {
    const [mailSent, setMailSent] = useState<boolean>(false);
    const [showNav, setShowNav] = useState<boolean>(false);
    const router = useRouter();
    const searchParams = useSearchParams();
    const from = searchParams.get("from");
    const reason = searchParams.get("reason");
    const isNotFound = !from && !reason;

    useEffect(() => {
        if (loading.get()) setLoading(false)
    }, []);

    const sendSupportEmail = async () => {
        setLoading(true)
        const response = await fetch("/api/betterAuth/sendMailRequestAccess", {
            method: 'POST',
            body: JSON.stringify({url: from || window.location.pathname}),
        });
        if (response.ok) {
            showSuccess('Mail Send, please wait for response');
        } else {
            showError("Something wrong happened, please retry later")
        }
        setMailSent(true)
        setShowNav(true)
        setLoading(false)
    }

    const handleRedirect = () => {
        const urlToPush = (!_.isEmpty(from)) ? _.replace(from, "/", "") : "welcome"
        router.push(urlToPush);
    };

    const getTitle = () => {
        if (isNotFound) return "Page Not Found";
        return "No Access";
    }

    const getMessage = () => {
        if (isNotFound) {
            return "The page you are looking for doesn't exist or has been moved.";
        }
        return "It looks like you don't have access to this page. An email will be sent to our support team to grant you access.";
    }

    return (
        <Box
            sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '80vh'
            }}>
            <Box sx={{
                maxWidth: 400,
                mx: 'auto',
                p: 2,
                border: '1px solid #ddd',
                borderRadius: 8,
                backgroundColor: '#fafafa',
                textAlign: 'center'
            }}>
                <DoNotDisturbAltOutlinedIcon sx={{color: 'gray', fontSize: '100px', marginBottom: '10px'}}/>
                <Typography level="h4" mb={2}>
                    {getTitle()}
                </Typography>
                <Typography level="body-lg" mb={3}>
                    {getMessage()}
                </Typography>
                {reason && (<>
                    <Typography level="body-sm" color="danger">
                        Reason:
                    </Typography>
                    <Typography level="body-sm" color="danger" mb={3}>
                        {reason}
                    </Typography>
                </>)}
                <Button
                    startDecorator={<MailOutlineOutlinedIcon/>}
                    onClick={sendSupportEmail}
                    variant="solid"
                    color='neutral'
                    sx={{width: '100%'}}>
                    {(mailSent) ? "Mail sent" : "Send Email to Support"}
                </Button>
                {showNav && <>
                    <Typography level="body-sm" mt={3} mb={3}>
                        {isNotFound 
                            ? "Return to a known page:" 
                            : "If you confirm the permissions already sets, you can go to your page."}
                    </Typography>
                    <Button onClick={handleRedirect} variant="soft" color='neutral'>
                        <EastOutlinedIcon/>
                    </Button>
                </>}
            </Box>
        </Box>
    )
}
