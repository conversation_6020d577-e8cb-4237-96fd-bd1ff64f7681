'use server'
import {getHost} from "@/app/_lib/utils/getHost.js";

export const getDataAction = async (payload) => {
    const token = ""
    try {
        const cfHost = getHost()
        const response = await fetch(`${cfHost}/api/mongo/getItems`, {
            method: 'POST',
            body: JSON.stringify(payload),
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
            },
        });
        const getItemsResult = await response.json();
        return getItemsResult.items || []
    } catch (e) {
        console.error("Error: ", e)
        return []
    }
}
