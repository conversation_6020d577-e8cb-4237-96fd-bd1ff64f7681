import Box from "@mui/joy/Box";
import * as React from "react";
import Loading from "react-loading";
import {loading} from "@/app/_components/Loading/loadingState";
import {useEntity} from "simpler-state";

export const ConversionFinderLoading = () => {
    const loadingStyles = {
        backgroundColor: '#f5f5f5',
        width: '100%',
        height: '100%',
        position: 'absolute',
        zIndex: 9998,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        opacity: '.9'
    }
    const loadingPage = useEntity(loading);
    return (<>
        {loadingPage && <Box sx={loadingStyles}>
            <Loading color={'black'} width={'30px'} height={'30px'} type={'spin'}/>
        </Box>}

    </>);
}
