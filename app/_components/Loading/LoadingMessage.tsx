import React from "react";
import {Typography} from "@mui/joy";
import Loading from "react-loading";
import {useEntity} from "simpler-state";
import {loading} from "@/app/_components/Loading/loadingState";

export const LoadingMessage: React.FC<any> = ({ message }) => {
    const generalLoading = useEntity(loading)

    return (
        <div className="flex flex-col items-center justify-center p-10 text-center">
            {!generalLoading && <Loading color={'black'} width={'30px'} height={'30px'} type={'spin'} />}
            {typeof message === 'string' ? (
                message.split('\n').map((line: string, i: number) => (
                    <Typography key={i} level="body-sm" mb={0.5} fontWeight="bold">
                        {line}
                    </Typography>
                ))
            ) : (
                message
            )}
        </div>
    )
}
