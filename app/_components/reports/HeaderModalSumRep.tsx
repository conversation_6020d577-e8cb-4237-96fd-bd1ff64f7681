import React, {useEffect, useState} from "react";
import {Autocomplete, AutocompleteOption, Grid, Option, Select, Typography} from "@mui/joy";
import {styledName} from "@/app/_components/utils/styledName";
import {DrawerDownloadReport} from "@/app/_components/reports/DrawerDownloadReport";
import _ from "lodash";
import {
    detailValueSelected,
    setDetailValueSelected,
    setThirdLevelSelected,
    setVendorLeadsSelected,
    thirdLevelSelected
} from "@/app/_components/table/states/changeTabSummaryState";
import {useEntity} from "simpler-state";
import Box from "@mui/joy/Box";
import {DateRangeComponent} from "@/app/_components/table/filters/DateRangeComponent";
import {DatePickerSummary} from "@/app/_components/reports/DatePickerSummary";
import {setTableNameForEdit} from "@/app/_components/table/states/adminEditStates";

const stringifyValue = (value: any) => JSON.stringify(value);

export const HeaderModalSumRep: React.FC<any> = ({
                                                     table,
                                                     allVendors,
                                                     summaryRes,
                                                     loadingBtn,
                                                     downloadCSV,
                                                     valueSel,
                                                     handleChangePrincipal,
                                                     changeRangeDateFilter,
                                                     defaultRangeDate,
                                                     selectKeys,
                                                     userRestrictions,
                                                     userRole
                                                 }) => {
    const detailValueSel = useEntity(detailValueSelected);
    const thirdLvlValSel = useEntity(thirdLevelSelected);
    const [optPrincipal, setOptPrincipal] = useState([]);
    const [optDetails, setOptDetails] = useState([]);
    const [opt3rdLevel, setOpt3rdLevel] = useState([]);
    const [selectedValue, setSelectedValue] = useState({key: "none", show: "None", value: "none"});
    const [detailsSel, setDetailsSel] = useState(null);
    const [thirdLevelSel, setThirdLevelSel] = useState(null);

    useEffect(() => {
        setOptsValues();
    }, [valueSel]);

    const findDefaultOption = (options: any, defaultValue = 'none') => {
        return _.find(options, (opt) => opt.value === defaultValue) ?? null;
    };

    const setSelectedOption = (options: any, setOption: any, defaultValue = 'none') => {
        const defaultOption = findDefaultOption(options, defaultValue);
        setOption(defaultOption);
        return defaultOption;
    };

    const setOptsValues = () => {
        let getDrillDownOpts = !_.isEmpty(valueSel?.drill_down) ? valueSel?.drill_down : [];
        if (optPrincipal.length < 2) {
            const drillDownOpt = ["none", "dates", "campaign_key"];
            getDrillDownOpts = getDrillDownOpts.filter((item: any) => _.includes(drillDownOpt, item.value));
        }
        if (userRole !== "admin") {
            const onlyAdminOpt = ["pub_id"];
            getDrillDownOpts = getDrillDownOpts.filter((item: any) => !_.includes(onlyAdminOpt, item.fKey));
        }

        const getValueDef = (detailValueSel?.value !== "none") ? detailValueSel?.value : "none";
        const getItmVal = findDefaultOption(getDrillDownOpts, getValueDef);
        const restrictedPrincipalOpt = selectKeys.filter((item: any) =>
            !item.foreign || userRestrictions[item.foreign] !== false) || [];
        setOptPrincipal([{key: "none", show: "None", value: "none"}, ...restrictedPrincipalOpt]);
        setDetailsSel(getItmVal ?? null);
        setOptDetails(getDrillDownOpts);

        if (getValueDef !== "none" && !_.isEmpty(getItmVal?.details)) {
            const getItm3rdLvl = _.find(getItmVal.details, (infDP3) => infDP3?.value === thirdLvlValSel?.value);
            setOpt3rdLevel(getItmVal.details);
            setThirdLevelSel(getItm3rdLvl ?? null);
        } else {
            setOpt3rdLevel([]);
            setThirdLevelSel(null);
        }
    };

    const onChangePrincipal = (event: any, newValue: any) => {
        const parsedValue = JSON.parse(newValue);
        setSelectedValue(parsedValue);
        handleChangePrincipal(event, parsedValue);
        setOptsValues()
    };

    const onChangeDetails = (event: any, newDetails: any) => {
        if (!_.isEmpty(newDetails)) {
            setDetailsSel(newDetails);
            setDetailValueSelected(newDetails);

            const new3rdLevelOptions = newDetails?.details ?? [];
            setOpt3rdLevel(new3rdLevelOptions);
            setSelectedOption(new3rdLevelOptions, setThirdLevelSel);
            setTableNameForEdit("")
            setVendorLeadsSelected({})
            setThirdLevelSelected({label: "None", key: null, value: "none"});
        }
    };

    const onChange3rdLevel = (event: any, new3rdLevel: any) => {
        if (!_.isEmpty(new3rdLevel)) {
            setThirdLevelSel(new3rdLevel);
            setThirdLevelSelected(new3rdLevel);
        }
    };

    const handleChangeAC = (
        event: React.SyntheticEvent | null,
        objSel: any | null
    ) => {
        let valVendorSel = {}
        if (!_.isEmpty(objSel)) {
            valVendorSel = {
                'vendor_id-eq': {
                    value: objSel?._id,
                    filterType: "eq"
                }
            }
        }
        setTableNameForEdit(objSel?._id)
        setVendorLeadsSelected(valVendorSel)
    };

    return (
        <>
            {(summaryRes && summaryRes.length > 0) &&
                <DrawerDownloadReport onDownload={downloadCSV}/>}
            <Grid container spacing={2} sx={{width: '100%'}}>
                <Grid xs={12}
                      md={(!_.isEmpty(opt3rdLevel)) ? 3 : (!_.isEmpty(optDetails)) ? 4 : 6}
                      lg={(!_.isEmpty(opt3rdLevel)) ? 3 : (!_.isEmpty(optDetails)) ? 4 : 6}>
                    <div className="flex flex-col items-center">
                        {defaultRangeDate.startDate && (<>
                            <Typography level="body-md">
                                Date
                            </Typography>
                            {(detailValueSel?.key !== "date") ?
                                <Box sx={{mt: '5px'}}>
                                    <DateRangeComponent
                                        inputStyles={{
                                            height: '32px',
                                            background: '#fbfcfe',
                                            fontSize: '14px'
                                        }}
                                        hiddeClear={true}
                                        onApplyFilters={changeRangeDateFilter}
                                        defaultValues={defaultRangeDate}
                                        isLoading={loadingBtn}
                                    />
                                </Box> :
                                <DatePickerSummary
                                    onApplyFilters={changeRangeDateFilter}
                                    isLoading={loadingBtn}
                                />}
                        </>)}
                    </div>
                </Grid>
                {(optPrincipal && optPrincipal.length > 0) && <>
                    <Grid xs={12}
                          md={(!_.isEmpty(optDetails)) ? 3 : 4}
                          lg={(!_.isEmpty(optDetails)) ? 3 : 4}>
                        {(table === "leads" && detailValueSel?.value !== 'none') ?
                            <div className="flex flex-col items-center">
                                <Typography level="body-md">
                                    Select Vendor
                                </Typography>
                                <Autocomplete
                                    options={allVendors.map((option: any) => {
                                        return {label: option.vendor, ...option}
                                    })}
                                    disabled={loadingBtn}
                                    placeholder="Select or search"
                                    freeSolo
                                    onChange={handleChangeAC}
                                    sx={{height: "32px", fontSize: '12px', mt: '5px'}}
                                    renderOption={(props: any, option: any) => {
                                        const {key, ...rest} = props;
                                        return <AutocompleteOption key={key}  {...rest}>
                                            <Typography level="body-xs">
                                                {option.label}
                                            </Typography>
                                        </AutocompleteOption>
                                    }}
                                />
                            </div> :
                            <div className="flex flex-col items-center">
                                <Typography level="body-md">
                                    View by
                                </Typography>
                                <Select
                                    value={stringifyValue(selectedValue)}
                                    disabled={loadingBtn}
                                    placeholder="Select key"
                                    onChange={onChangePrincipal}
                                    sx={{fontSize: '12px', mt: '5px'}}
                                    slotProps={{listbox: {sx: {width: '100%'}}}}>
                                    {optPrincipal.map((itm: any, index: number) => (
                                        <Option key={index} sx={{fontSize: '12px'}} value={stringifyValue(itm)}>
                                            {styledName(itm?.show)}
                                        </Option>
                                    ))}
                                </Select>
                            </div>}
                    </Grid>
                    {(optDetails.length > 0) &&
                        <Grid xs={12}
                              md={(!_.isEmpty(opt3rdLevel)) ? 3 : 4}
                              lg={(!_.isEmpty(opt3rdLevel)) ? 3 : 4}>
                            <div className="flex flex-col items-center">
                                <Typography level="body-md">
                                    View 2nd Level
                                </Typography>
                                <Select
                                    value={detailsSel}
                                    disabled={loadingBtn}
                                    placeholder="Select details"
                                    onChange={onChangeDetails}
                                    sx={{fontSize: '12px', mt: '5px'}}
                                    slotProps={{listbox: {sx: {width: '100%'}}}}>
                                    {optDetails.map((itm: any, index: number) => (
                                        <Option key={index} sx={{fontSize: '12px'}} value={itm}>
                                            {itm?.label}
                                        </Option>
                                    ))}
                                </Select>
                            </div>
                        </Grid>}
                    {(opt3rdLevel.length > 0) &&
                        <Grid xs={12}
                              md={3}
                              lg={3}>
                            <div className="flex flex-col items-center">
                                <Typography level="body-md">
                                    View 3rd Level
                                </Typography>
                                <Select
                                    value={thirdLevelSel}
                                    disabled={loadingBtn}
                                    placeholder="Select the 3rd level"
                                    onChange={onChange3rdLevel}
                                    sx={{fontSize: '12px', mt: '5px'}}
                                    slotProps={{listbox: {sx: {width: '100%'}}}}>
                                    {opt3rdLevel.map((itm: any, index: number) => (
                                        <Option key={index} sx={{fontSize: '12px'}} value={itm}>
                                            {itm?.label}
                                        </Option>
                                    ))}
                                </Select>
                            </div>
                        </Grid>}
                </>}
            </Grid>
        </>
    );
}
