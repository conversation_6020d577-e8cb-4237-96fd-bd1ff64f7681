import React, {useEffect, useState} from "react";
import {Box, Grid, Option, Select, Typography} from "@mui/joy";
import {styledName} from "@/app/_components/utils/styledName";
import _ from "lodash";
import {useQuery} from "@tanstack/react-query";
import {TableSummaryReport} from "./TableSummaryReport";
import {MatchReport} from "@/app/_components/queries/MatchReport";
import {ProgressLoadingReport} from "@/app/_components/reports/ProgressLoadingReport";

export const MatchCountReportComponent: React.FC<any> = ({
                                                             table,
                                                             matchRepConf,
                                                             dataSelects
                                                         }) => {
    const [countOptions, setCountOptions] = useState(matchRepConf ?? []);
    const [optionSelected, setOptionSelected] = useState(matchRepConf[0] ?? null);
    const [opts2ndLvl, setOpts2ndLvl] = useState(dataSelects[optionSelected?.foreign]);
    const [name2ndLevel, setName2ndLevel] = useState(optionSelected?.f_key2)
    const [optionDetail, setOptionDetail] = useState(matchRepConf[0]?.drill_down ?? null);
    const [valueToMatch, setValueToMatch] = useState(null);
    const [partialChunks, setPartialChunks] = useState<any[]>([]);
    const [fetchProgress, setFetchProgress] = useState<number>(0);
    const [progressLabel, setProgressLabel] = useState<string>('');

    useEffect(() => {
        setPartialChunks([]);
    }, [valueToMatch, optionSelected]);

    const onChangePrincipal = (event: any, newValue: any) => {
        if (!_.isEmpty(newValue)) {
            setOpts2ndLvl(dataSelects[newValue?.foreign])
            setName2ndLevel(newValue?.f_key2)
            setOptionSelected(newValue)
            setOptionDetail(newValue?.drill_down)
        }
    }

    const onChangeValueMatch = (event: any, newMatch: any) => {
        if (!_.isEmpty(newMatch)) {
            setValueToMatch(newMatch)
        }
    }

    const reFetch = () => {
        refetch()
    }

    const {data, isFetching, refetch} = useQuery(
        MatchReport(
            table,
            valueToMatch,
            optionSelected,
            (updater: (prev: any[]) => any[]) => {
                setPartialChunks((prev) => updater(prev));
            },
            {
                onProgressUpdate: (percent, label) => {
                    setFetchProgress(percent);
                    setProgressLabel(label);
                }
            }
        )
    );

    const sxBox = (isStreaming: boolean) => {
        return {
            mt: '25px',
            overflow: (isStreaming) ? 'hidden' : 'scroll',
            height: {
                xs: '75vh',
                sm: '75vh',
                md: '80vh',
                lg: '85vh'
            },
            width: {
                xs: '90%',
                sm: '97%',
                md: '98%',
                lg: '99%'
            },
            position: 'relative',
            '@media (max-width: 600px)': {
                width: '95%'
            }
        }
    }

    return (<>
        {!_.isEmpty(countOptions) &&
            <Grid container spacing={2}>
                <Grid xs={12}
                      md={!_.isEmpty(name2ndLevel) ? 6 : 12}
                      lg={!_.isEmpty(name2ndLevel) ? 6 : 12}>
                    <div className="flex flex-col items-center">
                        <Typography level="body-md">
                            View by
                        </Typography>
                        <Select
                            value={optionSelected}
                            disabled={isFetching}
                            placeholder="Select key"
                            onChange={onChangePrincipal}
                            sx={{fontSize: '12px', mt: '5px'}}
                            slotProps={{listbox: {sx: {width: '100%'}}}}>
                            {countOptions.map((itm: any, index: number) => (
                                <Option key={index} sx={{fontSize: '12px'}} value={itm}>
                                    {styledName(itm?.show)}
                                </Option>
                            ))}
                        </Select>
                    </div>
                </Grid>
                {!_.isEmpty(name2ndLevel) &&
                    <Grid xs={12}
                          md={6}
                          lg={6}>
                        <div className="flex flex-col items-center">
                            <Typography level="body-md">
                                Select {styledName(name2ndLevel)}
                            </Typography>
                            <Select
                                disabled={isFetching}
                                placeholder={styledName(name2ndLevel)}
                                onChange={onChangeValueMatch}
                                sx={{fontSize: '12px', mt: '5px'}}
                                slotProps={{listbox: {sx: {width: '100%'}}}}>
                                {opts2ndLvl.map((itm: any, index: number) => (
                                    <Option key={index} sx={{fontSize: '12px'}} value={itm}>
                                        {styledName(itm[name2ndLevel])}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                    </Grid>}
            </Grid>}
        {/*<Box sx={{*/}
        {/*    display: 'flex',*/}
        {/*    justifyContent: 'center',*/}
        {/*    width: {*/}
        {/*        xs: '90%',*/}
        {/*        md: 'auto'*/}
        {/*    }*/}
        {/*}}>*/}
        {/*    <Button variant='outlined' color='neutral'*/}
        {/*            sx={{height: '30px', marginLeft: '10px'}} onClick={reFetch}>*/}
        {/*        Re Fetch Data*/}
        {/*    </Button>*/}
        {/*</Box>*/}
        {(!_.isEmpty(data) && !_.isEmpty(optionSelected)) ?
            <Box sx={sxBox(false)}>
                <TableSummaryReport principalValueSel={optionSelected} detailValueSel={optionDetail}
                                    optionsCond={null} summaryRes={data} tableName={table}
                                    thirdLevelSel={null} requiredKey={valueToMatch[name2ndLevel]} isStreaming={false}
                                    isMatchRep={true} isPerformance={false}/>
            </Box> : (isFetching) ?
                <Box sx={sxBox(true)}>
                    <ProgressLoadingReport progressLabel={progressLabel} fetchProgress={fetchProgress}/>
                    <TableSummaryReport principalValueSel={optionSelected} detailValueSel={optionDetail}
                                        optionsCond={null} summaryRes={partialChunks} tableName={table}
                                        thirdLevelSel={null} requiredKey={valueToMatch[name2ndLevel]} isStreaming={true}
                                        isMatchRep={true} isPerformance={false}/>
                </Box> :
                (!_.isEmpty(valueToMatch)) ?
                    <div className="flex justify-center mt-10">
                        <Typography level="title-lg">
                            No data to display.
                        </Typography>
                    </div> :
                    <div className="flex justify-center mt-10">
                        <Typography level="title-lg">
                            Please select a {styledName(name2ndLevel)} option.
                        </Typography>
                    </div>}
    </>)
}
