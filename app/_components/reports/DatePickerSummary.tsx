import {Option, Select, Stack} from "@mui/joy";
import React, {useEffect, useState} from "react";
import {DateTime} from "luxon";
import _ from "lodash";

export const DatePickerSummary: React.FC<any> = ({onApplyFilters, isLoading}) => {
    const currentMonth = DateTime.now().month;
    const currentYear = DateTime.now().year;
    const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December',
    ];
    const years = _.range(2020, 2031);
    const [selectedMonth, setSelectedMonth] = useState(currentMonth);
    const [selectedYear, setSelectedYear] = useState(currentYear);

    useEffect(() => {
        updateDateRange(selectedYear, selectedMonth);
    }, [selectedMonth, selectedYear]);

    const updateDateRange = (year: number, month: number) => {
        const startDate = DateTime.local(year, month, 1).toISODate();
        const endDate = DateTime.local(year, month).endOf('month').toISODate();
        onApplyFilters({startDate, endDate})
    };

    return (
        <Stack spacing={2} direction="row" sx={{mt: '5px'}}>
            <Select
                placeholder="Select Month"
                value={selectedMonth}
                disabled={isLoading}
                sx={{fontSize: '12px', mt: '5px'}}
                slotProps={{listbox: {sx: {width: '100%'}}}}
                onChange={(e, valueM) => setSelectedMonth(valueM)}>
                {months.map((month, index) => (
                    <Option key={index} sx={{fontSize: '12px'}} value={index + 1}>
                        {month}
                    </Option>
                ))}
            </Select>
            <Select
                placeholder="Select Year"
                value={selectedYear}
                disabled={isLoading}
                sx={{fontSize: '12px', mt: '5px'}}
                slotProps={{listbox: {sx: {width: '100%'}}}}
                onChange={(e, valueY) => setSelectedYear(valueY)}>
                {years.map((year, index) => (
                    <Option key={index} sx={{fontSize: '12px'}} value={year}>
                        {year}
                    </Option>
                ))}
            </Select>
        </Stack>
    );
}
