'use client';

import React, {useEffect, useState} from "react";
import Typography from "@mui/joy/Typography";
import {styleNumber} from "@/app/_components/utils/styleNumber";
import _ from "lodash";
import {Table} from "@mui/joy";
import {changeFiltersOnSelSumData} from "@/app/_components/reports/func/changeFiltersOnSelSumData";
import {
    setChangeDefFiltersOnSelSum,
    setIsReportConsult,
    setTabChangeOnSummary
} from "@/app/_components/table/states/changeTabSummaryState";
import {showInfo} from "@/app/_components/alerts/toast/ToastMessages";
import {ModalConsultComponent} from "@/app/_components/reports/ModalConsultComponent";
import {Table1stRowComponent} from "./tableRows/Table1stRowComponent";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import {useQueryClient} from "@tanstack/react-query";
import {useSession} from "@/app/_lib/auth/auth-client";

interface RestrictionsType {
    vendors?: any;
    clients?: any;
}

export const TableSummaryReport: React.FC<any> = ({
                                                      principalValueSel,
                                                      detailValueSel,
                                                      thirdLevelSel,
                                                      optionsCond,
                                                      summaryRes,
                                                      tableName,
                                                      requiredKey,
                                                      isMatchRep,
                                                      isPerformance
                                                  }) => {
    const {data: session, isPending} = useSession();
    const {setFiltersEntity, setDefaultFilters} = createTableEntities(tableName);
    const queryClient = useQueryClient();

    const [openModal, setOpenModal] = useState(false);
    const [rowValue, setRowValue] = useState(null);
    const [addRowValue, setAddRowValue] = useState(null);
    const [itemValue, setItemValue] = useState(null);
    const [valueSearch, setValueSearch] = useState(null);
    const [optionSearch, setOptionSearch] = useState(null);
    const [width1stCol, setWidth1stCol] = useState("40%");
    const [width2ndCol, setWidth2ndCol] = useState<string | null>(null);
    const [width3rdCol, setWidth3rdCol] = useState<string | null>(null);
    const [widthTotalsCol, setWidthTotalsCol] = useState("20%");

    const [shouldDisplayVendor, setShouldDisplayVendor] = useState(true);
    const [shouldDisplayClients, setShouldDisplayClients] = useState(true);
    const [filteredSummaryRes, setFilteredSummaryRes] = useState<any[]>([]);

    const [viewDrillDown, setViewDrillDown] = useState(false);
    const [view3rdLevel, setView3rdLevel] = useState(false);
    const [keyWOId, setKeyWOId] = useState("");
    const [newKeyAdd, setNewKeyAdd] = useState(null);

    useEffect(() => {
        if (isPending || !session) return;

        const {user}: any = session || {user: null};
        const {metadata} = user || {metadata: null};
        const jsonMetadata = metadata ? JSON.parse(metadata) : null;
        const restrictions = jsonMetadata?.restrictions as RestrictionsType;

        setShouldDisplayVendor(restrictions?.vendors !== false);
        setShouldDisplayClients(restrictions?.clients !== false);
    }, [session, isPending]);

    useEffect(() => {
        setViewDrillDown(!_.isEmpty(principalValueSel?.drill_down));
        setView3rdLevel(!_.isEmpty(detailValueSel?.details));
        setNewKeyAdd(detailValueSel?.value ?? null);

        let newKey = "";
        if (!_.isEmpty(principalValueSel?.drill_down)) {
            if (_.includes(detailValueSel?.key, "_id")) {
                newKey = _.replace(detailValueSel?.key, "_id", "");
            } else {
                newKey = detailValueSel?.key ?? "";
            }
        }
        setKeyWOId(detailValueSel?.fKey ?? newKey);
    }, [principalValueSel, detailValueSel]);

    useEffect(() => {
        if (_.isEmpty(summaryRes)) {
            setFilteredSummaryRes([]);
            return;
        }

        const filtered = summaryRes.map((item: any) => {
            const filteredItem = {...item};
            if (!shouldDisplayVendor) delete filteredItem.vendors;
            if (!shouldDisplayClients) delete filteredItem.clients;
            return filteredItem;
        });

        setFilteredSummaryRes(filtered);
    }, [summaryRes, shouldDisplayVendor, shouldDisplayClients]);

    useEffect(() => {
        if (viewDrillDown) {
            setWidth1stCol("15%");
            setWidth2ndCol("25%");
            setWidthTotalsCol("10%");
        }
        if (view3rdLevel) {
            setWidth1stCol("15%");
            setWidth2ndCol("15%");
            setWidth3rdCol("15%");
            setWidthTotalsCol("15%");
        }
    }, [viewDrillDown, view3rdLevel]);

    const selectValue = (row: any, addRow: any, item: any) => {
        if (isPerformance) {
            showInfo("This utility is not available for performance reporting.")
        } else {
            const setRowVal = (row?._id === "total") ? null : row;
            setRowValue(setRowVal);
            setItemValue(item);
            setAddRowValue(addRow);
            const getValueToSearch = (detailValueSel === 'normal') ? ((row?.vendor) ? row?.vendor : row?.client) : (row?.date) ? row?.date : detailValueSel?.value;
            setValueSearch(getValueToSearch);
            setOptionSearch(item?.name || null);
            setOpenModal(true);
        }
    };

    const consultOnTableTab = () => {
        const getFilters = changeFiltersOnSelSumData(tableName, rowValue, addRowValue, itemValue, requiredKey, detailValueSel);
        setChangeDefFiltersOnSelSum(getFilters);
        setFiltersEntity(getFilters)
        setIsReportConsult(true);
        setOpenModal(false);
        setTabChangeOnSummary(1);
        invalidateQuery()
    };

    const invalidateQuery = async () => {
        await queryClient.invalidateQueries({
            queryKey: [`table_data_${tableName}`],
            refetchType: "active"
        });
    };

    const getColSpanCount = () => {
        let count = 0;
        if (viewDrillDown && newKeyAdd !== "none") count += 1;
        count += 1;
        if ((viewDrillDown && newKeyAdd !== "none") && keyWOId) count += 1;
        if ((viewDrillDown && newKeyAdd !== "none") && view3rdLevel && thirdLevelSel?.key) count += 1;
        count += optionsCond?.length || 0;
        if (!isPerformance) count += 1;
        return count;
    };

    return (<>
        <ModalConsultComponent
            open={openModal}
            handleClose={() => setOpenModal(false)}
            handleConfirm={() => consultOnTableTab()}
            tableName={tableName}
            valueSearch={valueSearch}
            optionSearch={optionSearch}
            requiredKey={requiredKey}
        />
        <Table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0 z-10">
            <tr>
                {viewDrillDown && newKeyAdd !== "none" && <th style={{width: "3%"}}></th>}
                {(!viewDrillDown || newKeyAdd === "none") ?
                    <th style={{width: width1stCol}}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {principalValueSel?.show}
                    </th> :
                    <React.Fragment>
                        <th style={{width: width1stCol}}
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {principalValueSel?.show}
                        </th>
                        {keyWOId &&
                            <th style={{width: width2ndCol}}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {keyWOId}
                            </th>}
                        {(view3rdLevel && !_.isEmpty(thirdLevelSel?.key)) &&
                            <th style={{width: width3rdCol}}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {thirdLevelSel?.label}
                            </th>}
                    </React.Fragment>}
                {optionsCond && optionsCond.map((itm: any, index: number) => (
                    <th style={{width: widthTotalsCol}} key={index}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {itm?.name || "Null"}
                    </th>
                ))}
                {!isPerformance &&
                    <th style={{width: widthTotalsCol}}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                    </th>}
            </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
            {(filteredSummaryRes && filteredSummaryRes.length > 0) ?
                filteredSummaryRes.map((firstRow: any, firstIndex: number) => (
                    <React.Fragment key={`level1-${firstIndex}`}>
                        <Table1stRowComponent
                            firstRow={firstRow}
                            firstIndex={firstIndex}
                            keyWOId={keyWOId}
                            optionsCond={optionsCond}
                            principalValueSel={principalValueSel}
                            detailValueSel={detailValueSel}
                            thirdLevelSel={thirdLevelSel}
                            isPerformance={isPerformance}
                            viewDrillDown={viewDrillDown}
                            view3rdLevel={view3rdLevel}
                            isMatchRep={isMatchRep}
                            selectValue={selectValue}
                        />
                    </React.Fragment>
                )) :
                <tr style={{height: '45px'}}>
                    <td colSpan={getColSpanCount()} className="text-center py-4" style={{padding: 0}}>
                        <div className="flex justify-center">
                            <Typography level="title-md">
                                No results found
                            </Typography>
                        </div>
                    </td>
                </tr>
            }
            {(newKeyAdd && newKeyAdd !== "none" && summaryRes[newKeyAdd]) &&
                summaryRes[newKeyAdd].map((itmPlus: any, indexAdd: number) => (
                    <tr key={`${newKeyAdd}_${indexAdd}`}>
                        <td className="px-6 py-4 whitespace-nowrap">
                            {itmPlus[principalValueSel?.show]}
                        </td>
                        {optionsCond && optionsCond.map((itm: any, index: number) => (
                            <td key={index} className="px-6 py-4 whitespace-nowrap">
                                {styleNumber(itmPlus[itm?.name || "null"])}
                            </td>
                        ))}
                        <td className="px-6 py-4 whitespace-nowrap">
                            {styleNumber(itmPlus.total)}
                        </td>
                    </tr>
                ))}
            </tbody>
        </Table>
    </>);
};
