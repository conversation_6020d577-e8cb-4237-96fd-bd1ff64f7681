'use client'
import {useEntity} from "simpler-state";
import {
    changeDefFiltersOnSelSum,
    setChangeDefFiltersOnSelSum,
    setDetailValueSelected,
    setIsReportConsult,
    setTabChangeOnSummary,
    tabChangeOnSummary
} from "@/app/_components/table/states/changeTabSummaryState";
import React, {useEffect, useState} from "react";
import {Tab, Tab<PERSON>ist, Tab<PERSON>anel, Tabs} from "@mui/joy";
import {SummaryReportView} from "@/app/_components/reports/SummaryReportView";
import {LeadsProvider} from "@/app/(private)/leads/_components/LeadsProvider";
import {styledName} from "@/app/_components/utils/styledName";
import {TransfersProvider} from "@/app/(private)/transfers/_components/TransfersProvider";
import {PostBacksProvider} from "@/app/(private)/postbacks/_components/PostBacksProvider";
import _ from "lodash";
import {useQuery} from "@tanstack/react-query";
import Loading from "react-loading";
import {OptionsMatchReport} from "@/app/_components/queries/OptionsMatchReport";
import {debugFilters} from "@/app/_components/table/states/debugFiltersState";
import {CallsProvider} from "@/app/(private)/call-center/calls/_components/CallsProvider";
import {loading} from "@/app/_components/Loading/loadingState";

export const TabsReportTableProvider = ({tableName, globalSearchConfig, tableConfig, defaultFilters}) => {
    const activeTab = useEntity(tabChangeOnSummary)
    const filtersToPass = useEntity(changeDefFiltersOnSelSum)
    const debugFiltersEntity = useEntity(debugFilters)
    const [viewMatchRep, setViewMatchRep] = useState(false);
    const generalLoading = useEntity(loading)
    const providerComponents = {
        leads: LeadsProvider,
        transfers: TransfersProvider,
        postbacks: PostBacksProvider,
        calls: CallsProvider,
    };

    useEffect(() => {
        setChangeDefFiltersOnSelSum(defaultFilters)
        setDetailValueSelected({
            label: "None",
            key: null,
            value: "none"
        })
        if (!debugFiltersEntity && _.isEmpty(debugFiltersEntity)) setTabChangeOnSummary(0)
        setIsReportConsult(false)
        const getRoleUser = _.get(tableConfig?.userMetadata, "role")
        setViewMatchRep(!_.isEmpty(tableConfig?.matchRepConf) && getRoleUser === "admin")
    }, []);

    const changeTab = (event: any, newValue: string | number) => {
        setIsReportConsult(false)
        setTabChangeOnSummary(newValue)
    }

    const {data, isFetching} = useQuery(OptionsMatchReport(tableName));

    const getProviderTable = () => {
        const ProviderComponent = providerComponents[tableName];
        const commonProps = {
            globalSearchConfig,
            tableColumns: tableConfig.tableColumns,
            tableName,
            count: tableConfig.count,
            tableConfig,
            defaultFilters: filtersToPass,
        };
        const dataProp = JSON.stringify(tableConfig.data);
        return <ProviderComponent {...commonProps} data={dataProp}/>;
    }

    return (<>
            {!generalLoading && isFetching ?
                <div className="flex justify-center mt-20">
                    <Loading color={'black'} width={'30px'} height={'30px'} type={'spin'}/>
                </div> :
                <Tabs
                    aria-label="tabs"
                    defaultValue={0}
                    value={activeTab}
                    onChange={changeTab}
                    sx={{bgcolor: 'transparent'}}>
                    <TabList sticky='top' sx={{
                        display: 'inline-flex',
                        justifyContent: 'center',
                        '& .MuiTab-root': {
                            minWidth: 5,
                            '@media (max-width: 600px)': {
                                fontSize: '0.75rem',
                                minWidth: 0,
                                padding: '6px 12px',
                            }
                        },
                        zIndex: 8
                    }}>
                        <Tab value={0} sx={{minWidth: 20}}>{styledName(tableName)} Summary Report</Tab>
                        <Tab value={1} sx={{minWidth: 20}}>{styledName(tableName)} Table Details</Tab>
                        {/*TODO: Uncomment when the feature is ready*/}
                        {/*{(viewMatchRep && !_.isEmpty(data)) &&*/}
                        {/*    <Tab value={2} sx={{minWidth: 20}}>{styledName(tableName)} Match Report</Tab>}*/}
                    </TabList>
                    <TabPanel value={0}>
                        <SummaryReportView
                            table={tableName}
                            dataVendors={data?.vendors || []}
                            dataClients={data?.clients || []}
                            authMetadata={tableConfig?.userMetadata}
                            summaryReportConf={tableConfig?.summaryReportConf}
                        />
                    </TabPanel>
                    <TabPanel value={1}>
                        {getProviderTable()}
                    </TabPanel>
                    {/*TODO: Uncomment when the feature is ready*/}
                    {/*{(viewMatchRep && !_.isEmpty(data)) &&*/}
                    {/*    <TabPanel value={2}>*/}
                    {/*        <MatchCountReportComponent*/}
                    {/*            table={tableName}*/}
                    {/*            matchRepConf={tableConfig?.matchRepConf}*/}
                    {/*            dataSelects={data}*/}
                    {/*        />*/}
                    {/*    </TabPanel>}*/}
                </Tabs>}
        </>
    )
}
