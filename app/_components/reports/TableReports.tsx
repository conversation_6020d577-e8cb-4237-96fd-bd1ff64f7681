"use client"
import Box from "@mui/joy/Box";
import {CsvReport} from "@/app/_components/reports/CsvReport";
import ModalDialog from "@mui/joy/ModalDialog";
import ModalClose from "@mui/joy/ModalClose";
import Typography from "@mui/joy/Typography";
import Modal from "@mui/joy/Modal";
import * as React from "react";
import {useEffect, useState} from "react";
import {Button, Tooltip} from "@mui/joy";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import {useEntity} from "simpler-state";
import {DateTime} from "luxon";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {buildFilters} from "@/app/_lib/nova/functions/utils/buildFilters";
import {useSession} from "@/app/_lib/auth/auth-client";

export const TableReports: React.FC<any> = ({table}) => {
    const {data: session, isPending} = useSession()
    const [showModal, setShowModal] = useState(false)
    const [userData, setUserData] = useState(null);
    const [email, setEmail] = useState("")
    const [loadingBtn, setLoadingBtn] = useState(false)
    const {filtersEntity, columnsVisibilityEntityConfiguration} = createTableEntities(table);
    const filters = useEntity(filtersEntity);

    useEffect(() => {
        const {user}: any = session || {user: null};
        setUserData(user);
    }, [isPending]);

    const generateReport = (report: any) => {
        const email = userData?.email || "";
        setEmail(email);
        setShowModal(true);
    }

    const generateFile = async () => {
        setLoadingBtn(true);
        const currentDateTime = DateTime.now().setZone(Intl.DateTimeFormat().resolvedOptions().timeZone);
        const fileName = `${table}_${currentDateTime.toFormat('yyyy-MM-dd_HH:mm:ss')}.csv`;
        const timezone: string = userData?.timezone && typeof userData?.timezone === 'string'
            ? userData?.timezone
            : "America/Chicago";
        const mongoFilters = buildFilters(filters, {timezone});
        const payload =
            {
                filters,
                mongoFilters,
                table,
                columns: columnsVisibilityEntityConfiguration.get(),
                fileName,
                context: {timezone}
            };
        try {
            const response = await fetch("/api/mongo/reports/generateReport", {
                method: 'POST',
                body: JSON.stringify(payload),
            });
            setLoadingBtn(false)
            setShowModal(false);
            showSuccess(`Your report is being built`)
        } catch (error) {
            setLoadingBtn(false)
            setShowModal(false);
            console.error('Error to generate error');
            console.error(error)
            showError(`Error to generate report`)
        }
    }
    return <>
        <Tooltip title="Generate Report" variant="soft" placement="left">
            <Box>
                <CsvReport generateCsv={() => generateReport("CSV")}>
                </CsvReport>
            </Box>
        </Tooltip>

        <Modal open={showModal}>
            <ModalDialog size="sm" style={{maxWidth: '350px'}} variant="outlined" sx={{padding: '20px'}}>
                <ModalClose onClick={() => setShowModal(false)}/>
                <Typography color="neutral"
                            level="title-lg"
                            noWrap={false}
                            variant="plain">
                    Export to Csv
                </Typography>
                <Typography
                    color="neutral"
                    level="body-md"
                    noWrap={false}
                    variant="plain">
                    The csv file will be generated and sent to <strong> {email}</strong>
                </Typography>
                <Box sx={{width: '100%', display: 'flex', justifyContent: 'end'}}>
                    <Button loading={loadingBtn} sx={{width: '150px', marginTop: '15px'}} onClick={() => generateFile()}
                            variant="outlined">
                        Generate
                    </Button>
                </Box>
            </ModalDialog>
        </Modal>
    </>
}
