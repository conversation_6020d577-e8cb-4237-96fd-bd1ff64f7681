import React, { useState } from "react";
import {Tooltip} from "@mui/joy";
import CloudDownloadIcon from "@mui/icons-material/CloudDownload";

export const DrawerDownloadReport = ({ onDownload }) => {
    const [open, setOpen] = useState(false);

    const handleToggle = () => {
        const newOpenState = !open;
        setOpen(newOpenState);
    };

    const handleDownload = () => {
        handleToggle();
        onDownload();
    };

    return (
        <div
            onClick={handleDownload}
            style={{
                position: 'absolute',
                right: '0',
                top: '70px',
                cursor: 'pointer',
                zIndex: 10
            }}
        >
            <Tooltip title="Download Summary" variant="soft" placement="left">
                <CloudDownloadIcon
                    sx={{
                        fontSize: '30px',
                    }}
                />
            </Tooltip>
        </div>
    );
}
