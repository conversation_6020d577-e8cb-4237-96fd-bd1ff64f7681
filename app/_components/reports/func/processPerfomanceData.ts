import _ from "lodash";

export const processPerformanceData = ({
                                           vendor,
                                           optionsReport,
                                           results
                                       }): { report: any[], counts: any } => {
    if (_.isEmpty(vendor) || _.isEmpty(vendor.pub_ids) || _.isEmpty(results)) {
        return {report: [], counts: {}};
    }

    const finalResponseMap = new Map();
    const collectionCounts: Record<string, number> = {};

    for (const {collection, additions} of optionsReport) {
        const resultsCollection = results.find((itm: any) => itm.collection === collection);
        if (_.isEmpty(resultsCollection?.response)) continue;

        collectionCounts[collection] = 0;

        for (const campaignInfo of resultsCollection.response) {
            const campaignKey = campaignInfo?._id;
            let campaignObj = finalResponseMap.get(campaignKey);

            if (!campaignObj) {
                campaignObj = {
                    campaignKey,
                    [`campaignKey_${collection}`]: campaignInfo?.total ?? 0,
                    subIds: [],
                    _subIdMap: new Map() // internal use only, to avoid findIndex
                };
                applyAdditions(additions, campaignObj, "campaignKey", collection, campaignInfo);
                finalResponseMap.set(campaignKey, campaignObj);
            } else {
                applyAdditions(additions, campaignObj, "campaignKey", collection, campaignInfo);
                campaignObj[`campaignKey_${collection}`] = campaignInfo?.total ?? 0;
            }

            if (Array.isArray(campaignInfo?.infoSubId)) {
                for (const subIdInfo of campaignInfo.infoSubId) {
                    const subId = subIdInfo._id;
                    let subObj = campaignObj._subIdMap.get(subId);

                    if (!subObj) {
                        subObj = {
                            subId,
                            [`subId_${collection}`]: subIdInfo?.total ?? 0
                        };
                        applyAdditions(additions, subObj, "subId", collection, subIdInfo);
                        campaignObj._subIdMap.set(subId, subObj);
                        campaignObj.subIds.push(subObj);
                    } else {
                        applyAdditions(additions, subObj, "subId", collection, subIdInfo);
                        subObj[`subId_${collection}`] = subIdInfo?.total ?? 0;
                    }
                }

                // Optional: sort subIds only once after filling them all
                campaignObj.subIds.sort((a, b) => (a.subId > b.subId ? 1 : -1));
            }

            collectionCounts[collection] += campaignInfo?.total ?? 0;
        }
    }

    // Remove internal maps before returning
    const finalResponse = Array.from(finalResponseMap.values()).map(obj => {
        delete obj._subIdMap;
        return obj;
    });

    return {report: finalResponse, counts: collectionCounts};
};

const applyAdditions = (additions: any[], object: any, type: string, collection: string, response: any) => {
    if (Array.isArray(additions)) {
        for (const item of additions) {
            if (item?.name) {
                object[`${type}_${collection}${item.name}`] = response[item.name] || 0;
            }
        }
    }
}
