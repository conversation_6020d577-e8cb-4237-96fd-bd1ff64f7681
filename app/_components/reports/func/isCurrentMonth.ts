import {DateTime} from 'luxon';

export function isCurrentMonth({startDate, endDate}) {
    const now = DateTime.now();
    const currentMonth = now.month;
    const currentYear = now.year;

    const start = DateTime.fromISO(startDate);
    const end = DateTime.fromISO(endDate);

    const isStartInCurrentMonth = start.month === currentMonth && start.year === currentYear;
    const isEndInCurrentMonth = end.month === currentMonth && end.year === currentYear;

    const isRangeOverlapCurrentMonth =
        start < now.endOf('month') && end > now.startOf('month');

    return isStartInCurrentMonth || isEndInCurrentMonth || isRangeOverlapCurrentMonth;
}