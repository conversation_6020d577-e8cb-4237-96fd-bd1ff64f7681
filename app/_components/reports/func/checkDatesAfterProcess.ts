import {calculateDaysBetween} from "@/app/_components/reports/func/calculateDayBetween";
import {showWarning} from "@/app/_components/alerts/toast/ToastMessages";
import _ from "lodash";

interface dateType {
    startDate: string;
    endDate: string;
}

export function checkDatesAfterProcess(dates: dateType, days: number) {
    const getDaysBetween = calculateDaysBetween(dates?.startDate, dates?.endDate)
    if (getDaysBetween > days) {
        showWarning("Please select a date range less than 6 months for this report")
        return [];
    }
    if (_.isEmpty(dates?.startDate)) {
        showWarning("Select a date range to generate the report")
        return [];
    }
}