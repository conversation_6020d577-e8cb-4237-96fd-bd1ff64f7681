const transferNameGreater = "transfers>120"
const transferNameLess = "transfers<120"

export function groupDataAgingPerformance(data: Array<any>, finalResults: any) {
    const result: { [key: string]: { count: number, ids: any[] } } = {};

    data.forEach((group) => {
        group.forEach((item: any) => {
            const {range, count, ids} = item;
            if (result[range]) {
                result[range].count += count;
                const findBucket = finalResults.find((item: any) => item.bucket === range);
                if (findBucket) {
                    findBucket.transfers += count;
                    findBucket[transferNameGreater] += item[transferNameGreater] ?? 0;
                    findBucket[transferNameLess] += item[transferNameLess] ?? 0;
                }
                sumCountTotal(finalResults, count, item);
                result[range].ids = Array.from(new Set(result[range].ids.concat(ids)));
            } else {
                const groupObj = {
                    bucket: range,
                    leads: finalResults[0].leads,
                    transfers: count,
                    [transferNameGreater]: item[transferNameGreater] ?? 0,
                    [transferNameLess]: item[transferNameLess] ?? 0,
                };
                finalResults.push(groupObj);
                sumCountTotal(finalResults, count, item);
                result[range] = {
                    count,
                    ids: [...ids],
                };
            }
        });
    });

    return Object.entries(result).map(([key, value]) => ({
        _id: key,
        ...value,
    }));
}

function sumCountTotal(finalResults: any, totalTransfers: number, item: any) {
    finalResults[0].transfers += totalTransfers;
    finalResults[0][transferNameGreater] += item[transferNameGreater] ?? 0;
    finalResults[0][transferNameLess] += item[transferNameLess] ?? 0;
}