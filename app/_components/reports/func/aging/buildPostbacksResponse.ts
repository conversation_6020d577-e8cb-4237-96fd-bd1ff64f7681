export function buildPostbacksResponse(response: Array<any>, finalResults: any) {
    if (response.length === 0) return finalResults;
    response.forEach((item: any) => {
        const {bucket, total} = item;
        const findBucket = finalResults.find((item: any) => item.bucket === bucket);
        if (findBucket) findBucket.postbacks = total;
        finalResults[0].postbacks += total;
    });
    return finalResults;
}
