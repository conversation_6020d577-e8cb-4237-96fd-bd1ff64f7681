import {parallel} from "radash";
import {fetchWithRetry} from "@/app/_components/reports/func/fetchWithRetry";

export function fetchPostbacksBuckets(bucketsInfo: Array<any>, optionsReport: Array<any>) {
    if (bucketsInfo.length === 0) return [];
    return parallel(bucketsInfo.length, bucketsInfo, async (bucket) => {
        const payload = {
            transfers: bucket?.ids,
            bucket: bucket?._id,
            optionsReport
        };
        const getPostbacksDataFetch = await fetchWithRetry("/api/mongo/reports/performance/aging/getPostbacks", {
            method: 'POST',
            body: JSON.stringify(payload),
        });
        if (getPostbacksDataFetch.ok) {
            const {response} = await getPostbacksDataFetch.json();
            return response;
        }
    });
}
