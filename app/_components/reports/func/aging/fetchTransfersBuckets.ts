import {parallel} from 'radash';
import {fetchWithRetry} from "@/app/_components/reports/func/fetchWithRetry";

export async function fetchTransfersBuckets(leadIds: Array<any>, optionsReport: Array<any>) {
    const chunkSize = 10;
    const chunks = chunkArray(leadIds, chunkSize);
    let finalResponse = [];
    for (const chunk of chunks) {
        const responses = await parallel(chunk.length, chunk, async (infLeadId) => {
            const payload = {
                leads: infLeadId,
                optionsReport
            };
            const getTransferDataFetch = await fetchWithRetry("/api/mongo/reports/performance/aging/getTransfers", {
                method: 'POST',
                body: JSON.stringify(payload),
            });
            if (getTransferDataFetch.ok) {
                const {response} = await getTransferDataFetch.json();
                return response;
            }
        });

        finalResponse.push(...responses);
    }
    return finalResponse;
}

function chunkArray(array: Array<any>, size: number) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
}
