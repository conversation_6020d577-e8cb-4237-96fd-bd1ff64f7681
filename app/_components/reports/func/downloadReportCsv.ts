import {builtJsonForCSV} from "@/app/_components/reports/func/builtJsonForCSV";
import {json2csv} from "json-2-csv";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import _ from "lodash";
import {tryit} from "radash";

export const downloadCSV = async (data: any, optionsCond: any, valuesSelected: any, table: string, datePicker: string, dateFilter: any, isPerformance: boolean) => {
    const [errorCsv, cscResponse] = await tryit(async () => {
        const builtJson = builtJsonForCSV(data, optionsCond, valuesSelected, isPerformance)
        const csv = json2csv(builtJson, {});
        const blob = new Blob([csv], {type: 'text/csv;charset=utf-8;'});
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        const typeRep = valuesSelected?.principalValueSel?.foreign || ""
        const nameReport = buildReportName(typeRep, datePicker, dateFilter, table);
        link.setAttribute('download', nameReport);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        showSuccess(`Your report is downloaded`);
    })();
    if (errorCsv) {
        console.error('Error built CSV', errorCsv);
        showError(`Error to generate report`);
    }
};

const buildReportName = (typeRep: string, datePicker: string, dateFilter: any, table: string) => {
    const getSinceDate = (datePicker) ? _.get(datePicker, `${dateFilter}-between.value.start`) || "" :
        dateFilter?.startDate || ""
    const getToDate = (datePicker) ? _.get(datePicker, `${dateFilter}-between.value.end`) || "" :
        dateFilter?.endDate || ""
    return `${table}_${typeRep}_${getSinceDate}-00:00_to_${getToDate}-23:59_report_summary.csv`
}