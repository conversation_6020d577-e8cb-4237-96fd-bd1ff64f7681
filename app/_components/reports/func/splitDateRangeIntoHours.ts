import {DateTime} from 'luxon';

export const splitDateRangeIntoHours = (
    startDate: string,
    endDate: string,
    zone: string = 'utc'
) => {
    if (zone === 'UTC' || zone === 'utc') {
        const start = DateTime.fromISO(startDate).startOf('day');
        const end = DateTime.fromISO(endDate).endOf('day');

        const chunks = [];
        let current = start;

        while (current < end) {
            const chunkStart = current;
            const chunkEnd = current.plus({hours: 4}).minus({seconds: 1});

            chunks.push({
                startDate: chunkStart.toISO(),
                endDate: chunkEnd < end ? chunkEnd.toISO() : end.toISO()
            });

            current = current.plus({hours: 4});
        }

        return chunks;
    } else {
        const start = DateTime.fromISO(startDate, {zone}).startOf('day');
        const end = DateTime.fromISO(endDate, {zone}).endOf('day');

        const chunks = [];
        let current = start;

        while (current < end) {
            const chunkStart = current;
            const chunkEnd = current.plus({hours: 4}).minus({seconds: 1});
            chunks.push({
                startDate: chunkStart.toUTC().toISO(),
                endDate: (chunkEnd < end ? chunkEnd : end).toUTC().toISO()
            });

            current = current.plus({hours: 4});
        }

        return chunks;
    }
};
