import {DateTime} from "luxon";

export const getISOWeeksInRange = (startDate, endDate) => {
    let current = DateTime.fromISO(startDate);
    const end = DateTime.fromISO(endDate);
    const weeks = new Set();
    const years = new Set();
    while (current <= end) {
        weeks.add(current.weekNumber);
        years.add(current.weekYear);
        current = current.plus({ days: 1 });
    }
    return {
        weeks: Array.from(weeks),
        years: Array.from(years)
    };
};
