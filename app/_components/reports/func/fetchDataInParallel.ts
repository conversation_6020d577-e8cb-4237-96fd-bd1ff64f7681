import {parallel} from 'radash';
import {combinePerformanceReport} from "@/app/_components/reports/func/combinePerformanceReport";
import {processPerformanceData} from "@/app/_components/reports/func/processPerfomanceData";
import _ from "lodash";
import {fetchWithRetry} from "@/app/_components/reports/func/fetchWithRetry";
import {splitDateRangeIntoHours} from "@/app/_components/reports/func/splitDateRangeIntoHours";

export async function fetchDataInParallel(
    optionsReport: Array<any>,
    payload: any,
    onPartial?: (updater: (prev: any[]) => any[]) => void,
    progress?: {
        onProgressUpdate?: (percent: number, label: string) => void;
    }
) {
    const {rangeDate, context} = payload;
    const chunkDates = splitDateRangeIntoHours(rangeDate.startDate, rangeDate.endDate, context?.timezone);

    const tasks = [];

    let completedTasks = 0;

    for (const optRep of optionsReport) {
        for (const dayRange of chunkDates) {
            tasks.push({optRep, dayRange});
        }
    }

    const resultsByCollection: Record<string, any[]> = {};

    await parallel(10, tasks, async ({optRep, dayRange}) => {
        const {collection} = optRep;

        const res = await fetchWithRetry("/api/mongo/reports/performance/getDataCollections", {
            method: "POST",
            body: JSON.stringify({
                ...payload,
                dayRange,
                optSelect: collection,
                optionsReport
            }),
        });

        if (!res.ok) {
            const err = await res.text();
            console.error(`Error fetching ${collection} on ${dayRange.startDate}:`, err);
            return;
        }

        const {data} = await res.json();
        if (data) {
            resultsByCollection[collection] = combinePerformanceReport(
                resultsByCollection[collection] || [],
                data
            );

            const partialProcessed = processPerformanceData({
                vendor: payload.vendor,
                optionsReport,
                results: [{collection, response: data}]
            });

            if (!_.isEmpty(partialProcessed?.report)) {
                onPartial?.(prev => partialProcessed?.report);
            }

            completedTasks++;
            progress?.onProgressUpdate?.(
                Math.round((completedTasks / tasks.length) * 100),
                `Processing ${completedTasks} of ${tasks.length}`
            );
        }
    });

    const results = Object.entries(resultsByCollection).map(([collection, response]) => ({
        collection,
        response
    }));

    return processPerformanceData({
        vendor: payload.vendor,
        optionsReport,
        results
    });
}
