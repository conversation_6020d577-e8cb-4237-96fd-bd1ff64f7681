import _ from "lodash";

export const getNumberPerformanceOptsRow = (nameOpt: string, row: any, optItm: any, compoundNameOpt: boolean) => {
    const getNamePerNumber = (compoundNameOpt) ?
        `${nameOpt}_${optItm?.name || "null"}` : nameOpt
    let setTotalNumRow = 0;
    let setPartNumRow = 0;
    if (compoundNameOpt) {
        const getNameLeads = `${nameOpt}_leads`
        const getNameTransfers = `${nameOpt}_transfers`
        const getNameTransfersGte = `${nameOpt}_transfers>120`
        if (_.includes(getNamePerNumber, "leads")) {
            if (!row[getNameLeads] || row[getNameLeads] === 0) return 0
            setTotalNumRow = 100
            setPartNumRow = 100
        } else if (_.includes(getNamePerNumber, "transfers")) {
            setTotalNumRow = row[getNameLeads]
            setPartNumRow = row[getNamePerNumber]
        } else if (_.includes(getNamePerNumber, "postbacks")) {
            setTotalNumRow = row[getNameTransfersGte] ?? row[getNameTransfers]
            setPartNumRow = row[getNamePerNumber]
        }
        return calculatePercentage(setTotalNumRow ?? 0, setPartNumRow ?? 0) ?? 0;
    } else {
        switch (optItm?.name) {
            case "leads":
                if (!row[optItm?.name] || row[optItm?.name] === 0) return 0
                setTotalNumRow = 100
                setPartNumRow = 100
                return calculatePercentage(setTotalNumRow ?? 0, setPartNumRow ?? 0) ?? 0;
            case "transfers>120":
            case "transfers<120":
            case "transfers":
                setTotalNumRow = row.leads
                setPartNumRow = row[optItm?.name]
                return calculatePercentage(setTotalNumRow ?? 0, setPartNumRow ?? 0) ?? 0;
            case "postbacks":
                setTotalNumRow = row["transfers>120"] ?? row.transfers
                setPartNumRow = row[optItm?.name]
                return calculatePercentage(setTotalNumRow ?? 0, setPartNumRow ?? 0) ?? 0;
            default:
                return 0
        }
    }
};

const calculatePercentage = (total: number, part: number) => {
    if (!total || total === 0) return 0;
    if (part > total) return 100;
    const percentage = (part / total) * 100;
    return parseFloat(percentage.toFixed(2));
}