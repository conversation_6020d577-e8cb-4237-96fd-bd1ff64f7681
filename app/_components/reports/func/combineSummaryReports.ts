import _ from 'lodash';

export function combineSummaryReports(dataArrays: any, keys: any, nestedArrayKey = null, nestedSubArrayKey = null) {
    const combinedData = _.flatten(dataArrays);
    const {level1, level2, level3} = keys;

    const groupedData = _.groupBy(combinedData, level1);

    const results = Object.values(groupedData).map((items) => {
        const baseSums = items.reduce((acc, item) => {
            Object.keys(item).forEach((prop) => {
                if (typeof item[prop] === 'number') {
                    acc[prop] = (acc[prop] || 0) + item[prop];
                } else if (prop === level1) {
                    acc[prop] = item[prop];
                } else if (!acc[prop] && typeof item[prop] !== 'object') {
                    acc[prop] = item[prop];
                }
            });
            return acc;
        }, {});

        if (nestedArrayKey && items[0][nestedArrayKey]) {
            baseSums[nestedArrayKey] = Object.values(_.groupBy(_.flatten(items.map(i => i[nestedArrayKey])), level2)).map(nestedItems => {
                const nestedSum = nestedItems.reduce((nestedAcc, nestedItem) => {
                    Object.keys(nestedItem).forEach((nestedProp) => {
                        if (typeof nestedItem[nestedProp] === 'number') {
                            nestedAcc[nestedProp] = (nestedAcc[nestedProp] || 0) + nestedItem[nestedProp];
                        } else if (nestedProp === level2) {
                            nestedAcc[nestedProp] = nestedItem[nestedProp];
                        }
                    });
                    return nestedAcc;
                }, {});

                if (nestedSubArrayKey && nestedItems[0][nestedSubArrayKey]) {
                    nestedSum[nestedSubArrayKey] = Object.values(_.groupBy(_.flatten(nestedItems.map(n => n[nestedSubArrayKey])), level3)).map(subItems => {
                        return subItems.reduce((subAcc, subItem) => {
                            Object.keys(subItem).forEach((subProp) => {
                                if (typeof subItem[subProp] === 'number') {
                                    subAcc[subProp] = (subAcc[subProp] || 0) + subItem[subProp];
                                } else if (subProp === level3) {
                                    subAcc[subProp] = subItem[subProp];
                                }
                            });
                            return subAcc;
                        }, {});
                    });
                }

                return nestedSum;
            });
        }

        return baseSums;
    });

    return _.sortBy(results, level1);
}
