import _ from 'lodash';

type Data = {
    report: any[];
    counts: { [key: string]: number };
};

export function combinePerformanceReports(data: Data[], keyIdentifier = 'campaignKey', subKeyIdentifier = 'subId'): Data {
    return data.reduce(
        (acc, item) => {
            item.report.forEach((report: any) => {
                const identifier = report[keyIdentifier];
                const findIdentifier = _.find(acc.report, [keyIdentifier, identifier]);

                if (findIdentifier) {
                    _.forEach(report, (value, key) => {
                        if (_.isNumber(value)) {
                            findIdentifier[key] = (findIdentifier[key] || 0) + value;
                        } else if (key === 'subIds' && Array.isArray(value)) {
                            value.forEach((subIdItem: any) => {
                                const existingSubId = _.find(findIdentifier.subIds, [subKeyIdentifier, subIdItem[subKeyIdentifier]]);
                                if (existingSubId) {
                                    _.forEach(subIdItem, (subValue, subKey) => {
                                        if (_.isNumber(subValue)) {
                                            existingSubId[subKey] = (existingSubId[subKey] || 0) + subValue;
                                        }
                                    });
                                } else {
                                    findIdentifier.subIds.push(_.cloneDeep(subIdItem));
                                }
                            });
                        }
                    });
                } else {
                    acc.report.push(_.cloneDeep(report));
                }
            });

            if (item.counts) {
                _.forEach(item?.counts, (countValue, countKey) => {
                    if (_.isNumber(countValue)) {
                        acc.counts[countKey] = (acc.counts[countKey] || 0) + countValue;
                    }
                });
            }
            return acc;
        },
        { report: [], counts: {} } as Data
    );
}
