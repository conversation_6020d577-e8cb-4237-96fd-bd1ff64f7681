import {DateTime} from "luxon";

export function getWeekNumbers(start: any, end: any) {
    const startDate = DateTime.fromISO(start);
    const endDate = DateTime.fromISO(end);
    const weekNumbers = new Set();

    let currentDate = startDate;
    while (currentDate <= endDate) {
        weekNumbers.add(currentDate.weekNumber);
        currentDate = currentDate.plus({ days: 1 });
    }

    return Array.from(weekNumbers);

}

