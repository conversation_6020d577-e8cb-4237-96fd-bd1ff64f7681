import _ from "lodash";
import {fetchWithRetry} from "@/app/_components/reports/func/fetchWithRetry";
import {parallel} from "radash";
import {combineSummaryReports} from "@/app/_components/reports/func/combineSummaryReports";

export async function summaryReportRequest(
    payload: any,
    onPartial?: (updater: (prev: any[]) => any[]) => void,
    progress?: {
        onProgressUpdate?: (percent: number, label: string) => void;
    }
): Promise<any[]> {
    const {
        dateOnlyMode,
        chunkDatesHours,
        chunkDates,
        selectObj,
        selectDetail,
        thirdLevelDetail,
        availableVendors,
        availableClients
    } = payload;

    const level1 = selectObj?.show;
    const level2 = (_.includes(selectDetail?.key, "_id")) ?
        _.replace(selectDetail?.key, '_id', '') :
        selectDetail?.key ?? null;
    const level3 = thirdLevelDetail?.value ?? null;

    const name2ndNested = (selectDetail?.value !== "none") ? selectDetail?.value : null;
    const name3rdNested = (thirdLevelDetail?.value !== "none") ? thirdLevelDetail?.value : null;

    let combinations: any[];

    if (dateOnlyMode) {
        combinations = chunkDatesHours.map((dayRange: any) => ({dayRange}));
    } else {
        const isVendor = level1 === 'vendor';
        const idsList = isVendor ? availableVendors : availableClients;

        combinations = idsList.flatMap((idObj: any) =>
            chunkDates.map((dayRange: any) => ({idObj, dayRange}))
        );
    }

    let completedTasks = 0;

    try {
        const results = await parallel(10, combinations, async (combo: any) => {
            const bodyPayload = {
                ...payload,
                chunkDates: [combo.dayRange],
            };

            if (!dateOnlyMode && combo.idObj) {
                bodyPayload.matchKey = selectObj?.key;
                bodyPayload.matchValue = combo.idObj._id;
                bodyPayload.nameMatch = combo.idObj[level1];
            }

            const res = await fetchWithRetry('/api/mongo/reports/generateSummary', {
                method: 'POST',
                body: JSON.stringify(bodyPayload),
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!res.ok) {
                const err = await res.text();
                console.error("Fetch error:", err);
                return [];
            }

            const {data} = await res.json();
            if (!Array.isArray(data)) return [];

            completedTasks++;
            progress?.onProgressUpdate?.(
                Math.round((completedTasks / combinations.length) * 100),
                `Processing ${completedTasks} of ${combinations.length}`
            );

            if (data.length > 0) onPartial?.((prev) => data);

            return data;
        });

        return combineSummaryReports(_.flatten(results), {
            level1,
            level2,
            level3
        }, name2ndNested, name3rdNested);
    } catch (err) {
        console.error("Summary report failed:", err);
        throw err;
    }
}
