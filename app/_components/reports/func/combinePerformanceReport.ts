import _ from "lodash";

export const combinePerformanceReport = (existing: any[], incoming: any[]): any[] => {
    const resultMap = new Map<string, any>();

    for (const item of existing) {
        resultMap.set(item._id, _.cloneDeep(item));
    }

    for (const newItem of incoming) {
        const id = newItem._id;
        const existingItem = resultMap.get(id);

        if (!existingItem) {
            resultMap.set(id, _.cloneDeep(newItem));
        } else {
            for (const [key, value] of Object.entries(newItem)) {
                if (key !== '_id' && key !== 'infoSubId' && typeof value === 'number') {
                    existingItem[key] = (existingItem[key] || 0) + value;
                }
            }

            if (Array.isArray(newItem.infoSubId)) {
                if (!Array.isArray(existingItem.infoSubId)) {
                    existingItem.infoSubId = [];
                }

                const subMap = new Map<string, any>();
                for (const sub of existingItem.infoSubId) {
                    subMap.set(sub._id, _.cloneDeep(sub));
                }

                for (const subItem of newItem.infoSubId) {
                    const existingSub = subMap.get(subItem._id);
                    if (!existingSub) {
                        subMap.set(subItem._id, _.cloneDeep(subItem));
                    } else {
                        for (const [key, value] of Object.entries(subItem)) {
                            if (key !== '_id' && typeof value === 'number') {
                                existingSub[key] = (existingSub[key] || 0) + value;
                            }
                        }
                    }
                }

                existingItem.infoSubId = Array.from(subMap.values()).sort((a, b) =>
                    a._id.localeCompare(b._id)
                );
            }
        }
    }

    return Array.from(resultMap.values()).sort((a, b) => a._id.localeCompare(b._id));
};
