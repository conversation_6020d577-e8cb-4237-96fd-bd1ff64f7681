import { DateTime } from "luxon";

export const splitDateRangeIntoDays = (startDate: string, endDate: string, timezone: string = "America/Chicago") => {
    if (timezone === 'UTC' || timezone === 'utc') {
        const start = DateTime.fromISO(startDate);
        const end = DateTime.fromISO(endDate);
        const days = [];

        let currentDay = start;

        while (currentDay <= end) {
            days.push({
                startDate: currentDay.toISODate(),
                endDate: currentDay.toISODate(),
            });
            currentDay = currentDay.plus({ days: 1 });
        }

        return days;
    } else {
        const start = DateTime.fromISO(startDate, {zone: timezone});
        const end = DateTime.fromISO(endDate, {zone: timezone});
        const days = [];

        let currentDay = start;

        while (currentDay <= end) {
            const dayUTC = currentDay.toUTC();
            days.push({
                startDate: dayUTC.toISODate(),
                endDate: dayUTC.toISODate(),
            });
            currentDay = currentDay.plus({ days: 1 });
        }

        return days;
    }
};
