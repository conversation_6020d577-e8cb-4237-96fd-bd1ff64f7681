import _ from "lodash";
import {removeS} from "@/app/_components/reports/func/removeS";

export const builtJsonForCSV = (jsonData: any, optionsCol: any, {
    principal,
    secondary,
    third
}, isPerformance: boolean) => {
    const csvData = [];
    jsonData.forEach((itemFirst: any) => {
        if (!_.isEmpty(principal)) {
            if (secondary?.value && secondary?.value !== "none") {
                const resultFst = builtObjCSV(itemFirst, principal?.show, "", optionsCol, isPerformance)
                csvData.push(resultFst);
                if (itemFirst[secondary?.value]) {
                    itemFirst[secondary?.value].forEach((itemSecond: any) => {
                        const secValue = (secondary?.fKey) ? secondary?.fKey : removeS(secondary?.value)
                        if (third?.value !== "none") {
                            const resultSnd = builtObjCSV(itemSecond, secValue, "", optionsCol, isPerformance)
                            csvData.push(resultSnd);
                            if  (itemSecond[third?.value]) {
                                itemSecond[third?.value].forEach((itemThird: any) => {
                                    const resultThird = builtObjCSV(itemThird, third?.value, "", optionsCol, isPerformance)
                                    csvData.push(resultThird);
                                });
                            }
                        } else {
                            const resultSnd = builtObjCSV(itemSecond, secValue, "", optionsCol, isPerformance)
                            csvData.push(resultSnd);
                        }
                    });
                }
            } else {
                const resultFst = builtObjCSV(itemFirst, "", principal?.show, optionsCol, isPerformance)
                csvData.push(resultFst);
            }
        }
    });

    return csvData;
};


function builtObjCSV(item: any, selectData: string, unique: string, optionsCol: any, isPerformance: boolean) {
    const getTotalNameRow = (!_.isEmpty(selectData)) ? `${selectData}_total` : "total";
    if (_.isArray(item)) {
        item.forEach((itm: any) => {
            return builtObjCSV(itm, selectData, unique, optionsCol, isPerformance)
        });
    } else {
        const builtObjJson = {
            type: selectData || unique,
            name: item[selectData || unique]
        };
        optionsCol.forEach((itmOptCol: any) => {
            const getNameOptNumber = (!_.isEmpty(selectData)) ? `${selectData}_${itmOptCol?.name || "null"}` : itmOptCol?.name || "null";
            builtObjJson[itmOptCol?.name] = item[getNameOptNumber] ?? 0;
        });
        if (!isPerformance) Object.assign(builtObjJson, {total: item[getTotalNameRow] ?? 0});
        return builtObjJson;
    }

}