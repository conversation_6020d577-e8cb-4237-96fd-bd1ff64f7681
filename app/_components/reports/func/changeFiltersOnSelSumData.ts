import _ from "lodash";
import {DateTime} from "luxon";

export const changeFiltersOnSelSumData = (tableName: string, rowData: any, addRow: any, itemData: any, keyFind: string, detailValueSel: any) => {
    const idNullVendor = "19869a1f3e47428f8a59b2fc6bcf9219"
    let returnFilters = {}
    const checkPickDate = localStorage.getItem(tableName + 'summaryDate');

    if (!_.isEmpty(checkPickDate) && !(_.has(rowData, "pub") || _.has(rowData, "transferred_to"))) {
        const dateObj = JSON.parse(checkPickDate)
        returnFilters = {...dateObj}
    }

    if (!_.isEmpty(itemData)) {
        const setNameForKey = `${keyFind}-${itemData?.func}`
        returnFilters[setNameForKey] = {
            filterType: itemData?.func,
            label: keyFind,
            value: itemData?.value
        }
    }

    if (!_.isEmpty(addRow)) {
        const getAddValueType = (addRow?.vendor) ? "vendor_id-inF" : "client_id-inF"
        const labelAddType = (addRow?.vendor) ? "vendor" : "client"
        const getValues = (addRow?._id === idNullVendor) ? [addRow?._id, "", null] : [addRow?._id]
        returnFilters[getAddValueType] = {
            value: getValues,
            filterType: 'inF',
            label: labelAddType
        }
    }

    if (detailValueSel !== 'date') {
        if (_.has(rowData, "vendor") || _.has(rowData, "client")) {
            const getValueType = (rowData?.vendor) ? "vendor_id-inF" : "client_id-inF"
            const labelType = (rowData?.vendor) ? "vendor" : "client"
            const getValues = (rowData?._id === idNullVendor) ? [rowData?._id, "", null] : [rowData?._id]
            returnFilters[getValueType] = {
                value: getValues,
                filterType: 'inF',
                label: labelType
            }
        } else if (_.has(rowData, "pub") || _.has(rowData, "transferred_to")) {
            const getValueFind = detailValueSel?.value
            const keyArray = (_.includes(getValueFind, "_id")) ?
                _.replace(getValueFind, '_id', '') :
                getValueFind
            const getValueEq = rowData[keyArray] ?? null
            if (!_.isEmpty(getValueEq) || getValueEq === "") {
                returnFilters[getValueFind] = {
                    value: getValueEq,
                    filterType: 'eq',
                    label: getValueFind
                }
            }
        } else {
            if (!_.isEmpty(rowData) && tableName === 'leads') {
                const getValueType = 'disposition-eq'
                const getValId = _.has(rowData, "_id") ? _.get(rowData, '_id') : null
                if (!_.isEmpty(getValId)) {
                    returnFilters[getValueType] = {
                        value: getValId,
                        filterType: 'eq',
                        label: 'disposition'
                    }
                }
            }
        }
    } else {
        const {date} = rowData;
        const parsedDate = DateTime.fromFormat(date, 'LLL dd, yyyy');
        const formattedDate = parsedDate.toISODate();
        const newValueDate = {
            start: formattedDate,
            end: formattedDate,
        }
        const mainKey = _.keys(returnFilters);
        const findKeyBetween = _.find(mainKey, (itm: string) => _.includes(itm, '-between'))
        _.set(returnFilters, [findKeyBetween, 'value'], newValueDate);
    }

    return returnFilters
}