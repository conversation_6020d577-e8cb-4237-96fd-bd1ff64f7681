export const fetchWithRetry = async (
    URL: string,
    payload: RequestInit
): Promise<Response> => {
    const retries = 3;
    const retryDelay = 1000;
    for (let attempt = 0; attempt <= retries; attempt++) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 28000);
            const res = await fetch(URL, { ...payload, signal: controller.signal });
            clearTimeout(timeoutId);

            if (!res.ok) {
                if (attempt < retries) {
                    console.warn(`Request failed with status ${res.status}. Retrying (${attempt + 1}/${retries})...`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                    continue;
                } else {
                    throw new Error(`Failed after ${retries + 1} attempts. Status: ${res.status}`);
                }
            }

            return res;
        } catch (err: any) {
            console.warn(`Attempt ${attempt + 1} failed: ${err.name} - ${err.message}`);

            if (attempt >= retries) {
                throw new Error(`Fetch failed after ${attempt + 1} attempts: ${err.message}`);
            }

            await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
    }

    throw new Error('Unexpected fetch failure');
};
