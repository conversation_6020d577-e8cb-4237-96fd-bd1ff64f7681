import React, {useEffect, useState} from "react";
import Typography from "@mui/joy/Typography";
import _ from "lodash";
import {Autocomplete, AutocompleteOption, Box, Button, Grid, Tooltip} from "@mui/joy";
import {DrawerDownloadReport} from "@/app/_components/reports/DrawerDownloadReport";
import {styleNumber} from "@/app/_components/utils/styleNumber";
import {DateRangeComponent} from "../../table/filters/DateRangeComponent";
import {DateTime} from "luxon";
import {isCurrentMonth} from "@/app/_components/reports/func/isCurrentMonth";

export const HeaderPerformanceReports: React.FC<any> = ({
                                                            title,
                                                            vendorsData,
                                                            rangeDate,
                                                            userMetadata,
                                                            isFetchProcess,
                                                            dataUpdatedAt,
                                                            changeRangeDateFilter,
                                                            onChangeVendor,
                                                            allData,
                                                            countsData,
                                                            reFetch,
                                                            handleDownloadCSV
                                                        }) => {
    const checkMonth = isCurrentMonth(rangeDate);
    const getTZ = userMetadata?.timezone ?? "America/Chicago"
    const [lastFetchedDate, setLastFetchedDate] = useState<{ relative: string, exact: string } | null>(null);

    useEffect(() => {
        const getDate = DateTime.fromMillis(dataUpdatedAt)
            .setZone(getTZ);

        const relativeTime = getDate.toRelative();
        const exactTime = getDate.toFormat('LLL dd yy, HH:mm ZZZZ');

        setLastFetchedDate({relative: relativeTime, exact: exactTime});
    }, [dataUpdatedAt]);

    return (<>
        <div className="flex justify-center mt-6 mb-6">
            <Typography level="h2" fontSize="xl" sx={{mb: 0.5}}>
                {title}
            </Typography>
        </div>
        {!_.isEmpty(vendorsData) &&
            <Grid container spacing={2}>
                <Grid xs={12} md={6} lg={6}>
                    <div className="flex flex-col items-center">
                        {rangeDate.startDate && (<>
                            <Typography level="body-md">
                                Date Range
                            </Typography>
                            <Box sx={{mt: '5px'}}>
                                <DateRangeComponent
                                    inputStyles={{
                                        height: '32px',
                                        background: '#fbfcfe',
                                        fontSize: '14px'
                                    }}
                                    hiddeClear={true}
                                    viewNormalButton={true}
                                    onApplyFilters={changeRangeDateFilter}
                                    defaultValues={rangeDate}
                                    isLoading={isFetchProcess}
                                />
                            </Box>
                        </>)}
                    </div>
                </Grid>
                <Grid xs={12} md={6} lg={6}>
                    <div className="flex flex-col items-center">
                        <Typography level="body-md">
                            Select Vendor
                        </Typography>
                        <Autocomplete
                            options={vendorsData.map((option: any) => {
                                return {label: option.vendor, ...option}
                            })}
                            disabled={isFetchProcess}
                            placeholder="Select or search"
                            freeSolo
                            onChange={onChangeVendor}
                            sx={{height: "32px", fontSize: '12px', mt: '5px'}}
                            renderOption={(props: any, option: any) => {
                                const {key, ...rest} = props;
                                return <AutocompleteOption key={key}  {...rest}>
                                    <Typography level="body-xs">
                                        {option?.vendor}
                                    </Typography>
                                </AutocompleteOption>
                            }}
                        />
                    </div>
                </Grid>
            </Grid>}
        {(!_.isEmpty(countsData)) &&
            <Grid container spacing={2} sx={{mt: '20px'}}>
                <Grid xs={12} md={4} lg={4}>
                    <div className="flex justify-center">
                        <Typography level="body-md">
                            <b>Leads:</b> {styleNumber(countsData?.leads || 0)}
                        </Typography>
                    </div>
                </Grid>
                <Grid xs={12} md={4} lg={4}>
                    <div className="flex justify-center">
                        <Typography level="body-md">
                            <b>Transfers:</b> {styleNumber(countsData?.transfers || 0)}
                        </Typography>
                    </div>
                </Grid>
                <Grid xs={12} md={4} lg={4}>
                    <div className="flex justify-center">
                        <Typography level="body-md">
                            <b>Postbacks:</b> {styleNumber(countsData?.postbacks || 0)}
                        </Typography>
                    </div>
                </Grid>
            </Grid>}
        {userMetadata?.dev &&
            <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                width: {
                    xs: '90%',
                    md: 'auto'
                }
            }}>
                <Button variant='outlined' color='neutral'
                        sx={{height: '30px', mt: '30px'}} onClick={reFetch}>
                    Re Fetch Data
                </Button>
            </Box>}
        {(!_.isEmpty(allData)) && <>
            {checkMonth ?
                <Tooltip title={`Last query: ${lastFetchedDate.exact}`} variant="soft" placement="top">
                    <Button size="md" variant='soft'
                            sx={{mt: '25px'}}
                            loading={isFetchProcess}
                            onClick={() => reFetch()} color="neutral">
                        Updated {lastFetchedDate.relative}
                    </Button>
                </Tooltip> : null}
            <DrawerDownloadReport onDownload={handleDownloadCSV}/>
        </>}
    </>);
}
