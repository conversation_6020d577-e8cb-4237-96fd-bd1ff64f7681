"use client"
import React, {useEffect, useState} from "react";
import {SummaryReportComponent} from "@/app/_components/reports/SummaryReportComponent";
import {createSummaryReportEntities} from "@/app/_components/table/states/summaryReportEntity";
import Loading from "react-loading";
import {checkRestricts} from "@/app/_components/reports/func/checkRestricts";

type SelectKeyConfig = {
    foreign: string;
};

export const SummaryReportView: React.FC<any> = ({
                                                     table,
                                                     dataVendors,
                                                     dataClients,
                                                     authMetadata,
                                                     summaryReportConf
                                                 }) => {
    const restrictions = authMetadata?.restrictions ?? []
    const filters = createSummaryReportEntities(table);
    const [filterDate, setFiltersDate] = useState(filters?.filtersEntity);
    const [isLoadingData, setIsLoadingData] = useState(false);
    const [allVendors, setAllVendors] = useState(checkRestricts(dataVendors, restrictions?.vendors ?? []));
    const [allClients, setAllClients] = useState(checkRestricts(dataClients, restrictions?.clients ?? []));
    const [updatedSumRepConf, setUpdatedSumRepConf] = useState(summaryReportConf);
    const keysProAgg = summaryReportConf?.project

    useEffect(() => {
        setIsLoadingData(true)
        const validatedSelectKeys = summaryReportConf.select_keys.filter((keyConfig: SelectKeyConfig) => {
            if (keyConfig.foreign === 'clients' && authMetadata?.restrictions.clients === false) {
                return false;
            }
            return !(keyConfig.foreign === 'vendors' && authMetadata?.restrictions.vendors === false);
        });
        const updatedSummaryReportConf = {
            ...summaryReportConf,
            select_keys: validatedSelectKeys
        }
        setUpdatedSumRepConf(updatedSummaryReportConf)
        setIsLoadingData(false)
    }, [])

    return (<>
        {isLoadingData ?
            <div className="flex justify-center mt-20">
                <Loading color={'black'} width={'30px'} height={'30px'} type={'spin'}/>
            </div> :
            <SummaryReportComponent
                table={table}
                allVendors={allVendors}
                allClients={allClients}
                keysProAgg={keysProAgg}
                summaryReportConf={updatedSumRepConf}
                authMetadata={authMetadata}
                filterDate={filterDate}/>}
    </>);
}
