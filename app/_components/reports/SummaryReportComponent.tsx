"use client"

import * as React from "react";
import {Fragment, useEffect, useState} from "react";
import {HeaderModalSumRep} from "@/app/_components/reports/HeaderModalSumRep";
import {TableSummaryReport} from "@/app/_components/reports/TableSummaryReport";
import {DateTime} from "luxon";
import Box from "@mui/joy/Box";
import _ from "lodash";
import {Button, Tooltip, Typography} from "@mui/joy";
import {
    detailValueSelected,
    thirdLevelSelected,
    vendorLeadsSelected
} from "@/app/_components/table/states/changeTabSummaryState";
import {useEntity} from "simpler-state";
import {downloadCSV} from "@/app/_components/reports/func/downloadReportCsv";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {SummaryReport} from "@/app/_components/queries/SummaryReport";
import {showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {ProgressLoadingReport} from "@/app/_components/reports/ProgressLoadingReport";

export const SummaryReportComponent: React.FC<any> = ({
                                                          table,
                                                          allVendors,
                                                          allClients,
                                                          keysProAgg,
                                                          summaryReportConf,
                                                          authMetadata,
                                                          filterDate
                                                      }) => {
    const filterVendSel = useEntity(vendorLeadsSelected)
    const detailValueSel = useEntity(detailValueSelected)
    const thirdLevelSel = useEntity(thirdLevelSelected)
    const [selectKeys, setSelectKeys] = useState(summaryReportConf?.select_keys || []);
    const [optionsCond, setOptionsCond] = useState(summaryReportConf?.options || []);
    const [requiredKey, setRequiredKey] = useState(summaryReportConf?.required_key || null);
    const [principalValueSel, setPrincipalValueSel] = useState({key: "none", show: "None", value: "none"});
    const [datePicker, setDatePicker] = useState(summaryReportConf?.filter || null);
    const [dateFilter, setDateFilter] = useState(filterDate || {});
    const [defaultRangeDate, setDefaultRangeDate] = useState({startDate: '', endDate: ''});
    const [userRestrictions, setUserRestrictions] = useState(authMetadata?.restrictions ?? {})
    const [userRole, setUserRole] = useState(authMetadata?.role ?? null)
    const [partialChunks, setPartialChunks] = useState<any[]>([]);
    const [lastFetchedDate, setLastFetchedDate] = useState<{ relative: string, exact: string } | null>(null);
    const [fetchProgress, setFetchProgress] = useState<number>(0);
    const [progressLabel, setProgressLabel] = useState<string>('');
    const queryClient = useQueryClient();

    useEffect(() => {
        setDefaultRangeDate(getDefaultRangeDate());
    }, [filterDate]);

    useEffect(() => {
        setPartialChunks([]);
    }, [dateFilter, principalValueSel, detailValueSel, thirdLevelSel, filterVendSel]);

    const getDefaultRangeDate = () => {
        let returnVals = null
        let setDatePick = {}
        if (filterDate && Object.keys(filterDate).length > 0) {
            let dateCreatedBetween = filterDate["date_created-between"];
            if (table === 'transfers') {
                dateCreatedBetween = filterDate["completed_date-between"];
            } else if (table === 'postbacks') {
                dateCreatedBetween = filterDate["date_retained-between"];
            } else if (table === 'calls') {
                dateCreatedBetween = filterDate["createdAt-between"];
            }
            returnVals = {startDate: dateCreatedBetween?.value?.start, endDate: dateCreatedBetween?.value?.end};
            setDatePick = {start: dateCreatedBetween?.value?.start, end: dateCreatedBetween?.value?.end};
        } else {
            const myTimeZone = 'America/Chicago';
            const todayDate = DateTime.now().setZone(myTimeZone).toISODate();
            returnVals = {startDate: todayDate, endDate: todayDate};
            setDatePick = {start: todayDate, end: todayDate};
        }
        builtDatePick(setDatePick)
        return returnVals
    }

    const changeRangeDateFilter = (event: any) => {
        builtDatePick({start: event.startDate, end: event.endDate});
    }

    const builtDatePick = (value: any) => {
        const built = {
            [`${datePicker}-between`]: {
                filterType: 'between',
                label: datePicker,
                value
            }
        }
        localStorage.setItem(table + 'summaryDate', JSON.stringify(built));
        setDateFilter(built);
    }

    const handleChangePrincipal = (
        event: React.SyntheticEvent | null,
        keySelect: any | null
    ) => {
        if (keySelect !== null) {
            localStorage.setItem(table + 'summarySelect', JSON.stringify(keySelect));
            setPrincipalValueSel(keySelect);
        }
        if (keySelect?.value === "none") {
            setPartialChunks([]);
        }
    }

    const functionReFetch = async () => {
        setPartialChunks([]);
        await queryClient.invalidateQueries({
            queryKey: [`${table}-summary-report`]
        });
    }

    const {data, isFetching, dataUpdatedAt} = useQuery(
        SummaryReport(
            table,
            principalValueSel,
            detailValueSel,
            thirdLevelSel,
            filterVendSel,
            dateFilter,
            datePicker,
            authMetadata,
            requiredKey,
            keysProAgg,
            optionsCond,
            allVendors,
            allClients,
            (updater: (prev: any[]) => any[]) => {
                setPartialChunks((prev) => updater(prev));
            },
            {
                onProgressUpdate: (percent, label) => {
                    setFetchProgress(percent);
                    setProgressLabel(label);
                }
            }
        )
    );

    const handleDownloadCSV = async () => {
        const valuesSelected = {
            principal: principalValueSel,
            secondary: detailValueSel,
            third: thirdLevelSel
        }
        await downloadCSV(data, optionsCond, valuesSelected, table, dateFilter, datePicker, false)
    };

    useEffect(() => {
        if (!isFetching && !_.isEmpty(data)) showSuccess("Report generated successfully")
        const getTZ = authMetadata?.timezone ?? "America/Chicago"
        if (dataUpdatedAt > 0) {
            const getDate = DateTime.fromMillis(dataUpdatedAt)
                .setZone(getTZ);

            const relativeTime = getDate.toRelative();
            const exactTime = getDate.toFormat('LLL dd yy, HH:mm ZZZZ');

            setLastFetchedDate({relative: relativeTime, exact: exactTime});
        }
    }, [isFetching, data]);

    const sxBox = (isStreaming: boolean) => {
        return {
            mt: '25px',
            overflow: (isStreaming) ? 'hidden' : 'scroll',
            height: {
                xs: '75vh',
                sm: '75vh',
                md: '80vh',
                lg: '85vh'
            },
            width: {
                xs: '90%',
                sm: '97%',
                md: '98%',
                lg: '99%'
            },
            position: 'relative',
            '@media (max-width: 600px)': {
                width: '95%'
            }
        }
    }

    return (
        <>
            <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                width: {
                    xs: '90%',
                    md: 'auto'
                }
            }}>
                <HeaderModalSumRep
                    table={table}
                    allVendors={allVendors}
                    summaryRes={data}
                    loadingBtn={isFetching}
                    downloadCSV={handleDownloadCSV}
                    valueSel={principalValueSel}
                    handleChangePrincipal={handleChangePrincipal}
                    changeRangeDateFilter={changeRangeDateFilter}
                    defaultRangeDate={defaultRangeDate}
                    selectKeys={selectKeys}
                    userRestrictions={userRestrictions}
                    userRole={userRole}
                />
            </Box>
            {(dataUpdatedAt && dataUpdatedAt > 0 && !_.isEmpty(lastFetchedDate)) ?
                <Tooltip title={`Last query: ${lastFetchedDate.exact}`} variant="soft" placement="top">
                    <Button size="md" variant='soft'
                            sx={{mt: '25px'}}
                            loading={isFetching}
                            onClick={() => functionReFetch()} color="neutral">
                        Updated {lastFetchedDate.relative}
                    </Button>
                </Tooltip> : null}
            {!_.isEmpty(data) ? <Box sx={sxBox(false)}>
                <TableSummaryReport principalValueSel={principalValueSel} detailValueSel={detailValueSel}
                                    optionsCond={optionsCond} summaryRes={data} tableName={table}
                                    thirdLevelSel={thirdLevelSel} requiredKey={requiredKey} isStreaming={false}
                                    isMatchRep={false} isPerformance={false}/>
                </Box> :
                (isFetching) ? <Box sx={sxBox(true)}>
                    <ProgressLoadingReport progressLabel={progressLabel} fetchProgress={fetchProgress}/>
                    <TableSummaryReport principalValueSel={principalValueSel} detailValueSel={detailValueSel}
                                        optionsCond={optionsCond} summaryRes={partialChunks} tableName={table}
                                        thirdLevelSel={thirdLevelSel} requiredKey={requiredKey} isStreaming={true}
                                        isMatchRep={false} isPerformance={false}/>
                    </Box> :
                    (principalValueSel?.value !== "none") ?
                        <div className="flex justify-center mt-10">
                            <Typography level="title-lg">
                                No data to display.
                            </Typography>
                        </div> :
                        <div className="flex justify-center mt-10">
                            <Typography level="title-lg">
                                Please select a view option.
                            </Typography>
                        </div>}
        </>
    )
}
