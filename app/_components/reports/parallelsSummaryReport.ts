import {summaryReportRequest} from "@/app/_components/reports/func/summaryReportRequest";
import {parallel, tryit} from "radash";

export async function parallelsSummaryReport(weeklyChunks: Array<any>, commonPayloadSettings: any, datePicker: string, filters: any, router: any) {
    const allResults = [];
    for (const chunk of weeklyChunks) {
        const [error, results] = await tryit(() =>
            parallel(chunk.length, chunk, async (weekRange: any) => {
                const payload = { ...commonPayloadSettings, filters: { ...filters }};
                payload.filters[`${datePicker}-between`] = {
                    filterType: 'between',
                    value: { start: weekRange?.startDate, end: weekRange?.endDate }
                };
                return summaryReportRequest(payload);
            })
        )();
        if (error) {
            console.error("Error in parallel requests:", error);
            continue;
        }
        allResults.push(...results);
    }
    return allResults;
}
