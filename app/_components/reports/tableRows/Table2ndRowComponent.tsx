import {But<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Tooltip} from '@mui/joy';
import _ from "lodash";
import {getNumberPerformanceOptsRow} from "@/app/_components/reports/func/getNumberPerformanceOptRow";
import {ProgressProvider} from "@/app/_components/vendorPerf/ProgressProvider";
import {styleNumber} from "@/app/_components/utils/styleNumber";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import React, {useState} from "react";
import {limitString} from "@/app/_components/reports/func/limitString";
import {Table3rdRowComponent} from "@/app/_components/reports/tableRows/Table3rdRowComponent";

export const Table2ndRowComponent = ({
                                         firstIndex,
                                         secondIndex,
                                         firstRow,
                                         secondRow,
                                         optionsCond,
                                         detailValueSel,
                                         thirdLevelSel,
                                         viewDrillDown,
                                         view3rdLevel,
                                         isMatchRep,
                                         isPerformance,
                                         selectValue
                                     }) => {
    const [openRow, setOpenRow] = useState(true);

    let keyWOId = (viewDrillDown) ?
        (_.includes(detailValueSel?.key, "_id")) ?
            _.replace(detailValueSel?.key, '_id', '') :
            (detailValueSel?.key ?? "") : ""
    keyWOId = detailValueSel?.fKey ? detailValueSel?.fKey : keyWOId;

    const getNumberOpts2ndLevelRow = (itmAdd: any, optItm: any) => {
        const getNameOptNumber = `${keyWOId}_${optItm?.name || "null"}`;
        return styleNumber(itmAdd[getNameOptNumber] || 0);
    };

    const getNumberTotal2ndLevelRow = (row: any) => {
        const getTotalNameRow = `${keyWOId}_total`;
        return styleNumber(row[getTotalNameRow] || 0);
    };

    return (<>
        <tr className={(view3rdLevel && thirdLevelSel?.value !== "none") ? "bg-gray-50" : ""}>
            {view3rdLevel && thirdLevelSel?.value !== "none" ?
                <td>
                    <IconButton
                        aria-label="expand row"
                        variant="plain"
                        color="neutral"
                        size="sm"
                        onClick={() => setOpenRow(!openRow)}>
                        {openRow ? <KeyboardArrowUpIcon/> : <KeyboardArrowDownIcon/>}
                    </IconButton>
                </td> :
                <td></td>}
            <td></td>
            <td colSpan={(thirdLevelSel?.value !== "none" && !isMatchRep) ? 2 : 1}
                className={"px-6 py-4 whitespace-nowrap"}>
                {limitString(!_.isEmpty(secondRow[keyWOId]) ? secondRow[keyWOId] : "Unknown")}
            </td>
            {(isPerformance && view3rdLevel && thirdLevelSel?.value !== "none") && <td></td>}
            {optionsCond && optionsCond.map((itm: any, indexOpt2: number) => (
                <td key={`${keyWOId}_${indexOpt2}`}
                    className={"px-6 py-4 whitespace-nowrap"}>
                    {!isPerformance ?
                        <Tooltip title="Go to this values" variant="soft" placement="top">
                            <Button variant='plain' color='neutral'
                                    onClick={() => selectValue(secondRow, firstRow, itm)}>
                                {getNumberOpts2ndLevelRow(secondRow, itm)}
                            </Button>
                        </Tooltip> :
                        getNumberOpts2ndLevelRow(secondRow, itm) === 0 ?
                            getNumberOpts2ndLevelRow(secondRow, itm) :
                            <ProgressProvider
                                total={getNumberOpts2ndLevelRow(secondRow, itm)}
                                percentage={getNumberPerformanceOptsRow(keyWOId, secondRow, itm, true)}/>
                    }
                </td>
            ))}
            {!isPerformance &&
                <td className={"px-6 py-4 whitespace-nowrap"}>
                    <Tooltip title="Go to this values" variant="soft" placement="top">
                        <Button variant='plain' color='neutral'
                                onClick={() => selectValue(secondRow, firstRow, null)}>
                            {getNumberTotal2ndLevelRow(secondRow)}
                        </Button>
                    </Tooltip>
                </td>}
        </tr>
        {(secondRow[thirdLevelSel?.value] && openRow) &&
            _.sortBy(secondRow[thirdLevelSel?.value], [(o) => {
                const getItmSort = `${thirdLevelSel?.value}_${thirdLevelSel?.key}` ?? ''
                return o[getItmSort];
            }]).map((thirdRow: any, thirdIndex: number) => (
                <React.Fragment
                    key={`level3-${firstIndex}-${secondIndex}-${thirdIndex}`}>
                    <Table3rdRowComponent
                        firstRow={firstRow}
                        secondRow={secondRow}
                        thirdRow={thirdRow}
                        optionsCond={optionsCond}
                        detailValueSel={detailValueSel}
                        thirdLevelSel={thirdLevelSel}
                        viewDrillDown={viewDrillDown}
                        isPerformance={isPerformance}
                        selectValue={selectValue}
                    />
                </React.Fragment>
            ))}
    </>);
};
