import {But<PERSON>, <PERSON><PERSON><PERSON>} from '@mui/joy';
import {limitString} from "@/app/_components/reports/func/limitString";
import _ from "lodash";
import {ProgressProvider} from '../../vendorPerf/ProgressProvider';
import {getNumberPerformanceOptsRow} from "@/app/_components/reports/func/getNumberPerformanceOptRow";
import {styleNumber} from "@/app/_components/utils/styleNumber";

export const Table3rdRowComponent = ({
                                         firstRow,
                                         secondRow,
                                         thirdRow,
                                         optionsCond,
                                         detailValueSel,
                                         thirdLevelSel,
                                         viewDrillDown,
                                         isPerformance,
                                         selectValue
                                     }) => {

    const getNumberOpts3rdLevelRow = (itmAdd: any, optItm: any) => {
        const get1stPartNameNum = (isPerformance) ? thirdLevelSel?.key : thirdLevelSel?.value
        const getNameOptNumber = `${get1stPartNameNum}_${optItm?.name || "null"}`;
        return styleNumber(itmAdd[getNameOptNumber] || 0);
    };

    const getNumberTotal3rdLevelRow = (row: any) => {
        const getTotalNameRow = `${thirdLevelSel?.value}_total`;
        return styleNumber(row[getTotalNameRow] || 0);
    };

    let keyWOId = (viewDrillDown) ?
        (_.includes(detailValueSel?.key, "_id")) ?
            _.replace(detailValueSel?.key, '_id', '') :
            (detailValueSel?.key ?? "") : ""
    keyWOId = detailValueSel?.fKey ? detailValueSel?.fKey : keyWOId;

    return (
        <tr>
            <td></td>
            <td></td>
            <td></td>
            <td className="px-6 py-4 whitespace-nowrap">
                {limitString(thirdRow[`${(isPerformance) ? thirdLevelSel?.key : thirdLevelSel?.value}`])}
            </td>
            {optionsCond && optionsCond.map((itm: any, indexOpt2: number) => (
                <td key={`${keyWOId}_${indexOpt2}`}
                    className="px-6 py-4 whitespace-nowrap">
                    {!isPerformance ?
                        <Tooltip title="Go to this values" variant="soft"
                                 placement="top">
                            <Button variant='plain' color='neutral'
                                    onClick={() => selectValue(secondRow, firstRow, itm)}>
                                {getNumberOpts3rdLevelRow(thirdRow, itm)}
                            </Button>
                        </Tooltip> :
                        getNumberOpts3rdLevelRow(thirdRow, itm) === 0 ?
                            getNumberOpts3rdLevelRow(thirdRow, itm) :
                            <ProgressProvider
                                total={getNumberOpts3rdLevelRow(thirdRow, itm)}
                                percentage={getNumberPerformanceOptsRow(thirdLevelSel?.key, thirdRow, itm, true)}/>
                    }
                </td>
            ))}
            {!isPerformance && <td className="px-6 py-4 whitespace-nowrap">
                <Tooltip title="Go to this values" variant="soft" placement="top">
                    <Button variant='plain' color='neutral'
                            onClick={() => selectValue(thirdRow, firstRow, null)}>
                        {getNumberTotal3rdLevelRow(thirdRow)}
                    </Button>
                </Tooltip>
            </td>}
        </tr>
    );
};
