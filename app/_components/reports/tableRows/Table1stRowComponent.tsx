import React, {useState} from "react";
import {Button, Icon<PERSON>utton, Tooltip} from '@mui/joy';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import {ProgressProvider} from "@/app/_components/vendorPerf/ProgressProvider";
import _ from "lodash";
import {styleNumber} from "@/app/_components/utils/styleNumber";
import {getNumberPerformanceOptsRow} from "@/app/_components/reports/func/getNumberPerformanceOptRow";
import {Table2ndRowComponent} from "@/app/_components/reports/tableRows/Table2ndRowComponent";
import {limitString} from "@/app/_components/reports/func/limitString";

export const Table1stRowComponent = ({
                                         firstRow,
                                         firstIndex,
                                         keyWOId,
                                         optionsCond,
                                         principalValueSel,
                                         detailValueSel,
                                         thirdLevelSel,
                                         viewDrillDown,
                                         view3rdLevel,
                                         isMatchRep,
                                         isPerformance,
                                         selectValue
                                     }) => {
    const [openRow, setOpenRow] = useState(true);
    const newKeyAdd = detailValueSel?.value

    const getNumberTotalRow = (row: any) => {
        const getTotalNameRow = (!_.isEmpty(newKeyAdd)) ? (detailValueSel?.value === 'none') ? "total"
            : `${principalValueSel.show}_total` : "total";
        return styleNumber(row[getTotalNameRow] || 0)
    };

    const getNumberOptsRow = (row: any, optItm: any) => {
        const getNameOptNumber = (!_.isEmpty(newKeyAdd)) ? (detailValueSel?.value === 'none') ? optItm?.name
            : `${principalValueSel.show}_${optItm?.name || "null"}` : optItm?.name || "null";
        return styleNumber(row[getNameOptNumber] || 0);
    };

    return (<>
        <tr className={(viewDrillDown && newKeyAdd !== "none") ? "bg-gray-100" : ""}>
            {viewDrillDown && newKeyAdd !== "none" && <td>
                <IconButton
                    aria-label="expand row"
                    variant="plain"
                    color="neutral"
                    size="sm"
                    onClick={() => setOpenRow(!openRow)}>
                    {openRow ? <KeyboardArrowUpIcon/> : <KeyboardArrowDownIcon/>}
                </IconButton>
            </td>}
            <td className="px-6 py-4 whitespace-nowrap"
                colSpan={(!viewDrillDown || newKeyAdd === "none") ? 1 : 2}>
                {limitString(!_.isEmpty(firstRow[principalValueSel?.show]) ? firstRow[principalValueSel?.show] : "Unknown")}
            </td>
            {(view3rdLevel && thirdLevelSel?.value !== "none") && <td></td>}
            {optionsCond && optionsCond.map((itm: any, indexOpt1: number) => (
                <td key={indexOpt1} className="px-6 py-4 whitespace-nowrap">
                    {!isPerformance ?
                        <Tooltip title="Go to this values" variant="soft" placement="top">
                            <Button variant='plain' color='neutral'
                                    onClick={() => selectValue(firstRow, null, itm)}>
                                {getNumberOptsRow(firstRow, itm)}
                            </Button>
                        </Tooltip> :
                        getNumberOptsRow(firstRow, itm) === 0 ?
                            getNumberOptsRow(firstRow, itm) :
                            <ProgressProvider
                                total={getNumberOptsRow(firstRow, itm)}
                                percentage={getNumberPerformanceOptsRow(principalValueSel.show, firstRow, itm, !_.isEmpty(principalValueSel?.drill_down))}/>
                    }
                </td>
            ))}
            {!isPerformance &&
                <td className="px-6 py-4 whitespace-nowrap">
                    <Tooltip title="Go to this values" variant="soft" placement="top">
                        <Button variant='plain' color='neutral'
                                onClick={() => selectValue(firstRow, null, null)}>
                            {getNumberTotalRow(firstRow)}
                        </Button>
                    </Tooltip>
                </td>}
        </tr>
        {(!_.isEmpty(firstRow[newKeyAdd]) && openRow) &&
            _.sortBy(firstRow[newKeyAdd], [(o) => {
                return o[keyWOId];
            }])?.map((secondRow: any, secondIndex: number) => (
                <React.Fragment key={`level2-${firstIndex}-${secondIndex}`}>
                    <Table2ndRowComponent
                        firstIndex={firstIndex}
                        secondIndex={secondIndex}
                        firstRow={firstRow}
                        secondRow={secondRow}
                        optionsCond={optionsCond}
                        detailValueSel={detailValueSel}
                        thirdLevelSel={thirdLevelSel}
                        viewDrillDown={viewDrillDown}
                        view3rdLevel={view3rdLevel}
                        isMatchRep={isMatchRep}
                        isPerformance={isPerformance}
                        selectValue={selectValue}
                    />
                </React.Fragment>
            ))}
    </>);
};
