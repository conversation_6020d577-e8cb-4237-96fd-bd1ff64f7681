import React from 'react';
import {Alert, IconButton, Modal} from '@mui/joy';
import CallMadeOutlinedIcon from '@mui/icons-material/CallMadeOutlined';
import CloseIcon from '@mui/icons-material/Close';
import CheckOutlinedIcon from '@mui/icons-material/CheckOutlined';
import {styledName} from "@/app/_components/utils/styledName";

export const ModalConsultComponent = ({
                                          open,
                                          handleClose,
                                          handleConfirm,
                                          tableName,
                                          valueSearch,
                                          optionSearch,
                                          requiredKey
                                      }) => {
    return (
        <Modal
            aria-labelledby="modal-title"
            aria-describedby="modal-desc"
            open={open}
            onClose={handleClose}
            sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
            <Alert
                startDecorator={<CallMadeOutlinedIcon/>}
                variant="solid"
                color="neutral"
                endDecorator={
                    <>
                        <IconButton variant="soft" size="sm" color="neutral" sx={{mr: 1}} onClick={handleConfirm}>
                            <CheckOutlinedIcon/>
                        </IconButton>
                        <IconButton variant="soft" size="sm" color="neutral" onClick={handleClose}>
                            <CloseIcon/>
                        </IconButton>
                    </>
                }>
                Do you want to see the {styledName(tableName)} on {valueSearch} with {styledName(requiredKey)} {optionSearch}
            </Alert>
        </Modal>
    );
};
