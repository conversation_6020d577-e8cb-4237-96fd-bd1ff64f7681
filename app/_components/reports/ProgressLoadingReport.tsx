import Box from "@mui/joy/Box";
import React from "react";
import {LinearProgress, Stack, Typography} from "@mui/joy";

export const ProgressLoadingReport: React.FC<any> = ({progressLabel, fetchProgress}) => {
    return (<>
        <Box
            sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                bgcolor: 'rgba(0, 0, 0, 0.4)',
                zIndex: 10,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                backdropFilter: 'blur(2px)',
                pointerEvents: 'all',
            }}
        >
            <Stack spacing={2} alignItems="center" sx={{width: '60%'}}>
                <Typography level="body-sm" fontWeight="bold" textColor="common.white" textAlign="center">
                    Generating the report...
                </Typography>
                <LinearProgress
                    determinate
                    variant="outlined"
                    color="neutral"
                    size="sm"
                    value={fetchProgress}
                    sx={{
                        width: '100%',
                        '--LinearProgress-radius': '20px',
                        '--LinearProgress-thickness': '24px',
                        position: 'relative',
                    }}
                >
                    <Typography
                        level="body-xs"
                        textColor="common.white"
                        sx={{fontWeight: 'xl', mixBlendMode: 'difference'}}
                    >
                        Compiling data... {`${Math.round(fetchProgress)}%`}
                    </Typography>
                </LinearProgress>

                {progressLabel && (
                    <Typography level="body-sm" textColor="common.white" fontWeight="bold" textAlign="center">
                        {progressLabel}
                    </Typography>
                )}
                <Typography level="body-sm" fontWeight="bold" textColor="common.white" textAlign="center">
                    We are compiling and consolidating the data.
                </Typography>
            </Stack>
        </Box>
    </>)
}
