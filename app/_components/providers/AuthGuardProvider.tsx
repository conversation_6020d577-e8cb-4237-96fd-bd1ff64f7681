'use client';

import React, {useEffect} from 'react';
import {usePathname, useRouter} from 'next/navigation';
import {useSession} from "@/app/_lib/auth/auth-client";
import {tryit} from 'radash';
import _ from "lodash";

type AuthUser = {
    id: string;
    email: string;
    dev?: boolean;
    role?: string;
    metadata?: string;
};

const PUBLIC_ROUTES = [
    '/',
    '/sign-up',
    '/sign-in',
    '/error',
    '/test',
    '/not-found',
    '/accept-invitation',
    '/maintenance',
];

const AUTH_ONLY_ROUTES = [
    '/welcome',
    '/user/settings',
    '/organizations',
    '/debug'
];

let lastMaintenanceCheck: number | null = null;
let lastMaintenanceValue: boolean | null = null;

const MAINTENANCE_TTL = 60 * 1000;

export function AuthGuardProvider({children}: { children: React.ReactNode }) {
    const {data, isPending} = useSession();
    const pathname = usePathname();
    const router = useRouter();

    useEffect(() => {
        if (_.includes(PUBLIC_ROUTES, pathname) || isPending) return;
        checkAccess();
    }, [pathname, isPending, data?.user]);

    const checkAccess = async () => {
        if (isPending || !data?.session) return;

        const session = data?.session || null;
        const user = data?.user as AuthUser | null;

        if (!_.includes(PUBLIC_ROUTES, pathname)) {
            if (!user?.dev) await checkMaintenanceMode();

            if (_.isEmpty(session) || _.isEmpty(user)) return router.replace('/sign-in');

            if (!_.includes(AUTH_ONLY_ROUTES, pathname)) {
                const metadata = _.attempt(() => JSON.parse(user?.metadata || '{}'));
                const allowedMenus = getMenusWithToolsAvailable(metadata?.menus ?? {});
                const accessResult = validateAccess(pathname, allowedMenus, user?.role || null);
                if (!accessResult.hasAccess) {
                    handleAccessDenied(pathname, accessResult.reason);
                }
            }
        }
    }

    const checkMaintenanceMode = async () => {
        const now = Date.now();

        if (lastMaintenanceCheck && (now - lastMaintenanceCheck < MAINTENANCE_TTL)) {
            if (lastMaintenanceValue) return router.replace('/maintenance');
            return;
        }
        const [fetchError, response] = await tryit(async () =>
            fetch('/api/public/status')
        )();

        if (fetchError) {
            console.error('Error checking maintenance mode:', fetchError);
            return;
        }

        const [parseError, {maintenance}] = await tryit(async () => response.json())();

        if (parseError) {
            console.error('Error parsing maintenance response:', parseError);
            return router.replace('/maintenance');
        }

        lastMaintenanceCheck = now;
        lastMaintenanceValue = maintenance;

        if (maintenance) return router.replace('/maintenance');
    }

    const validateAccess = (pathname: string, allowedMenus: string[], userRole: string) => {
        const isAdmin = userRole && userRole.includes('admin');
        if (isAdmin) return {hasAccess: true};

        if (_.isEmpty(allowedMenus)) return {hasAccess: false, reason: 'NO_MENUS'};

        const hasMenuAccess = _.some(allowedMenus, (menu: string) => pathname === `/${menu}`);

        return {hasAccess: hasMenuAccess, reason: hasMenuAccess ? null : 'MENU_ACCESS_DENIED'};
    }

    const handleAccessDenied = (pathname: string, reason: string) => {
        let url: string;

        switch (reason) {
            case 'NO_MENUS':
            case 'MENU_ACCESS_DENIED':
                url = `/not-found?from=${encodeURIComponent(pathname)}&reason=${reason}`;
                break;
            default:
                url = '/not-found?reason=NOT_FOUND';
                break;
        }

        return router.replace(url);
    };

    const getMenusWithToolsAvailable = (menuMetadata: any) => {
        return _.flatMap(_.values(menuMetadata), (menu: any) => _.get(menu, 'routes', []));
    };

    return <>{children}</>;
}
