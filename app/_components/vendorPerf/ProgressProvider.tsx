import React from "react";
import Typography from "@mui/joy/Typography";
import LinearProgress from "@mui/joy/LinearProgress";
import Box from "@mui/joy/Box";

export const ProgressProvider: React.FC<any> = ({total, percentage}) => {
    return (<>
        <Box sx={{ width: '120px', mt: 2 }}>
            <Typography>{total}</Typography>
        </Box>
        <Box sx={{ width: '60px', mt: 2 }}>
            <Typography>{percentage}%</Typography>
            <LinearProgress
                determinate
                color="neutral"
                variant="outlined"
                value={percentage}
                sx={{ mt: 1 }}
            />
        </Box>
    </>)
}