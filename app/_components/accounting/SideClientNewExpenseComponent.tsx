import {<PERSON>, Button} from "@mui/joy";
import React, {useEffect, useState} from "react";
import Modal from '@mui/joy/Modal';
import ModalClose from '@mui/joy/ModalClose';
import Sheet from '@mui/joy/Sheet';
import Typography from '@mui/joy/Typography';
import Input from "@mui/joy/Input";
import FormControl from "@mui/joy/FormControl";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import Decimal from "decimal.js";
import {InputReactNumberFormat} from "@/app/_components/inputReactNumberFormat/inputReactNumberFormat";
import {useEntity} from "simpler-state";
import {loadingCounting} from "@/app/_components/table/states/changeCountState";
import {formatNumber} from "@/app/_lib/utils/formatNumber";
import {setLoading} from "@/app/_components/Loading/loadingState";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import ExpenseRvnAmountType from "@/app/_components/utils/ExpenseRvnAmountType";
import {formatCurrency} from "@/app/_lib/utils/formatCurrency";
import {useSession} from "@/app/_lib/auth/auth-client";

export const SideClientNewExpenseComponent: React.FC<any> = ({action, dbTableName, onSuccess}) => {
    const {data: session, isPending} = useSession()
    const [userTimezone, setUserTimezone] = useState(null);
    const [openRevModal, setOpenRevModal] = React.useState<boolean>(false);
    const [amount, setAmount] = React.useState<any>(0);
    const [actionLabel, setActionLabel] = React.useState<any>('');
    const {filtersEntity, paginateConfigEntity} = createTableEntities(dbTableName);
    const loadingCount = useEntity(loadingCounting);
    const paginateConfig = useEntity(paginateConfigEntity);
    const [amountFor, setAmountFor] = useState('all')

    useEffect(() => {
        const {user}: any = session || {user: null};
        setUserTimezone(user?.timezone);
    }, [isPending]);

    const getAmountForEvent = () => {
        const decimalAmount = new Decimal(amount);
        let amountEachEvent = amount > 0 ? decimalAmount.div(paginateConfig.count).toDP(2).toNumber() : 0;
        if (amountFor === 'perItem') {
            amountEachEvent = amount;
        }
        return amountEachEvent;
    }

    const onCloseModal = () => {
        setOpenRevModal(false);
        setAmount(0)
        setActionLabel('')
        setAmountFor('all')
    }

    const createNewEvent = () => {
        setOpenRevModal(false);
        setLoading(true)
        const payload = {
            filters: filtersEntity.get(),
            amount: amount,
            totalEvents: paginateConfig.count,
            label: actionLabel,
            event: action === 'EXPENSE' ? action : 'BILLABLE_ACTION',
            eventAmount: amountFor,
            sourceTable: dbTableName,
            context: {timezone: userTimezone || "America/Chicago"}
        }
        fetch("/api/mongo/newEvent", {
            method: 'POST',
            body: JSON.stringify(payload),
        }).then(res => {
            setLoading(false)
            showSuccess(`$${formatCurrency(amount)} were applied to ${formatNumber(paginateConfig.count)} events`)
            setAmount(0)
            setActionLabel('');
            setAmountFor('all')
            onSuccess()
        }).catch(error => {
            setLoading(false);
            console.error(error);
            showError(`Error to apply Rvn/Exp`)
        });
    }

    const onChangeAmount = (event: any) => {
        if (event && event != '') {
            setAmount(Number.parseFloat(event))
        } else {
            setAmount('')
        }
    }

    const getLabelBtn = () => {
        const actionLabel = action === 'EXPENSE' ? 'Expense' : 'Billable action';
        const countLabel = loadingCount ? '...' : ` ${formatNumber(paginateConfig.count)} ${dbTableName}`;
        return `New ${actionLabel} for${countLabel}`;
    }

    const onChangeTypeAmount = (event: any) => {
        setAmountFor(event.target.value);
    }

    const getTotal = () => {
        const totalEvents = paginateConfig.count;
        if (amountFor === 'all') {
            const amountDecimal = new Decimal(amount);
            const eventsDecimal = new Decimal(totalEvents)
            const result = amountDecimal.div(eventsDecimal).toDP(2);
            return formatCurrency(result.mul(totalEvents).toNumber())
        } else if (amountFor === 'perItem') {
            const amountDecimal = new Decimal(amount);
            const eventsDecimal = new Decimal(totalEvents)
            return formatCurrency(amountDecimal.mul(eventsDecimal).toDP(2).toNumber())
        }
    }


    return <>
        <Box>
            <Button loading={loadingCount} disabled={paginateConfig.count <= 0} loadingPosition="start"
                    variant='outlined' color='neutral'
                    sx={{height: '30px'}} onClick={() => setOpenRevModal(true)}>
                {getLabelBtn()}
            </Button>
        </Box>
        <Box>
            <Modal
                aria-labelledby="modal-title"
                aria-describedby="modal-desc"
                open={openRevModal}
                onClose={() => setOpenRevModal(false)}
                sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}
            >
                <Sheet
                    variant="outlined"
                    sx={{
                        maxWidth: 500,
                        width: '100%',
                        borderRadius: 'md',
                        p: 5,
                        boxShadow: 'lg',
                    }}
                >
                    <ModalClose onClick={onCloseModal} variant="plain" sx={{m: 1}}/>
                    <Typography
                        component="h2"
                        id="modal-title"
                        level="h4"
                        textColor="inherit"
                        fontWeight="lg"
                        mb={3}
                        mt={2}
                    >
                        New {action === 'EXPENSE' ? 'Expense' : 'Billable action'}
                    </Typography>
                    <FormControl sx={{flex: 1}} size="lg">
                        <Typography level="body-lg">
                            {action === 'EXPENSE' ? 'Expense id' : 'Billable action id'}
                        </Typography>
                        <Input
                            size="lg"
                            type='text'
                            value={actionLabel}
                            onChange={(event) => setActionLabel(event.target.value)}
                        />
                    </FormControl>

                    <Typography level="body-lg" sx={{marginTop: '20px'}}>
                        Amount
                    </Typography>
                    <ExpenseRvnAmountType onChangeTypeAmount={onChangeTypeAmount}/>
                    <FormControl sx={{flex: 1, marginTop: '10px'}} size="lg">
                        <InputReactNumberFormat value={amount} changeValue={onChangeAmount}/>
                    </FormControl>

                    {actionLabel && actionLabel != '' && typeof amount != 'string' ?
                        <>
                            <Box sx={{marginTop: '20px'}}>
                                <Typography sx={{marginTop: '10px'}} level="body-lg">
                                    <span style={{fontWeight: 'bold'}}>${getAmountForEvent()}</span> for each item
                                    on <span
                                    style={{fontWeight: 'bold'}}>{formatNumber(paginateConfig.count)}</span> events for
                                    a total of <span
                                    style={{fontWeight: 'bold'}}>${getTotal()}</span>
                                </Typography>
                            </Box>
                            <Box sx={{textAlign: 'right', marginTop: '10px'}}>
                                <Button variant='outlined' onClick={onCloseModal} color='danger'
                                        sx={{height: '30px'}}>Cancel</Button>
                                <Button variant='outlined' color='neutral'
                                        disabled={paginateConfig.count <= 0}
                                        sx={{height: '30px', marginLeft: '10px'}}
                                        onClick={createNewEvent}>Apply</Button>
                            </Box>
                        </>
                        : null
                    }
                </Sheet>
            </Modal>
        </Box>
    </>
}
