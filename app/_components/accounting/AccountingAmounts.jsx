import Typography from "@mui/joy/Typography";
import {Box, Button, Tooltip} from "@mui/joy";
import FormControl from "@mui/joy/FormControl";
import React, {useEffect, useReducer} from "react";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import {InputReactNumberFormat} from "@/app/_components/inputReactNumberFormat/inputReactNumberFormat";
import SelectLabelComponent from "@/app/_components/accounting/SelectLabelComponent";
import _ from "lodash";

const initialState = [{amountLabel: '', amount: 0}];

const reducer = (state, action) => {
    switch (action.type) {
        case 'ADD_AMOUNT':
            return [...state, {amountLabel: '', amount: 0}];
        case 'UPDATE_AMOUNT':
            return state.map((item, index) =>
                index === action.index
                    ? {...item, ...action.payload}
                    : item
            );
        case 'REMOVE_AMOUNT':
            return state.filter((_, index) => index !== action.index);
        default:
            throw new Error(`Unknown action type: ${action.type}`);
    }
}

export const AccountingAmounts = ({loading, onChangesAmounts, labelsArray, refreshLabels}) => {
    const [amounts, dispatch] = useReducer(reducer, initialState);

    useEffect(() => {
        onChangesAmounts(amounts)
    }, [amounts, onChangesAmounts])

    const addAmount = () => {
        dispatch({type: 'ADD_AMOUNT'});
    };

    const updateAmount = (index, payload) => {
        if (payload?.amountLabel && payload?.amountLabel.label) {
            payload['amountLabel'] = _.trim(payload?.amountLabel.label);
        }
        dispatch({type: 'UPDATE_AMOUNT', index, payload});
    };

    const removeAmount = (index) => {
        dispatch({type: 'REMOVE_AMOUNT', index});
    };

    return (
        <Box className="amounts-container" sx={{marginBottom: '20px'}}>
            <Typography level="body-lg" sx={{marginTop: '20px'}}>
                Amounts
            </Typography>
            {amounts.map((amount, index) => (
                <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginTop: '10px'
                }}
                     key={`amount-${index}`}
                >
                    <FormControl size="lg" sx={{width: '50%'}}>
                        <Typography level="body-sm" sx={{marginTop: '5px'}}>
                            Label
                        </Typography>
                        <SelectLabelComponent
                            value={amount.amountLabel}
                            onChange={(newValue) =>
                                updateAmount(index, {amountLabel: newValue})
                            }
                            refreshLabels={refreshLabels}
                            labelsArray={labelsArray}
                        />
                    </FormControl>
                    <FormControl size="lg" sx={{width: '30%'}}>
                        <Typography level="body-sm" sx={{marginTop: '5px'}}>
                            Amount
                        </Typography>
                        <InputReactNumberFormat
                            size={'lg'}
                            value={amount.amount}
                            disabled={loading}
                            changeValue={(e) =>
                                updateAmount(index, {amount: e})
                            }
                        />
                    </FormControl>

                    <Box sx={{width: '10%'}}>
                        {amounts.length > 1 &&
                            <Tooltip title={'Delete'} size="sm">
                                <DeleteOutlineIcon onClick={() => removeAmount(index)}
                                                   sx={{cursor: 'pointer'}}/>
                            </Tooltip>
                        }
                    </Box>
                </Box>
            ))}

            <Box className="add-amount-btn"
                 sx={{display: 'flex', justifyContent: 'center', marginTop: '20px'}}>
                <Button variant='outlined'
                        color='neutral'
                        sx={{
                            height: '30px',
                            marginLeft: '10px',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                        }}
                        onClick={addAmount}
                >
                    <AddCircleIcon sx={{marginRight: '5px'}}/> Add Amount
                </Button>
            </Box>
        </Box>
    );
}

