import * as React from 'react';
import Autocomplete, {createFilterOptions} from '@mui/joy/Autocomplete';
import AutocompleteOption from '@mui/joy/AutocompleteOption';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import Modal from '@mui/joy/Modal';
import ModalDialog from '@mui/joy/ModalDialog';
import Button from '@mui/joy/Button';
import Input from '@mui/joy/Input';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import {CircularProgress} from "@mui/joy";

const filter = createFilterOptions();

export default function SelectLabelComponent({value, onChange, labelsArray, refreshLabels}) {
    const [open, toggleOpen] = React.useState(false);
    const [loading, setLoading] = React.useState(false);

    const handleClose = () => {
        setDialogValue({
            label: '',
        });
        toggleOpen(false);
    };

    const [dialogValue, setDialogValue] = React.useState({
        label: '',
    });

    const handleSubmit = (event) => {
        setLoading(true)
        const labelToLower = dialogValue.label.toLowerCase();
        event.preventDefault();
        fetch("/api/mongo/accounting/labels", {
            method: 'POST',
            body: JSON.stringify({label: labelToLower}),
        }).then(response => {
            onChange({
                label: labelToLower,
            });
            refreshLabels()
        }).catch(error => {
            console.error(error)
        }).finally(() => {
            handleClose();
            setLoading(false)
        });
    };

    return (
        <React.Fragment>
            <FormControl>
                <Autocomplete
                    value={value}
                    onChange={(event, newValue) => {
                        if (typeof newValue === 'string') {
                            setTimeout(() => {
                                toggleOpen(true);
                                setDialogValue({
                                    label: newValue.toLowerCase(),
                                });
                            });
                        } else if (newValue && newValue.inputValue) {
                            toggleOpen(true);
                            setDialogValue({
                                label: newValue.inputValue.toLowerCase(),
                            });
                        } else {
                            onChange(newValue);
                        }
                    }}
                    filterOptions={(options, params) => {
                        const filtered = filter(options, params);
                        if (params.inputValue !== '') {
                            filtered.push({
                                inputValue: params.inputValue,
                                label: `Add "${params.inputValue}"`,
                            });
                        }

                        return filtered;
                    }}
                    options={labelsArray}
                    getOptionLabel={(option) => {
                        if (typeof option === 'string') {
                            return option;
                        }
                        if (option.inputValue) {
                            return option.inputValue;
                        }
                        return option.label;
                    }}
                    freeSolo
                    selectOnFocus
                    clearOnBlur
                    handleHomeEndKeys
                    renderOption={(props, option) => (
                        <AutocompleteOption {...props} key={`label-${option._id}`}>{option.label}</AutocompleteOption>
                    )}
                    sx={{height: 44}}
                />
            </FormControl>
            <Modal open={open} onClose={handleClose}>
                <ModalDialog>
                    <form onSubmit={handleSubmit}>
                        <Typography
                            id="basic-modal-dialog-title"
                            component="h2"
                            level="inherit"
                            sx={{fontSize: '1.25em', mb: '0.25em'}}
                        >
                            Add a new label
                        </Typography>

                        <Stack spacing={2}>
                            <FormControl id="name">
                                <FormLabel>Label</FormLabel>
                                <Input
                                    autoFocus
                                    type="text"
                                    value={dialogValue.label.toLowerCase()}
                                    onChange={(event) =>
                                        setDialogValue({
                                            ...dialogValue,
                                            label: event.target.value.toLowerCase(),
                                        })
                                    }
                                />
                            </FormControl>

                            <Stack direction="row" spacing={2} sx={{justifyContent: 'flex-end'}}>
                                <Button variant="plain" color="neutral" disabled={loading} onClick={handleClose}>
                                    Cancel
                                </Button>
                                <Button variant='outlined'
                                        color='neutral'
                                        type="submit"
                                        disabled={loading}
                                        sx={{height: '40px', marginLeft: '10px'}}>
                                    {loading ? <span style={{display: 'flex'}}>
                                        Adding <CircularProgress color='neutral' size='sm'
                                                                 sx={{marginLeft: '10px'}}/>
                                    </span> : <span>
                                        Add
                                    </span>}
                                </Button>
                            </Stack>
                        </Stack>
                    </form>
                </ModalDialog>
            </Modal>
        </React.Fragment>
    );
}


