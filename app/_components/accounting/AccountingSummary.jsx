'use client'

import React, {useEffect, useState} from 'react'
import Sheet from '@mui/joy/Sheet'
import {Box, CircularProgress, Grid, Table, Tooltip} from "@mui/joy"

import ISOWeekFilter from "@/app/_components/filters/IsoWeeKFilter"
import {formatFilterIsoWeek} from "@/app/_components/utils/formatFilterIsoWeek.js"
import {AccountingUnassignedModal} from "@/app/_components/accounting/AccountingUnassignedModal.jsx"
import {useQueries} from "@tanstack/react-query"
import _ from "lodash"
import Typography from "@mui/joy/Typography"
import Loading from "react-loading";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {getDefaultIsoWeek} from "@/app/_components/utils/getDefaultIsoWeek.js";
import CachedIcon from '@mui/icons-material/Cached';
import {AccountingTheadTable} from "@/app/_components/accounting/AccountingTheadTable.js";
import {AccountingTbody} from "@/app/_components/accounting/AccountingTbody.js";
import {AccountingFilter} from "@/app/_components/accounting/AccountingFilter.jsx";
import Switch from '@mui/joy/Switch';
import SelectFilterComponent from "@/app/_components/table/filters/SelectFilterComponent";
import {AccountingAssignedModal} from "@/app/_components/accounting/AccountingAssignedModal.jsx";
import {DataTableAccounting} from "@/app/_components/queries/DataTableAccounting";
import {AccountingLabels} from "@/app/_components/queries/AccountingLabels";
import {OptionsAccountingOpts} from "@/app/_components/queries/OptionsAccountingOpts";
import {AssignInvoicesAcc} from "@/app/_components/queries/AssignInvoicesAcc";
import {useEntity} from "simpler-state";
import {setSummaryFilters, summaryFilters} from "@/app/_components/table/states/accountingFiltersState.js";
import {DebugFilters} from "@/app/_components/debug/DebugFilters";

const ACCOUNTING_TYPE = {REVENUE: 'REVENUE', EXPENSE: 'EXPENSE'}
const ACCOUNTING_TYPE_DEFAULT = {REVENUE: 'Revenue', EXPENSE: 'Expense'}

const defaultIsoWeek = getDefaultIsoWeek();

const tableBodyStyles = {
    '--TableCell-headBackground': 'var(--joy-palette-background-level1)',
    '--Table-headerUnderlineThickness': '1px',
    '--TableRow-hoverBackground': 'var(--joy-palette-background-level1)',
    '--TableCell-paddingY': '4px',
    '--TableCell-paddingX': '8px',
    minWidth: '800px'
}

const TransfersDurationFilter = ({onChangeDurationFilter, defaultMajorOperation = false}) => {
    const [majorDuration, setMajorDuration] = useState(defaultMajorOperation);
    useEffect(() => {
        onChangeDurationFilter(majorDuration)
    }, [majorDuration]);

    return (
        <Switch
            color={majorDuration ? 'success' : 'danger'}
            startDecorator={
                <Typography level="body-md" fontWeight="lg"
                            sx={[majorDuration ? {color: 'text.tertiary'} : {color: 'danger.600'}]}>
                    ≤ 120
                </Typography>
            }
            endDecorator={
                <Typography level="body-md" fontWeight="lg"
                            sx={[majorDuration ? {color: 'success.500'} : {color: 'text.tertiary'}]}>
                    ≥ 120
                </Typography>
            }
            checked={majorDuration}
            onChange={(event) => setMajorDuration(event.target.checked)}
        />
    );
}

export default function AccountingSummary() {
    const [selectedUnassigned, setSelectedUnassigned] = useState(null)
    const [openModal, setOpenModal] = useState(false)
    const [refreshLabels, setRefreshLabels] = useState(0);
    const [openAssignModal, setOpenAssignModal] = useState(false)
    const [assignSelectedInvoiceIds, setAssignSelectedInvoiceIds] = useState([])
    const [showIsoWeekFilter, setShowIsoWeekFilter] = useState(true);
    const [defaultIsoWeekForFilter, setDefaultIsoWeekForFilter] = useState({});

    const summaryFiltersEntity = useEntity(summaryFilters)

    const handleUnassignedClick = (value, vendorIndex, pubidIndex, item) => {
        setSelectedUnassigned({value, vendorIndex, pubidIndex, item});
        setOpenModal(true)
    }

    const handleAssignedClick = (value, vendorIndex, pubidIndex, item) => {
        const invoiceIds = summaryFiltersEntity.accountingType === ACCOUNTING_TYPE.REVENUE ? value?.invoice_revenue_ids : value?.invoice_expense_ids;
        setAssignSelectedInvoiceIds(invoiceIds || [])
        setOpenAssignModal(true)
    }

    const changeRangeDateFilter = (dates) => {
        if (dates.week.length > 1) {
            dates = {...dates, all: true}
        }
        const dateFilters = formatFilterIsoWeek(dates);
        setSummaryFilters(prev => ({...prev, isoWeekDates: dates, isoWeekDateFilter: dateFilters}));
    }


    const getDefaultIsoWeekDateFilter = () => {
        const isoWeekDates = summaryFiltersEntity.isoWeekDates;
        return isoWeekDates && !_.isEmpty(isoWeekDates)
            ? {weekNumber: isoWeekDates.week[0], year: isoWeekDates.year, week: isoWeekDates.week[0]}
            : {
                weekNumber: defaultIsoWeek.weekNumber,
                year: defaultIsoWeek.year,
                weekday: defaultIsoWeek.weekday,
                week: defaultIsoWeek.weekNumber
            }
    }

    const onErrorAccounting = () => {
        showError(`Error to generate Accounting. Try Again`)
    }

    const closeModal = () => {
        setOpenModal(false);
        setSummaryFilters({
            ...summaryFiltersEntity,
            refresh: summaryFiltersEntity.refresh + 1
        })
    }

    const [
        {data, isLoading, refetch},
        {data: accountingLabels},
        {data: optionSelectValues, isPending: isPendingOptionSelectValues},
        {data: assignSelectedInvoices, isLoading: assignSelectedInvoicesLoading}
    ] = useQueries({
        queries: [
            DataTableAccounting(
                summaryFiltersEntity.collection,
                summaryFiltersEntity.isoWeekDateFilter,
                summaryFiltersEntity.isoWeekDates,
                defaultIsoWeek,
                summaryFiltersEntity.accountingType,
                summaryFiltersEntity.refresh,
                summaryFiltersEntity.majorDuration,
                summaryFiltersEntity.selectFilters
            ),
            AccountingLabels(refreshLabels),
            OptionsAccountingOpts(refreshLabels, summaryFiltersEntity.accountingType, summaryFiltersEntity.collection, false),
            AssignInvoicesAcc(assignSelectedInvoiceIds)
        ]
    });

    const onGenerateAccounting = async () => {
        setOpenModal(false)
        showSuccess(`Your accounting is generating`);
        setSummaryFilters({
            ...summaryFiltersEntity,
            refresh: summaryFiltersEntity.refresh + 1
        })
    }

    const onChangeAccountingType = async (event) => {
        const accountingType = event.target.value ? event.target.value.toUpperCase() : '';
        setSummaryFilters({...summaryFiltersEntity, accountingType: accountingType})
    }

    const onChangeCollection = (event) => {
        const collection = event.target.value;
        setSummaryFilters({...summaryFiltersEntity, collection: collection})
    }

    const changeRageDurationFilter = (event) => {
        setSummaryFilters({...summaryFiltersEntity, majorDuration: event})
    }

    const onRefreshLabels = () => {
        setRefreshLabels(refreshLabels + 1);
    }

    const getTableLabel = () => {
        if (summaryFiltersEntity.collection === 'leads')
            return `Leads with disposition:new`;
        if (summaryFiltersEntity.collection === 'transfers')
            return `Transfers with duration ${summaryFiltersEntity.majorDuration ? '≥' : '≤'} 120`;
        if (summaryFiltersEntity.collection === 'postbacks')
            return 'Postbacks retained'
    }

    const applyFilterColumn = (event, key) => {
        setSummaryFilters((prevSummaryFilters) => ({
            ...prevSummaryFilters,
            selectFilters: (() => {
                const filters = {...prevSummaryFilters.selectFilters};
                switch (key) {
                    case "campaignKey":
                        filters.campaign_key = event;
                        break;
                    case "pubId":
                        filters[prevSummaryFilters.collection === 'transfers' ? 'pub_id' : 'pubid'] = event;
                        break;
                    case "vendor":
                        filters.vendor_id = event;
                        break;
                    case "client":
                        filters.client_id = event;
                        break;
                    default:
                        break;
                }
                return filters;
            })()
        }));

    };

    const getLabelFilter = (key) => {
        const labels = {campaignKey: "Campaign Key", pubId: 'Pubid', vendor: 'Vendor', client: 'Client'}
        return labels[key];
    }

    const getDefaultValue = (key) => {
        const selectFilters = {campaignKey: 'campaign_key', pubId: 'pubid', client: 'client_id', vendor: 'vendor_id'};
        return summaryFiltersEntity.selectFilters[selectFilters[key]]
    }

    const hideAssignModal = () => {
        setOpenAssignModal(false)
    }

    const destroyComponent = (event) => {
        setShowIsoWeekFilter(false);
        setDefaultIsoWeekForFilter({weekYear: event.year, weekNumber: 1, weekday: 5, week: 1})
        setTimeout(() => {
            setShowIsoWeekFilter(true)
        }, 500)
    }

    return (
        <Box>
            <DebugFilters
                filters={summaryFiltersEntity}/>
            <Box sx={{display: 'flex', flexDirection: 'column', height: {md: '85vh', sm: '100%'}}}>
                <Box sx={{textAlign: 'center', mb: 2}}>
                    <Typography level="h3">
                        Accounting
                    </Typography>
                </Box>
                <Box className="accounting-filters-container">
                    <Grid container spacing={4} sx={{display: 'flex', alignItems: 'center'}}>
                        <Grid className="date-filter" size={{md: 6, sm: 12}}>
                            <Typography level="body-md" fontWeight="lg" sx={{marginBottom: '5px'}}>
                                Date
                            </Typography>
                            {showIsoWeekFilter ?
                                <ISOWeekFilter onFilter={changeRangeDateFilter} showApplyFiltersButton={false}
                                               defaultIsoWeek={defaultIsoWeekForFilter} showAllWeeks={false}
                                               destroyComponent={destroyComponent} showAllOnChange={false}/>
                                : <CircularProgress color='neutral' size='sm' sx={{marginLeft: '10px'}}/>
                            }
                        </Grid>
                        <Grid className="accounting-for-container" size={{sm: 3, xs: 12}} sx={{display: 'flex'}}>
                            <AccountingFilter label={'Accounting for '} options={['Leads', 'Transfers', 'Postbacks']}
                                              defaultValue={summaryFiltersEntity.collection}
                                              onChange={onChangeCollection}/>
                            {summaryFiltersEntity.collection.toLowerCase() === 'transfers' &&
                                <Box sx={{marginTop: '5px'}}>
                                    <Typography level="body-md" fontWeight="lg" sx={{marginBottom: '5px'}}>
                                        Duration
                                    </Typography>
                                    <TransfersDurationFilter onChangeDurationFilter={changeRageDurationFilter}
                                                             defaultMajorOperation={summaryFiltersEntity.majorDuration}/>
                                </Box>
                            }
                        </Grid>
                        <Grid className="accounting-type-container" size={{sm: 3, xs: 12}} sx={{}}>
                            <AccountingFilter label={'Accounting type '} options={['Revenue', 'Expense']}
                                              defaultValue={ACCOUNTING_TYPE_DEFAULT[summaryFiltersEntity.accountingType]}
                                              onChange={onChangeAccountingType}/>
                        </Grid>
                        {optionSelectValues && Object.entries(optionSelectValues).map(([key, value]) => (
                            <Grid key={`filter-${key}`} className="accounting-type-container" size={{sm: 3, xs: 12}}
                                  sx={{}}>
                                <Typography level="body-md" fontWeight="lg" sx={{marginBottom: '5px'}}>
                                    {getLabelFilter(key)}
                                </Typography>
                                <SelectFilterComponent columnFilterValue={getDefaultValue(key)}
                                                       applyFilterColumn={(event) => applyFilterColumn(event, key)}
                                                       column={{columnDef: {meta: {config: {selectOptions: optionSelectValues[key] || []}}}}}
                                                       dbTableName={""}></SelectFilterComponent>
                            </Grid>
                        ))}

                    </Grid>
                </Box>
                <Box className="apply-filter-btns mb-3 text-right">
                    <Tooltip title={'Refresh'} size="sm" onClick={() => setSummaryFilters({
                        ...summaryFiltersEntity,
                        refresh: summaryFiltersEntity.refresh + 1
                    })}>
                        <CachedIcon sx={{marginLeft: '60px', cursor: 'pointer'}}/>
                    </Tooltip>
                </Box>
                <Box className="mb-3">
                    <Typography level="body-sm" fontWeight="bold">{getTableLabel()}</Typography>
                </Box>
                {!isLoading && !isPendingOptionSelectValues && data && data.length > 0 && < Sheet
                    variant="outlined"
                    sx={{
                        flexGrow: 1,
                        overflow: 'hidden',
                        display: 'flex',
                        flexDirection: 'column',
                        boxShadow: 'sm',
                        borderRadius: 'sm',
                    }}
                >
                    <Box sx={{overflow: 'auto', flexGrow: 1}}>
                        <Table stickyHeader sx={tableBodyStyles}>
                            <AccountingTheadTable collection={summaryFiltersEntity.collection}
                                                  accountingType={summaryFiltersEntity.accountingType}/>
                            <AccountingTbody data={data} collection={summaryFiltersEntity.collection}
                                             handleUnassignedClick={handleUnassignedClick}
                                             handleAssignedClick={handleAssignedClick}
                                             accountingType={summaryFiltersEntity.accountingType}/>
                        </Table>
                    </Box>
                </Sheet>
                }
                {isLoading || isPendingOptionSelectValues && <div className="flex justify-center mt-10">
                    <Loading color={'black'} width={'30px'} height={'30px'} type={'spin'}/>
                </div>}

                {
                    !isLoading && data && data.length <= 0 &&
                    <Box sx={{textAlign: 'center'}}>
                        <Typography level="md">
                            No Data found
                        </Typography>
                    </Box>
                }
                <AccountingUnassignedModal
                    selectedUnassigned={selectedUnassigned}
                    openModal={openModal}
                    accountingType={summaryFiltersEntity.accountingType}
                    table={summaryFiltersEntity.collection}
                    onSuccess={onGenerateAccounting}
                    onError={onErrorAccounting}
                    onCancel={closeModal}
                    defaultIsoWeek={getDefaultIsoWeekDateFilter()}
                    majorDuration={summaryFiltersEntity.majorDuration}
                    labelsArray={accountingLabels || []}
                    refreshLabels={onRefreshLabels}
                />

                <AccountingAssignedModal
                    openModal={openAssignModal}
                    items={assignSelectedInvoices?.response || []}
                    onCancel={hideAssignModal}
                    isLoading={assignSelectedInvoicesLoading}
                />

            </Box>
        </Box>
    )
}
