import React from "react";

const getShowColumnsLabel = (accountingType, collection) => {
    if (collection === 'leads') {
        return 'Campaign key - Pubid';
    }
    return accountingType === 'REVENUE' ? 'Campaign key - Pubid' : 'Campaign key - Pubid - Client';
};

export const AccountingTheadTable = ({collection, accountingType}) => {
    return <thead className="bg-gray-50 sticky top-0 z-10">
    <tr>
        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            style={{width: '15%'}}>
            {accountingType === 'REVENUE' ? 'Client' : 'Vendor'}
        </th>
        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            style={{width: '45%'}}>
            {getShowColumnsLabel(accountingType, collection)}
        </th>
        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            style={{width: '10%'}}>Assigned
        </th>
        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            style={{width: '15%'}}>Unassigned
        </th>
        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            style={{width: '15%'}}>Unit Amount
        </th>
    </tr>
    </thead>
}
