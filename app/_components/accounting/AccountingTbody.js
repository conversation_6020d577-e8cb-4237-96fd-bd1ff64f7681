import Button from "@mui/joy/Button";
import React from "react";
import _ from "lodash";
import {Tooltip} from "@mui/joy";
import Decimal from "decimal.js";
import {formatNumber} from "@/app/_lib/utils/formatNumber.js";
import {styleNumber} from "@/app/_components/utils/styleNumber.js";

const getTdByCollection = (pubid, collection, accountingType) => {
    const baseContent = (
        <>
            <strong style={{color: 'rgba(23,126,0,0.72)'}}>
                {get4CharactersCampaignKey(pubid.campaignKey)}
            </strong>
            -{pubid.pubid === '' ? "unknown pubid" : pubid.pubid}
        </>
    );
    const additionalContent = (
        accountingType === 'EXPENSE' && collection !== 'leads' && (
            <>
                -<strong style={{color: 'rgba(0,87,242,0.72)'}}>{pubid.client}</strong>
            </>
        )
    );
    return (
        <td className="px-6 py-4 whitespace-nowrap" style={getTdStyle(pubid)}>
            {baseContent}
            {additionalContent}
        </td>
    );
};


export const AccountingTbody = ({data, collection, handleUnassignedClick, accountingType, handleAssignedClick}) => {
    const getUnitAmount = (unitAmount) => {
        const decimalAmount = new Decimal(unitAmount);
        let amountEachEvent = unitAmount > 0 ? decimalAmount.div(100).toDP(2).toNumber() : 0;
        return formatNumber(amountEachEvent);
    }

    return <tbody className="bg-white divide-y divide-gray-200">
    {data && data.length > 0 && data.map((item, vendorIndex) => (
        item.pubids.map((pubid, pubidIndex) => (
            <tr key={`${item.groupId}-${pubidIndex}`}>
                {pubidIndex === 0 && (
                    <td rowSpan={item.pubids.length} style={{verticalAlign: 'top'}}>
                        {item[item.groupProperty]}
                    </td>
                )}
                <RunningTaskTooltip pubid={pubid}>
                    {getTdByCollection(pubid, collection, accountingType)}
                </RunningTaskTooltip>
                <RunningTaskTooltip pubid={pubid}>
                    <td className="px-6 py-4 whitespace-nowrap" style={getTdStyle(pubid)}>
                        {pubid.assigned > 0
                            ? <Button
                                variant="plain"
                                color="neutral"
                                disabled={pubid?.lock}
                                onClick={() => handleAssignedClick(pubid, vendorIndex, pubidIndex, item)}
                                sx={{
                                    width: '100%',
                                    justifyContent: 'flex-start',
                                    p: 0,
                                    cursor: 'pointer',
                                    padding: '5px'
                                }}
                            >
                                {pubid.assigned}
                            </Button> : <p>{styleNumber(pubid.assigned || 0)}</p>}
                    </td>
                </RunningTaskTooltip>
                <RunningTaskTooltip pubid={pubid}>
                    <td className="px-6 py-4 whitespace-nowrap" style={{...getTdStyle(pubid)}}>
                        <Button
                            variant="plain"
                            color="neutral"
                            disabled={pubid?.lock}
                            onClick={() => handleUnassignedClick(pubid, vendorIndex, pubidIndex, item)}
                            sx={{
                                width: '100%',
                                justifyContent: 'flex-start',
                                p: 0,
                                cursor: 'pointer',
                                padding: '5px'
                            }}
                        >
                            {styleNumber(pubid.unassigned || 0)}
                        </Button>
                    </td>
                </RunningTaskTooltip>
                <RunningTaskTooltip pubid={pubid}>
                    <td style={getTdStyle(pubid)}>${getUnitAmount(pubid.unitAmount)}</td>
                </RunningTaskTooltip>
            </tr>
        ))
    ))}
    </tbody>
}

function getTdStyle(pubId) {
    return {
        background: pubId?.lock ? '#ffe0e0' : 'none'
    };
}

function get4CharactersCampaignKey(campaignKey) {
    if (campaignKey && campaignKey.length > 4) {
        return _.slice(campaignKey, 0, 4).join('');
    } else {
        return 'unknown';
    }
}

const RunningTaskTooltip = ({children, pubid}) => (
    <Tooltip title={pubid.lock ? 'An Accounting task is running' : ''} size="sm">
        {children}
    </Tooltip>
);
