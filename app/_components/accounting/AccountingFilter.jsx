import * as React from 'react';
import Box from '@mui/joy/Box';
import Radio from '@mui/joy/Radio';
import RadioGroup from '@mui/joy/RadioGroup';
import Sheet from '@mui/joy/Sheet';
import Typography from "@mui/joy/Typography";

export const AccountingFilter = ({label, options, defaultValue, onChange}) => {
    return (
        <Box sx={{width: 300}}>
            <Typography level="body-md" fontWeight="lg">
                {label}
            </Typography>
            <RadioGroup
                aria-labelledby="storage-label"
                value={defaultValue}
                size="lg"
                sx={{gap: 1.5, display: 'flex', flexDirection: 'row', marginTop:'5px'}}
            >
                {options.map((value) => (
                    <Sheet key={value} sx={{p: 1, borderRadius: 'md', boxShadow: 'sm', height:'35px', display:'flex', alignItems:'center'}}>
                        <Radio
                            label={`${value}`}
                            overlay
                            disableIcon
                            value={value}
                            onChange={onChange}
                            slotProps={{
                                label: ({checked}) => ({
                                    sx: {
                                        fontSize: 'md',
                                        color: checked ? 'text.primary' : 'text.secondary',
                                    },
                                }),
                                action: ({checked}) => ({
                                    sx: (theme) => ({
                                        ...(checked && {
                                            '--variant-borderWidth': '2px',
                                            '&&': {
                                                borderColor: theme.vars.palette.primary[500],
                                            },
                                        }),
                                    }),
                                }),
                            }}
                        />
                    </Sheet>
                ))}
            </RadioGroup>
        </Box>
    );
};
