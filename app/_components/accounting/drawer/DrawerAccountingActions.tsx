import * as React from 'react';
import Drawer from '@mui/joy/Drawer';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';
import ModalClose from '@mui/joy/ModalClose';
import Divider from '@mui/joy/Divider';
import Sheet from '@mui/joy/Sheet';
import {Tooltip} from '@mui/joy';
import SettingsIcon from '@mui/icons-material/Settings';
import ConversionFinderOptions from "@/app/(private)/leads/_components/ConversionFinderOptions";

export const DrawerColumnsVisibilityConfiguration: React.FC<any> = ({
                                                                        onChangeAccountingType
                                                                    }) => {

    const [open, setOpen] = React.useState(false);

    const onChangeOption = (event) => {
        onChangeAccountingType(event);
        setOpen(false);
    }

    return (
        <>
            <Tooltip title="Generate Expense" variant="soft" placement="left">
                <SettingsIcon sx={{cursor: 'pointer'}} onClick={() => setOpen(true)}/>
            </Tooltip>
            <Drawer
                size="md"
                variant="plain"
                open={open}
                anchor={"right"}
                onClose={() => setOpen(false)}
                sx={{marginTop: '40px', maxWidth:'100px'}}
                slotProps={{
                    content: {
                        sx: {
                            bgcolor: 'transparent',
                            p: {md: 3, sm: 0},
                            boxShadow: 'none',
                            maxWidth:'300px'
                        },
                    },
                }}
            >
                <Sheet
                    sx={{
                        borderRadius: 'md',
                        p: 2,
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 2,
                        height: '100%',
                        overflow: 'none',
                        marginTop: '40px',
                    }}
                >
                    <DialogTitle>Accounting</DialogTitle>
                    <ModalClose/>
                    <Divider sx={{mt: 'auto'}}/>
                    <DialogContent sx={{gap: 2, overflowX: 'hidden'}}>
                        <ConversionFinderOptions onChange={onChangeOption}/>
                    </DialogContent>
                </Sheet>
            </Drawer>
        </>
    );
};
