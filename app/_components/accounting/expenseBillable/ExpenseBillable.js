import {DrawerColumnsVisibilityConfiguration} from "@/app/_components/accounting/drawer/DrawerAccountingActions";
import React, {useEffect, useState} from "react";
import {Box} from "@mui/joy";
import {SideClientNewExpenseComponent} from "@/app/_components/accounting/SideClientNewExpenseComponent";
import {useEntity} from "simpler-state";
import {setDataChangeCount} from "@/app/_components/table/states/changeDataState";
import _ from "lodash";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";

export const ExpenseBillable = ({tableName}) => {
    const [invoiceMode, setInvoiceMode] = useState(false);
    const [action, setAction] = useState('');
    const {
        filtersEntity,
        setFiltersEntity,
    } = createTableEntities(tableName);
    const filters = useEntity(filtersEntity);

    useEffect(() => {
        if (invoiceMode) {
            if (action === 'EXPENSE') {
                getItemsWithoutExpense()
            }
            if (action === 'REVENUE') {
                getItemsWithoutRevenue();
            }
        }
    }, [action]);

    const getItemsWithoutExpense = () => {
        let newFilters = {
            ...filters, 'cost_accounting_id-eq': {
                value: null,
                filterType: 'eq',
                notFilter: 'is',
                label: 'Accounting id'
            }
        }
        newFilters = _.omit(newFilters, "price_accounting_id-eq")
        setFiltersEntity(newFilters)
        setDataChangeCount()
    }

    const getItemsWithoutRevenue = () => {
        let newFilters = {
            ...filters, 'price_accounting_id-eq': {
                value: null,
                filterType: 'eq',
                notFilter: 'is',
                label: 'Accounting id'
            }
        }
        newFilters = _.omit(newFilters, "cost_accounting_id-eq")
        setFiltersEntity(newFilters)
        setDataChangeCount()
    }


    function onInvoiceMode() {
        setInvoiceMode(true);
    }

    const onGenerateExpense = () => {
        onInvoiceMode();
        setAction('EXPENSE')
    }

    const onGenerateRevenue = () => {
        onInvoiceMode();
        setAction('REVENUE')
    }

    const onEventSuccess = () => {
        let newFilters = filters
        newFilters = _.omit(newFilters, "cost_accounting_id-eq")
        newFilters = _.omit(newFilters, "price_accounting_id-eq")
        setFiltersEntity(newFilters)
        setDataChangeCount()
        setAction('');
    }

    const cancel = () => {
        onEventSuccess()
    }


    return (<>
        <DrawerColumnsVisibilityConfiguration onGenerateRevenue={onGenerateRevenue}
                                              onGenerateExpense={onGenerateExpense}
                                              onCancel={cancel}/>
        <Box sx={{display: 'flex'}}>
            {action !== '' &&
                <Box sx={{marginLeft: '10px'}}>
                    <SideClientNewExpenseComponent
                        onSuccess={onEventSuccess} action={action} dbTableName={tableName}/>
                </Box>
            }
        </Box>
    </>)
}
