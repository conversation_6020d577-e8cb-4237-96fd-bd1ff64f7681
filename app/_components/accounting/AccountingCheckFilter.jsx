import * as React from 'react';
import {useEffect} from 'react';
import Checkbox from '@mui/joy/Checkbox';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
import Box from "@mui/joy/Box";

export default function AccountingCheckFilter({options, defaultValues, onChange}) {
    const [value, setValue] = React.useState(defaultValues || []);

    const handleChange = (item, isChecked) => {
        setValue((currentValue) => {
            if (!isChecked) {
                if (currentValue.length === 1) {
                    return currentValue;
                }
                return currentValue.filter((text) => text !== item);
            } else {
                return [...currentValue, item];
            }
        });
    };
    useEffect(() => {
        onChange(value)
    }, [value]);

    return (
        <Box sx={{width: 300}}>
            <div role="group" aria-labelledby="rank">
                <List
                    orientation="horizontal"
                    wrap
                    sx={{
                        '--List-gap': '8px',
                        '--ListItem-radius': '10px',
                        '--ListItem-minHeight': '35px',
                        '--ListItem-gap': '4px',
                    }}
                >
                    {options.map((item) => (
                        <ListItem key={item}>
                            <Checkbox
                                size="sm"
                                disableIcon
                                overlay
                                label={item}
                                checked={value.includes(item)}
                                variant={value.includes(item) ? 'soft' : 'outlined'}
                                onChange={(event) => {
                                    handleChange(item, event.target.checked);
                                }}
                                slotProps={{
                                    action: ({checked}) => ({
                                        sx: checked
                                            ? {
                                                border: '1px solid',
                                                borderColor: 'primary.500',
                                            }
                                            : {},
                                    }),
                                }}
                            />
                        </ListItem>
                    ))}
                </List>
            </div>
        </Box>
    );
}
