import ModalDialog from "@mui/joy/ModalDialog";
import ModalClose from "@mui/joy/ModalClose";
import Typography from "@mui/joy/Typography";
import {Box, Button, CircularProgress} from "@mui/joy";
import FormControl from "@mui/joy/FormControl";
import Input from "@mui/joy/Input";
import Divider from "@mui/joy/Divider";
import ExpenseRvnAmountType from "@/app/_components/utils/ExpenseRvnAmountType";
import React, {useState} from "react";
import Modal from "@mui/joy/Modal";
import ISOWeekFilter from "@/app/_components/filters/IsoWeeKFilter";
import {formatFilterIsoWeek} from "@/app/_components/utils/formatFilterIsoWeek.js";
import {formatNumber} from "@/app/_lib/utils/formatNumber.js";
import {getFilterByCollection} from "@/app/_lib/utils/accounting/getFilterByCollection.js";
import {showError} from "@/app/_components/alerts/toast/ToastMessages";
import {AccountingAmounts} from "@/app/_components/accounting/AccountingAmounts.jsx";
import _ from "lodash";


export const AccountingUnassignedModal = ({
                                              selectedUnassigned,
                                              openModal,
                                              accountingType,
                                              table,
                                              onSuccess,
                                              onError,
                                              onCancel,
                                              defaultIsoWeek,
                                              majorDuration,
                                              labelsArray,
                                              refreshLabels
                                          }) => {
    const [invoiceLabel, setInvoiceLabel] = useState('')
    const [invoiceAmountType, setInvoiceAmountType] = useState('perItem')
    const [invoiceAmount, setInvoiceAmount] = useState(0)
    const [invoiceIsoWeekDateFilter, setInvoiceIsoWeekDateFilter] = useState({})
    const [loading, setLoading] = useState(false);
    const [amounts, setAmounts] = useState({});
    const [showIsoWeekFilter, setShowIsoWeekFilter] = useState(true);
    const [defaultIsoWeekForFilter, setDefaultIsoWeekForFilter] = useState({});

    const changeRangeDateFilter = (dates) => {
        setInvoiceIsoWeekDateFilter(dates)
    }

    const onChangeTypeAmount = (event) => {
        setInvoiceAmountType(event.target.value)
    }

    const getTotalAmount = () => {
        const summaryTotal = _.sumBy(amounts, item => Number(item.amount));
        const amount = invoiceAmountType === 'all' ? summaryTotal : summaryTotal * selectedUnassigned?.value?.unassigned;
        return formatNumber(amount);
    }

    const getAmountByItem = () => {
        const summaryTotal = _.sumBy(amounts, item => Number(item.amount));
        const amount = invoiceAmountType === 'all' ? summaryTotal / selectedUnassigned?.value?.unassigned : summaryTotal;
        return formatNumber(amount);
    }

    const disableButton = () => {
        return invoiceLabel && invoiceLabel !== '' && amounts && amounts.length > 0 && validateAmounts();
    }

    const validateAmounts = () => {
        let disable = false;
        amounts.forEach(item => {
            if (item.amountLabel === null || item.amountLabel === '') {
                disable = true;
            }
        });
        return !disable;
    }

    const onApply = () => {
        setLoading(true);
        const formatedIsoWeekDateFilter = formatFilterIsoWeek(defaultIsoWeek);
        let filters = getFilterByCollection(formatedIsoWeekDateFilter, table.toLowerCase(), majorDuration);
        const payload = {
            invoiceIsoWeekDateFilter,
            isoWeekDateFilter: defaultIsoWeek,
            invoiceLabel,
            invoiceAmount,
            amounts,
            invoiceAmountType,
            accountingType,
            collection: table.toLowerCase(),
            formatedIsoWeekDateFilter,
            items: selectedUnassigned?.value?.unassigned,
            vendorId: selectedUnassigned?.item?.vendor_id,
            clientId: selectedUnassigned?.item?.clientId ? selectedUnassigned?.item?.clientId : selectedUnassigned?.value?.clientId,
            pubId: selectedUnassigned?.value?.pubid,
            campaignKey: selectedUnassigned?.value?.campaignKey,
            dateFilters: filters
        };
        fetch("/api/mongo/accounting/generate", {
            method: 'POST',
            body: JSON.stringify(payload),
        }).then(response => {
            if (response.ok) {
                reset()
                onSuccess()
            } else {
                handleError(response)
            }
        }).catch(error => {
            console.error(error)
            onError()
        }).finally(() => {
            setLoading(false)
        });
    }

    const handleError = async (response) => {
        const error = await response.json();
        if (error && error.errorMessage) {
            showError(error.errorMessage);
            return;
        }
        showError('Error to generate accounting. Try again')
    }
    const reset = () => {
        setInvoiceLabel('');
        setInvoiceAmountType('perItem')
        setInvoiceAmount(0);
        setLoading(false)
    }

    const closeModal = () => {
        setDefaultIsoWeekForFilter({})
        reset();
        onCancel()
    }

    const destroyComponent = (event) => {
        setShowIsoWeekFilter(false);
        setDefaultIsoWeekForFilter({weekYear: event.year, weekNumber: 1, weekday: 5, week: 1})
        setTimeout(() => {
            setShowIsoWeekFilter(true)
        }, 500)
    }

    return <>
        <Modal open={openModal}>
            <ModalDialog size="lg" sx={{overflowY: 'auto'}}>
                {!loading &&
                    <ModalClose onClick={closeModal}/>
                }
                <Typography level="body-lg" sx={{marginTop: '20px'}}>
                    Create invoice to <strong>{selectedUnassigned?.value?.unassigned}</strong> items
                </Typography>
                <Box sx={{marginTop: '20px', width: '500px'}}>
                    <FormControl sx={{flex: 1, marginBottom: '30px'}} size="lg">
                        <Typography level="body-lg">
                            Invoice label
                        </Typography>
                        <Input
                            style={{marginTop: '10px'}}
                            size="lg"
                            type='text'
                            placeholder="Label"
                            disabled={loading}
                            value={invoiceLabel}
                            onChange={(event) => setInvoiceLabel(event.target.value)}
                        />
                    </FormControl>

                    <Divider orientation="horizontal"/>

                    <Box sx={{mb: 2}}>
                        <Typography level="body-lg" sx={{marginTop: '20px'}}>
                            Billing date
                        </Typography>
                        {showIsoWeekFilter ?
                            <ISOWeekFilter
                                showAllOnChange={false}
                                defaultIsoWeek={defaultIsoWeekForFilter && !_.isEmpty(defaultIsoWeekForFilter) ? defaultIsoWeekForFilter : defaultIsoWeek}
                                onFilter={changeRangeDateFilter}
                                showApplyFiltersButton={false} showAllWeeks={false}
                                destroyComponent={destroyComponent}/>
                            : <CircularProgress color='neutral' size='sm' sx={{marginLeft: '10px'}}/>
                        }
                    </Box>

                    <Divider orientation="horizontal"/>

                    <AccountingAmounts onChangesAmounts={setAmounts} labelsArray={labelsArray}
                                       refreshLabels={refreshLabels}/>

                    <Divider orientation="horizontal"/>
                    <Box sx={{marginTop: '20px', marginBottom: '20px'}}>
                        <ExpenseRvnAmountType onChangeTypeAmount={(event) => onChangeTypeAmount(event)}/>
                    </Box>
                    <Divider orientation="horizontal"/>
                    <Box>
                        <Typography level="body-lg" sx={{marginTop: '20px'}}>
                            Total amount <strong>${getTotalAmount()}</strong>
                        </Typography>
                        <Typography level="body-lg" sx={{marginTop: '20px'}}>
                            <strong>${getAmountByItem()}</strong> by item
                        </Typography>
                    </Box>
                </Box>
                {!loading &&
                    < Box sx={{textAlign: 'right', marginTop: '10px'}}>
                        <Button variant='outlined'
                                color='danger'
                                onClick={closeModal}
                                sx={{height: '30px'}}>Cancel</Button>
                        <Button variant='outlined'
                                color='neutral'
                                disabled={!disableButton()}
                                onClick={onApply}
                                sx={{height: '30px', marginLeft: '10px'}}>Apply</Button>
                    </Box>
                }
                {loading &&
                    <Box sx={{
                        textAlign: 'right',
                        marginTop: '10px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'end'
                    }}>
                        Generating Accounting <CircularProgress color='neutral' size='sm' sx={{marginLeft: '10px'}}/>
                    </Box>
                }
            </ModalDialog>
        </Modal>
    </>
}
