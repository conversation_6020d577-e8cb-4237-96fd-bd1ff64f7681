import {queryOptions} from "@tanstack/react-query";
import {getInvitationData} from "@/app/_components/queries/functions/getInvitationData";

export const InvitationDetails = (invitationId: string) => {
    return queryOptions({
        queryKey: [`invitation_details_${invitationId}`],
        queryFn: () => getInvitationData(invitationId),
        refetchOnWindowFocus: false,
        enabled: !!invitationId
    });
}
