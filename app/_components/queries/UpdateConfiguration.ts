import {queryOptions} from "@tanstack/react-query";
import {getUpdateConfiguration} from "@/app/_components/queries/functions/getUpdateConfiguration";

export const UpdateConfiguration = (dbTableName: string, jsonKeys: any, formData: any) => {
    return queryOptions({
        queryKey: [`${dbTableName}-update-configuration`, formData],
        queryFn: () => getUpdateConfiguration(dbTableName, jsonKeys, formData),
        refetchOnWindowFocus: false,
        enabled: true
    })
}
