import {queryOptions} from "@tanstack/react-query";
import {getPendingInvitations} from "./functions/getPendingInvitations";

export const PendingInvitationsQuery = (organizationId: string) => {
    return queryOptions({
        queryKey: ['pending_invitations', organizationId],
        queryFn: () => getPendingInvitations(organizationId),
        refetchOnWindowFocus: false,
        enabled: !!organizationId
    });
};
