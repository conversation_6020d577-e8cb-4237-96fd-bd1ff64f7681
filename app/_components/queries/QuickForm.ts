import {queryOptions} from "@tanstack/react-query";
import {getDataQuickForm} from "@/app/_components/queries/functions/getDataQuickForm";

export const QuickForm = (userId: string, menu: string) => {
    return queryOptions({
        queryKey: ["quick_form_admin"],
        queryFn: () => getDataQuickForm(userId, menu),
        staleTime: 0,
        refetchOnWindowFocus: false,
        enabled: (!!userId && !!menu)
    });
}
