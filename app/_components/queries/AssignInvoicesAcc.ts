import {queryOptions} from "@tanstack/react-query";
import {getAssignInvoices} from "@/app/_components/queries/functions/getAssignInvoices";

export const AssignInvoicesAcc = (assignSelectedInvoiceIds: any) => {
    return queryOptions({
        queryKey: [`invoice-assign-accounting`, assignSelectedInvoiceIds],
        queryFn: () => getAssignInvoices(assignSelectedInvoiceIds),
        refetchOnWindowFocus: false,
        enabled: true,
        retry: 3,
    });
}
