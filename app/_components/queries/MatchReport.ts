import {queryOptions} from "@tanstack/react-query";
import {getDataMatchReport} from "@/app/_components/queries/functions/getDataMatchReport";

export const MatchReport = (
    table: string,
    valueToMatch: any,
    optionSelected: any,
    onPartial?: (updater: (prev: any[]) => any[]) => void,
    progress?: {
        onProgressUpdate?: (percent: number, label: string) => void;
    }
) => {
    return queryOptions({
        queryKey: [`${table}-get-opts-match-report`, valueToMatch],
        queryFn: () => getDataMatchReport(table, valueToMatch, optionSelected, onPartial, progress),
        refetchOnWindowFocus: false,
        enabled: !!valueToMatch
    });
}
