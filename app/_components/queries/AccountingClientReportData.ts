import {queryOptions} from "@tanstack/react-query";
import _ from "lodash";
import {
    getAccountingClientReportDataTable
} from "@/app/_components/queries/functions/getAccountingClientReportDataTable";

export const AccountingClientReportData = (accountingFilters: any,accountingLabels:any) => {
    return queryOptions({
        queryKey: ['report-data-accounting-client', accountingFilters, accountingLabels],
        queryFn: () => getAccountingClientReportDataTable(accountingFilters,accountingLabels),
        refetchOnWindowFocus: false,
        enabled: !_.isEmpty(accountingFilters),
        retry: 3,
    })
}
