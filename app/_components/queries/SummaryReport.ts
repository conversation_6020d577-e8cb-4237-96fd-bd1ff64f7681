import {queryOptions} from "@tanstack/react-query";
import {getSummaryDataTable} from "@/app/_components/queries/functions/getSummaryDataTable";

export const SummaryReport = (
    table: string,
    principalValueSel: any,
    detailValueSel: any,
    thirdLevelSel: any,
    filterVendSel: any,
    dateFilter: any,
    datePicker: string,
    authMetadata: any,
    requiredKey: any,
    keysProAgg: any,
    optionsCond: any,
    availableVendors: any,
    availableClients: any,
    onPartial?: (updater: (prev: any[]) => any[]) => void,
    progress?: {
        onProgressUpdate?: (percent: number, label: string) => void;
    }
) => {
    return queryOptions({
        queryKey: [`${table}-summary-report`, dateFilter, principalValueSel, detailValueSel, thirdLevelSel, filterVendSel],
        queryFn: () => getSummaryDataTable(
            table,
            principalValueSel,
            detailValueSel,
            thirdLevelSel,
            filterVendSel,
            dateFilter,
            datePicker,
            authMetadata,
            requiredKey,
            keysProAgg,
            optionsCond,
            availableVendors,
            availableClients,
            onPartial,
            progress
        ),
        refetchOnWindowFocus: false,
        enabled: principalValueSel?.value !== "none"
    })
}
