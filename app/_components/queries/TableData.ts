import {queryOptions} from "@tanstack/react-query";
import {getTableData} from "@/app/_components/queries/functions/getTableData";
import {AppRouterInstance} from "next/dist/shared/lib/app-router-context.shared-runtime";

export const TableData = (
    dbTableName: string,
    dbName: string,
    filters: any,
    paginateConfig: any,
    tableColumns: any,
    tableDetails: any,
    router: AppRouterInstance,
    debugFilters: any = {}
) => {
    return queryOptions({
        queryKey: [`table_data_${dbTableName}`, filters, paginateConfig.page, paginateConfig.size, paginateConfig?.sort],
        queryFn: () => getTableData(dbTableName, dbName, filters, paginateConfig, tableColumns, tableDetails, router, debugFilters),
        refetchOnWindowFocus: false,
        enabled: true
    })
}
