import {queryOptions} from "@tanstack/react-query"
import {getDataTableAccounting} from "@/app/_components/queries/functions/getDataTableAccounting";

export const DataTableAccounting = (
    collection: string,
    isoWeekDateFilter: any,
    isoWeekDates: any,
    defaultIsoWeek: any,
    accountingType: string,
    refresh: number,
    majorDuration: boolean,
    selectFilters: any
) => {
    return queryOptions({
        queryKey: [`${collection}-accounting`, isoWeekDateFilter, accountingType, refresh, majorDuration, selectFilters],
        queryFn: () => getDataTableAccounting(
            collection.toLowerCase(),
            isoWeekDateFilter,
            isoWeekDates,
            defaultIsoWeek,
            accountingType,
            majorDuration,
            selectFilters
        ),
        refetchOnWindowFocus: false,
        enabled: true,
        retry: 3,
    })
}
