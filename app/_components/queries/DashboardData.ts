import {queryOptions} from "@tanstack/react-query";
import {getDashboardTotalEventsWithoutRvn} from "@/app/_components/queries/functions/getDashboardEvents";
import _ from "lodash";

export const DashboardData = (filters: any) => {
    return queryOptions({
        queryKey: [`dashboard_page_data`, filters],
        queryFn: () => getDashboardTotalEventsWithoutRvn(filters),
        refetchOnWindowFocus: false,
        enabled: !_.isEmpty(filters)
    });
}
