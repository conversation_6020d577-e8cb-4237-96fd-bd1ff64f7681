import {queryOptions} from "@tanstack/react-query";
import {getEditableCallCampaign} from "@/app/_components/queries/functions/getEditableCallCampaign";

export const CallCampaignScript = (callCampaignId: string) => {
    return queryOptions({
        queryKey: [`_id_${callCampaignId}`],
        queryFn: () => getEditableCallCampaign(callCampaignId),
        refetchOnWindowFocus: false,
        enabled: true
    });
}
