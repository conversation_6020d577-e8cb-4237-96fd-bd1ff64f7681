import {queryOptions} from "@tanstack/react-query";
import _ from "lodash";
import {
    getAccountingVendorReportDataTable
} from "@/app/_components/queries/functions/getAccountingVendorReportDataTable";

export const AccountingVendorReportData = (accountingFilters: any, accountingLabels: any) => {
    return queryOptions({
        queryKey: ['report-data-accounting-vendor', accountingFilters, accountingLabels],
        queryFn: () => getAccountingVendorReportDataTable(accountingFilters, accountingLabels),
        refetchOnWindowFocus: false,
        enabled: !_.isEmpty(accountingFilters),
        retry: 3,
    })
}
