import {queryOptions} from "@tanstack/react-query";
import {getSelectOptsAccounting} from "@/app/_components/queries/functions/getSelectOptsAccounting";

export const OptionsAccountingOpts = (refreshLabels: number, accountingType: any, collection: string, isReportConsult: boolean) => {
    return queryOptions({
        queryKey: [`${refreshLabels}-accounting-select-options`, accountingType, collection],
        queryFn: () => getSelectOptsAccounting(accountingType, collection, isReportConsult),
        refetchOnWindowFocus: false,
        enabled: true,
        retry: 3,
    })
}
