import {queryOptions} from "@tanstack/react-query";
import {getTableAdminDataConf} from "@/app/_components/queries/functions/getTableAdminDataConf";

export const TablesConfiguration = (tableNameEnt: any) => {
    return queryOptions({
        queryKey: ["tables_configuration_admin", tableNameEnt],
        queryFn: () => getTableAdminDataConf(tableNameEnt),
        refetchOnWindowFocus: false,
        enabled: !!tableNameEnt
    })
}
