import {queryOptions} from "@tanstack/react-query";
import {getPaginationCont} from "@/app/_components/queries/functions/getPaginationCont";

export const Pagination = (
    dbTableName: string,
    dbName: string,
    filters: any,
    paginateConfig: any,
    reFetchData: any,
    showOnlyPagButtons: boolean,
    tableDetails: any
) => {
    return queryOptions({
        queryKey: [`${dbTableName}-count`, filters, paginateConfig.page, paginateConfig.size, reFetchData],
        queryFn: () => getPaginationCont(showOnlyPagButtons, dbTableName, dbName, filters, paginateConfig,tableDetails),
        refetchOnWindowFocus: false,
        enabled: true
    });
}
