import {queryOptions} from "@tanstack/react-query"
import {getDataTabsNodeScripts} from "@/app/_components/queries/functions/getDataTabsNodeScripts";

export const NodeScriptsTabs = (
    tableName: string,
    paginateConfig: any,
    callCampaignId: string,
    dataNodeScriptsCount: any,
    tabNum: any
) => {
    return queryOptions({
        queryKey: [`conf_tabs_${tableName}`, paginateConfig.page, paginateConfig.size, callCampaignId, dataNodeScriptsCount],
        queryFn: () => getDataTabsNodeScripts(callCampaignId, tabNum),
        refetchOnWindowFocus: false,
        enabled: true
    });
}
