import {queryOptions} from "@tanstack/react-query";
import {getUserMetadata} from "@/app/_components/queries/functions/getUserMetadata";

export const AdminMetadata = (userSelected: any) => {
    return queryOptions({
        queryKey: ["user_admin_edit"],
        queryFn: () => getUserMetadata(userSelected),
        refetchOnWindowFocus: false,
        enabled: !!userSelected,
        staleTime: 0,
    })
}
