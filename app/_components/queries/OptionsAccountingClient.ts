import {queryOptions} from "@tanstack/react-query";
import {getSelectOptsClientAccounting} from "@/app/_components/queries/functions/getSelectOptsClientAccounting";

export const OptionsAccountingClient = (refreshLabels: number, accountingType: any, collection: string, isReportConsult: boolean) => {
    return queryOptions({
        queryKey: [`${refreshLabels}-accounting-client-select-options`, accountingType, collection],
        queryFn: () => getSelectOptsClientAccounting(accountingType, collection, isReportConsult),
        refetchOnWindowFocus: false,
        enabled: true,
        retry: 3
    })
}
