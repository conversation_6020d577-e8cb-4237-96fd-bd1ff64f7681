import {queryOptions} from "@tanstack/react-query";
import {getDataCallCampaign} from "@/app/_components/queries/functions/getDataCallCampaign";

export const DataCallCampaign = (callCampaignId: string) => {
    return queryOptions({
        queryKey: [`data_call_campaign_${callCampaignId}`],
        queryFn: () => getDataCallCampaign(callCampaignId),
        refetchOnWindowFocus: false,
        enabled: true
    });
}