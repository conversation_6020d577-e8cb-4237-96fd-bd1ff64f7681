import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getUpdateConfiguration = async (dbTableName: string, jsonKeys: any, formData: any) => {
    const getItmResp = await fetch("/api/mongo/getUpdateConfiguration", {
        method: 'POST',
        body: JSON.stringify({collection: dbTableName, jsonKeys, formData}),
    });
    if (getItmResp.ok) {
        return await getItmResp.json();
    } else {
        const {error} = await getItmResp.json();
        console.error('Error to get update configuration', error);
        showError(error || "Something wrong happened")
        return [];
    }
}
