import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getDataAdmin = async (resetValues: () => void) => {
    resetValues();
    const getResponseAdmin = await fetch("/api/mongo/getDataForAdminUsers", {
        method: 'POST',
        body: JSON.stringify({allData: false})
    });
    if (getResponseAdmin.ok) {
        const {response} = await getResponseAdmin.json() || {}
        return response || []
    } else {
        const {error} = await getResponseAdmin.json();
        console.error('Error to get admin data', error);
        showError(error || "Something wrong happened")
        return []
    }
}
