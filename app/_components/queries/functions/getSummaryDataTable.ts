import {buildFilters} from "@/app/_lib/nova/functions/utils/buildFilters";
import {tryit} from "radash";
import {summaryReportRequest} from "@/app/_components/reports/func/summaryReportRequest";
import _ from "lodash";
import {calculateDaysBetween} from "@/app/_components/reports/func/calculateDayBetween";
import {showWarning} from "@/app/_components/alerts/toast/ToastMessages";
import {splitDateRangeIntoHours} from "@/app/_components/reports/func/splitDateRangeIntoHours";
import {splitDateRangeIntoDays} from "@/app/_components/reports/func/splitDateRangeIntoDays";

export const getSummaryDataTable = async (
    table: string,
    principalValueSel: any,
    detailValueSel: any,
    thirdLevelSel: any,
    filterVendSel: any,
    dateFilter: any,
    datePicker: string,
    authMetadata: any,
    requiredKey: any,
    keysProAgg: any,
    optionsCond: any,
    availableVendors: any,
    availableClients: any,
    onPartialChunk?: (updater: (prev: any[]) => any[]) => void,
    progress?: {
        onProgressUpdate?: (percent: number, label: string) => void;
    }
) => {
    const timezone: string = typeof authMetadata?.timezone === 'string'
        ? authMetadata?.timezone
        : "America/Chicago";
    const getStartDate = _.get(dateFilter, `${datePicker}-between.value.start`) || "";
    const getEndDate = _.get(dateFilter, `${datePicker}-between.value.end`) || "";
    const getDaysBetween = calculateDaysBetween(getStartDate, getEndDate);
    const chunkDates = splitDateRangeIntoDays(getStartDate, getEndDate, timezone);
    const chunkDatesHours = splitDateRangeIntoHours(getStartDate, getEndDate, timezone);

    if (table === 'leads') {
        if (detailValueSel?.value !== "none" && _.isEmpty(filterVendSel)) {
            showWarning("Please select a vendor to generate this report")
            return [];
        }
        if (getDaysBetween > 30) {
            showWarning("Please select a date range less than 30 days for this report")
            return [];
        }
    }

    const mongoFilters = buildFilters(dateFilter, {timezone});
    const filters = (table === 'leads') ? {...dateFilter, ...filterVendSel} : dateFilter

    const commonPayloadSettings = {
        filters,
        mongoFilters,
        table,
        requiredKey,
        keysProAgg,
        selectObj: principalValueSel,
        selectDetail: detailValueSel,
        thirdLevelDetail: thirdLevelSel,
        optionsCond,
        pickFil: datePicker,
        context: {timezone},
        streamResults: true
    };

    const payload = {
        ...commonPayloadSettings,
        filters: table === 'leads' ? {...dateFilter, ...filterVendSel} : dateFilter,
        mongoFilters,
        table,
        requiredKey,
        keysProAgg,
        selectObj: principalValueSel,
        selectDetail: detailValueSel,
        thirdLevelDetail: thirdLevelSel,
        optionsCond,
        pickFil: datePicker,
        context: {timezone},
        dateOnlyMode: true,
        chunkDates,
        chunkDatesHours,
        availableVendors,
        availableClients
    };

    const [errorSumRep, responseSumRep] = await tryit(() =>
        summaryReportRequest(payload, onPartialChunk, progress)
    )();

    return responseSumRep || [];
}
