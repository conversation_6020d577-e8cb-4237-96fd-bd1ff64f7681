import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getParamAMPT = async (key: string) => {
    const getParamResp = await fetch("/api/ampt/getParam", {
        method: 'POST',
        body: JSON.stringify({key}),
    });
    if (getParamResp.ok) {
        return await getParamResp.json();
    } else {
        const {error} = await getParamResp.json();
        console.error('Error to get update configuration', error);
        showError(error || "Something wrong happened")
        return [];
    }
}
