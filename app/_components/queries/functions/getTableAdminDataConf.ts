import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getTableAdminDataConf = async (tableNameEnt: string) => {
    const getTableDataResponse = await fetch("/api/mongo/getTableConfiguration", {
        method: 'POST',
        body: JSON.stringify(tableNameEnt),
    });
    if (getTableDataResponse.ok) {
        const {response} = await getTableDataResponse.json();
        return response;
    } else {
        const {error} = await getTableDataResponse.json();
        console.error('Error to get table data for admin page', error);
        showError(error || "Something wrong happened")
        return {};
    }
}
