import {authClient} from "@/app/_lib/auth/auth-client";

export const getInvitationData = async (invitationId: string) => {
    const {data, error} = await authClient.organization.getInvitation({
        query: {
            id: invitationId
        }
    });
    if (error) {
        const getError = error?.message || 'Unknown error'
        console.error('Error to get invitation data', getError);
        throw new Error(error.message || 'Failed to fetch invitation details');
    }
    return data;
}
