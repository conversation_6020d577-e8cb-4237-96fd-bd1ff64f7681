import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getMatchRepData = async () => {
    const getDataMatchRep = await fetch("/api/mongo/reports/getOptsMatchReport", {
        method: 'GET',
    });
    if (getDataMatchRep.ok) {
        const {response} = await getDataMatchRep.json();
        return response || []
    } else {
        const {error} = await getDataMatchRep.json();
        console.error('Error to get match report data', error);
        showError(error || "Something wrong happened")
        return []
    }
}
