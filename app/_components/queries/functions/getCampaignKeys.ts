import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getCampaignKeys = async () => {
    const getItmResp = await fetch("/api/mongo/campaignKeys", {
        method: 'GET'
    });

    if (getItmResp.ok) {
        const response = await getItmResp.json();
        return response || [];
    } else {
        const {error} = await getItmResp.json();
        console.error('Error to get campaign keys', error);
        showError(error || "Something wrong happened")
        return [];
    }
}
