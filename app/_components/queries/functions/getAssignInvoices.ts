export const getAssignInvoices = async (assignSelectedInvoiceIds: string) => {
    if (assignSelectedInvoiceIds && assignSelectedInvoiceIds.length > 0) {
        const payload = {
            collection: 'invoices',
            query: {
                label: 1,
                total_events: 1,
                amount_by_event: 1,
                total_amount: 1
            },
            options: {
                sort: {
                    created_at: -1
                }
            },
            filters: {
                "_id-inF": {
                    "value": assignSelectedInvoiceIds,
                    filterType: 'inF',
                    label: '_id'
                }
            }
        }
        const invoices = await fetch("/api/mongo/getItems", {
            method: 'POST',
            body: JSON.stringify(payload),
        })
        return await invoices.json()
    } else {
        return []
    }
}
