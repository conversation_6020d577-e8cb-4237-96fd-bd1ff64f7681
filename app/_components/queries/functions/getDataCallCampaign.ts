import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getDataCallCampaign = async (callCampaignId: string) => {
    if (callCampaignId) {
        const payload = {
            campaignId: callCampaignId
        }
        const getMapData = await fetch("/api/mongo/call-center/get-variables", {
            method: 'POST',
            body: JSON.stringify(payload),
        })
        if (getMapData.ok) {
            const {response} = await getMapData.json() || {}
            return response || {}
        } else {
            const {error} = await getMapData.json();
            console.error('Error to script mapping data', error);
            showError(error || "Something wrong happened")
            return {}
        }
    } else {
        return {}
    }
}
