export const getDashboardTotalEventsWithoutRvn = async (filters: any) => {
    const response = await fetch("/api/mongo/getTotalEventsWithoutRvn", {
        method: 'POST',
        body: JSON.stringify(filters),
    });
    const getTotalEventsWithoutRvn = await response.json();
    const getColumnSumResponse = await fetch("/api/mongo/getSumColumn", {
        method: 'POST',
        body: JSON.stringify({filters, column: 'amount', collection: 'accounting'}),
    });
    const getColumnSumResponseJson = await getColumnSumResponse.json();
    return {response: getTotalEventsWithoutRvn ?? 0, totalAccountingAmount: getColumnSumResponseJson ?? 0};
}
