import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getDataMatchReport = async (
    table: string,
    valueToMatch: any,
    optionSelected: any,
    onPartial?: (updater: (prev: any[]) => any[]) => void,
    progress?: {
        onProgressUpdate?: (percent: number, label: string) => void;
    }
) => {
    try {
        const payload = {
            optionSelected,
            valueToMatch,
            table
        };

        const res = await fetch("/api/mongo/reports/generateMatchReport", {
            method: "POST",
            body: JSON.stringify(payload),
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!res.ok) {
            const errorText = await res.text();
            console.error("Error:", errorText);
            showError("Something went wrong while fetching match report");
            return [];
        }

        const {response, error} = await res.json();

        if (error) {
            showError(error || "Unknown error from match report");
            return [];
        }

        if (Array.isArray(response)) {
            onPartial?.((prev) => response);

            progress?.onProgressUpdate?.(
                100,
                `Completed loading match report`
            );

            return response;
        }

        return [];
    } catch (error) {
        console.error('Error to get match report data', error);
        showError(error || "Something wrong happened");
        return [];
    }
};
