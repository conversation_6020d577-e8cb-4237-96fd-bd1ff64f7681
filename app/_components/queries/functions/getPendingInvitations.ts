import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getPendingInvitations = async (organizationId: string) => {
    try {
        const response = await fetch('/api/organization/getPendingInvitations', {
            method: 'POST',
            body: JSON.stringify({ organizationId }),
            headers: {
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            throw new Error('Failed to fetch pending invitations');
        }

        const data = await response.json();
        return data.response || [];
    } catch (error) {
        console.error('Error fetching pending invitations:', error);
        showError("Failed to load pending invitations");
        return [];
    }
};