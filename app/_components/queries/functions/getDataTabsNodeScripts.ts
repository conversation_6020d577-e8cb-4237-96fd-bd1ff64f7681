import _ from "lodash";
import {setActiveNodeScriptTab} from "@/app/_components/table/states/nodeSriptsStates";
import {getScriptCategories} from "@/app/_components/callScriptsConf/funcs/getScriptCategories";

export const getDataTabsNodeScripts = async (
    callCampaignId: string,
    tabNum: any
) => {
    const transformTabNum = _.toInteger(tabNum);
    const tabsAvailable = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
    const getTabNum = (_.isNumber(transformTabNum) && _.includes(tabsAvailable, transformTabNum)) ?
        transformTabNum : 0;
    setActiveNodeScriptTab(getTabNum);

    return await getScriptCategories(callCampaignId);
}
