import _ from "lodash";
import {getAccountingFilters} from "@/app/_lib/utils/accounting/getAccountingFilters";

export const getDataTableAccounting = async (
    collection: string,
    isoWeekDateFilter: any,
    isoWeekDates: any,
    defaultIsoWeek: any,
    accountingType: string,
    majorDuration: boolean,
    selectFilters: any
) => {
    if (!_.isEmpty(isoWeekDateFilter)) {
        let filters = getAccountingFilters(isoWeekDateFilter, collection, majorDuration, selectFilters, accountingType)
        const summaryResponse = await fetch("/api/mongo/accounting/summary", {
            method: 'POST',
            body: JSON.stringify({
                collection: collection,
                filters: filters,
                accountingType: accountingType,
                isoWeekDateFilter: getDefaultIsoWeekDateFilter(isoWeekDates, defaultIsoWeek)
            }),
        })
        return await summaryResponse.json()
    } else {
        return []
    }
}


const buildSelectFilters = (selectFilters: any, collection: string, accountingType: string) => {
    const filterMap: any = {
        leads: ['campaign_key', 'pubid', 'vendor_id'],
        transfers: ['campaign_key', 'pubid', 'client_id', 'vendor_id'],
        postbacks: ['campaign_key', 'pubid', 'client_id', 'vendor_id'],
    };
    let filters = _.cloneDeep(selectFilters)
    if (accountingType === 'REVENUE') {
        filters = _.omit(selectFilters, ['vendor_id'])
    }
    const fields = filterMap[collection.toLowerCase()];
    return fields ? _.pick(filters, fields) : filters;
};


const getDefaultIsoWeekDateFilter = (isoWeekDates: any, defaultIsoWeek: any) => {
    return isoWeekDates && !_.isEmpty(isoWeekDates)
        ? {
            weekNumber: isoWeekDates.week[0],
            year: isoWeekDates.year,
            week: isoWeekDates.week[0]
        }
        : {
            weekNumber: defaultIsoWeek.weekNumber,
            year: defaultIsoWeek.year,
            weekday: defaultIsoWeek.weekday,
            week: defaultIsoWeek.weekNumber
        }
}
