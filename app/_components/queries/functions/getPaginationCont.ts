import {setLoadingCounting} from "@/app/_components/table/states/changeCountState";
import _ from "lodash";
import {showError} from "@/app/_components/alerts/toast/ToastMessages";
import {buildFilters} from "@/app/_lib/nova/functions/utils/buildFilters";

export const getPaginationCont = async (
    showOnlyPagButtons: boolean,
    dbTableName: string,
    dbName: string,
    filters: any,
    paginateConfig: any,
    tableDetails:any
) => {
    const payloadFilters = _.has(filters, 'defaultFilters') ? _.omit(filters, ['defaultFilters']) : filters;
    const context = {timezone: tableDetails?.userMetadata?.timezone || "America/Chicago"};
    const mongoFilters = buildFilters(filters, context);

    const payload = {
        filters: payloadFilters,
        options: {
            limit: paginateConfig.size
        },
        collection: dbTableName,
        dbName: dbName,
        context: context,
        mongoFilters
    }

    if (!showOnlyPagButtons) {
        setLoadingCounting(true);
        const countResponse = await fetch("/api/mongo/getCount", {
            method: 'POST',
            body: JSON.stringify(payload),
        });
        let getCountResult: any;
        if (countResponse.ok) {
            const {response} = await countResponse.json();
            getCountResult = response || {count: 0, totalPages: 1}
        } else {
            const {error} = await countResponse.json();
            console.error('Error to get count data', error);
            showError(error || "Something wrong happened")
            getCountResult = {count: 0, totalPages: 1}
        }
        setLoadingCounting(false)
        return getCountResult
    } else {
        return {count: 5000, totalPages: 1000}
    }
}
