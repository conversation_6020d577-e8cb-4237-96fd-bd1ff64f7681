export const getSelectOptsAccounting = async (accountingType: string, collection: string, isReportConsult: boolean) => {
    collection = collection.toLowerCase();
    const getSelectUrl = isReportConsult
        ? `/api/mongo/accounting/type/${accountingType}/collection/${collection}/reports/generateDistinctValues`
        : `/api/mongo/accounting/type/${accountingType}/collection/${collection}/generateDistinctValues`

    const summaryResponse = await fetch(getSelectUrl, {
        method: 'GET'
    })
    return await summaryResponse.json()
}
