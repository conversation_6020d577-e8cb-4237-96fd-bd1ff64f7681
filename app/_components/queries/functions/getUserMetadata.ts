import {showError} from "@/app/_components/alerts/toast/ToastMessages";
import {setTabsAdminData} from "@/app/_components/table/states/adminEditStates";
import {buildSchemaTableConfig} from "@/app/_components/admin/funcs/buildSchemaUserPMD";
import _ from "lodash";
import {styledName} from "@/app/_components/utils/styledName";
import {tryit} from "radash";

interface UserMetadata {
    metadata?: string;
    role?: string;
    timezone?: string;

    [key: string]: any;
}

export const getUserMetadata = async (userSelected: string): Promise<{ userMetadata?: any, [key: string]: any }> => {
    const [fetchError, getAuthMetadataResp] = await tryit(async () =>
        fetch("/api/betterAuth/getPrivateDataById", {
            method: 'POST',
            body: JSON.stringify(userSelected),
        })
    )();

    if (fetchError || !getAuthMetadataResp?.ok) {
        const errorMessage = fetchError ? "Network error" : "Failed to fetch user metadata";
        console.error('Error fetching user metadata:', fetchError || errorMessage);

        if (getAuthMetadataResp) {
            const [parseError, errorData] = await tryit(async () => getAuthMetadataResp.json())();
            if (!parseError && errorData?.error) {
                showError(errorData.error);
            } else {
                showError(errorMessage);
            }
        } else {
            showError(errorMessage);
        }

        return {};
    }

    const [parseError, userMetadata] = await tryit(async () => getAuthMetadataResp.json())();
    if (parseError) {
        console.error('Error parsing user metadata:', parseError);
        showError("Failed to parse user data");
        return {};
    }

    const dataReturned = await getAllSchemaData(userMetadata || {});
    return {userMetadata: userMetadata || {}, ...dataReturned};
}

const getAllSchemaData = async (authMetadata: UserMetadata = {}) => {
    const [fetchError, response] = await tryit(async () =>
        fetch("/api/mongo/getDataForAdminUsers", {
            method: 'POST',
            body: JSON.stringify({allData: true})
        })
    )();

    if (fetchError || !response?.ok) {
        const errorMessage = fetchError ? "Network error" : "Failed to fetch schema data";
        console.error('Error fetching schema data:', fetchError || errorMessage);

        if (response) {
            const [parseError, errorData] = await tryit(async () => response.json())();
            if (!parseError && errorData?.error) {
                showError(errorData.error);
            } else {
                showError(errorMessage);
            }
        } else {
            showError(errorMessage);
        }

        return {};
    }

    const [parseError, responseData] = await tryit(async () => response.json())();
    if (parseError) {
        console.error('Error parsing schema data:', parseError);
        showError("Failed to parse schema data");
        return {};
    }

    const getDataSchema = responseData?.response || {};
    const {tabs = [], roles = [], timezones = []} = getDataSchema;

    if (tabs?.length) {
        setTabsAdminData(tabs);
    }

    const {metadata, role, timezone} = authMetadata;

    const [jsonParseError, jsonMetadata] = await tryit(() =>
        metadata ? JSON.parse(metadata) : null
    )();

    if (jsonParseError) {
        console.error('Error parsing metadata JSON:', jsonParseError);
        showError("Failed to parse user metadata");
        return {};
    }

    const userRole = roles.find((rolInf: any) => rolInf.role === role);
    const userTimezone = timezones.find((tzInf: any) => tzInf.timezone === timezone);

    const userFormData = buildSchemaTableConfig(getDataSchema, jsonMetadata, "Save User Metadata");
    const templateFormData = userRole ? buildSchemaTableConfig(getDataSchema, userRole?.permissions, null) : null;
    const originalSchema = userRole ? buildSchemaTableConfig(getDataSchema, userRole?.permissions, null) : null;

    return {
        tabs,
        roles,
        timezones,
        templateVersion: userRole?.templateVersion || null,
        userRole: userRole ? {...userRole, label: styledName(userRole.role)} : null,
        timezoneSelected: userTimezone?.timezone,
        timezoneUser: userTimezone ? {...userTimezone, label: userTimezone.name} : null,
        formPermissions: userFormData?.formData,
        originalTemplate: originalSchema?.formData,
        warningPermissions: _.isEqual(userFormData?.formData, templateFormData?.formData),
        dataBuild: userFormData
    };
}
