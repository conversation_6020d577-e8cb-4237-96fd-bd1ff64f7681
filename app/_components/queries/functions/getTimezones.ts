import {showError} from "@/app/_components/alerts/toast/ToastMessages";

type timezone = {
    _id: string;
    abb: string;
    name: string;
    timezone: string;
}

export const getTimezones = async (): Promise<timezone[]> => {
    const getTZResponse = await fetch("/api/public/timezones", {
        method: 'GET'
    });

    if (getTZResponse.ok) {
        const {response} = await getTZResponse.json();
        return response || []
    } else {
        const {error} = await getTZResponse.json();
        console.error('Error to get timezones', error);
        showError(error || "Something wrong happened")
        return []
    }
}
