import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getTags = async (showFilters: boolean) => {
    const filters = (showFilters) ? {'show-eq': {value: true, filterType: 'eq', label: 'Show'}} : {};
    const payload = {
        filters,
        options: {
            sort: {'name': 1},
            skip: 0,
            limit: 0
        },
        collection: 'tags',
        query: {id: 1, name: 1}
    }

    const getItmResp = await fetch("/api/mongo/getItems", {
        method: 'POST',
        body: JSON.stringify(payload),
    });

    if (getItmResp.ok) {
        const {response} = await getItmResp.json();
        return response || [];
    } else {
        const {error} = await getItmResp.json();
        console.error('Error to get tags', error);
        showError(error || "Something wrong happened")
        return [];
    }
}
