import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getUsersOrganization = async (orgId: string) => {
    const getItmResp = await fetch("/api/organization/getOrgMembers", {
        method: 'POST',
        body: JSON.stringify({orgId}),
    });

    if (getItmResp.ok) {
        const {response} = await getItmResp.json();
        return response || [];
    } else {
        const {error} = await getItmResp.json();
        console.error('Error to get campaign keys', error);
        showError(error || "Something wrong happened")
        return [];
    }
}
