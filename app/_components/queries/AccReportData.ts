import {queryOptions} from "@tanstack/react-query";
import {getAccountingReportDataTable} from "@/app/_components/queries/functions/getAccountingReportDataTable";
import _ from "lodash";

export const AccReportData = (accountingFilters: any) => {
    return queryOptions({
        queryKey: ['report-data-accounting', accountingFilters],
        queryFn: () => getAccountingReportDataTable(accountingFilters),
        refetchOnWindowFocus: false,
        enabled: !_.isEmpty(accountingFilters),
        retry: 3,
    })
}
