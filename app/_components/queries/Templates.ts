import { queryOptions } from "@tanstack/react-query";
import {getDataAdmin} from "@/app/_components/queries/functions/getDataAdmin";

export const Templates = (resetValues: () => void) => {
    return queryOptions({
        queryKey: ["templates_management_tab"],
        queryFn: () => getDataAdmin(resetValues),
        staleTime: 0,
        refetchOnWindowFocus: false,
        enabled: true
    });
}
