'use client'
import * as React from 'react';
import {useEffect, useState} from 'react';
import Drawer from '@mui/joy/Drawer';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';
import ModalClose from '@mui/joy/ModalClose';
import Divider from '@mui/joy/Divider';
import Sheet from '@mui/joy/Sheet';
import Typography from '@mui/joy/Typography';
import {Alert, Box, Button, Chip, Tab, tabClasses, TabList, Tabs, Textarea, Tooltip} from '@mui/joy';
import JsonView from "react18-json-view";
import Card from "@mui/joy/Card";
import CardContent from "@mui/joy/CardContent";
import {usePathname, useRouter, useSearchParams} from "next/navigation";
import CheckIcon from '@mui/icons-material/Check';
import {showError} from "@/app/_components/alerts/toast/ToastMessages";
import {buildFilters} from "@/app/_lib/nova/functions/utils/buildFilters";
import {setDebugFilters} from "@/app/_components/table/states/debugFiltersState";
import AdbIcon from '@mui/icons-material/Adb';
import copy from "copy-to-clipboard";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import {useEntity} from "simpler-state";
import {useSession} from "@/app/_lib/auth/auth-client";


const getJson = (filters, timezone, dbTableName) => {
    if (dbTableName) {
        return buildFilters(filters, timezone);
    } else {
        return filters
    }
}

const CopyFiltersComponent = ({
                                  showCopiedMessage,
                                  loading,
                                  onCopyToClipboard,
                                  fullUrl,
                                  filters,
                                  timezone,
                                  dbTableName
                              }) => {
    return <Box>
        <Box sx={{textAlign: 'right', marginTop: '10px'}}>
            {showCopiedMessage &&
                <Chip
                    variant="outlined"
                    color="success"
                    size="sm"
                    endDecorator={<CheckIcon fontSize="medium"/>}>
                    Copied filters
                </Chip>
            }
            <Button variant='outlined'
                    color='neutral'
                    loading={loading}
                    onClick={onCopyToClipboard}
                    sx={{height: '30px', marginLeft: '10px'}}>Copy filters</Button>
        </Box>
        <Typography level="title-md" fontWeight="bold" sx={{mt: 1}}>
            Url
        </Typography>

        <Typography level={"title-sm"}>
            {fullUrl}
        </Typography>

        <Typography level="title-md" fontWeight="bold" sx={{mt: 1}}>
            Filters
        </Typography>

        <Card variant="soft">
            <CardContent>
                <Box className="json-view-container"
                     sx={{maxHeight: '400px', overflowY: 'auto'}}>
                    <JsonView style={{fontSize: '12px'}} src={getJson(filters, {timezone}, dbTableName)}
                              theme="github"
                              enableClipboard={false}
                              collapseStringsAfterLength={150}/>
                </Box>
            </CardContent>
        </Card>

    </Box>
}

const ApplyYourFilters = ({setSharedToken, sharedToken, showSignError, loading, applyFilters}) => {
    return <Box>
        <Typography level="title-md" fontWeight="bold" sx={{mt: 1}}>
            Apply your filters
        </Typography>
        <Textarea sx={{marginTop: '15px'}} minRows={15} maxRows={15}
                  onChange={(event) => setSharedToken(event.target.value)}
                  placeholder={'Paste here your filters'} value={sharedToken}/>

        {showSignError &&
            <Box sx={{marginTop: '10px'}}>
                <Alert color="danger" variant="outlined">
                    Your filters was modified, paste your original string.
                </Alert>
            </Box>
        }
        <Box sx={{textAlign: 'right', marginTop: '10px'}}>
            <Button variant='outlined'
                    color='neutral'
                    disabled={loading}
                    onClick={applyFilters}
                    loading={loading}
                    sx={{height: '30px'}}>
                Apply
            </Button>
        </Box>
    </Box>
}

export const DebugFilters: React.FC<any> = ({filters, dbTableName = undefined, options = {}}) => {
    const {data: session, isPending} = useSession();
    const [userTimezone, setUserTimezone] = useState(null);
    const [open, setOpen] = React.useState(false);
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const [fullUrl, setFullUrl] = useState("");
    const [tab, setTab] = useState(0);
    const [showCopiedMessage, setShowCopiedMessage] = useState(false);
    const [loading, setLoading] = useState(false);
    const [showSignError, setShowSignError] = useState(false);
    const [sharedToken, setSharedToken] = useState('');
    const router = useRouter();
    const {filtersEntity} = createTableEntities(dbTableName);
    const entityFilters = useEntity(filtersEntity);
    const filtersResult = dbTableName ? entityFilters : filters;

    const COPY_FILTERS_TAB = 0;
    const PASTE_FILTERS_TAB = 1

    useEffect(() => {
        if (isPending || !session) return;

        const {user}: any = session || {user: null};
        setUserTimezone(user?.timezone);
    }, [session, isPending]);

    useEffect(() => {
        if (typeof window !== "undefined") {
            setFullUrl(`${window.location.origin}${pathname}${searchParams ? `?${searchParams.toString()}` : ""}`);
        }
    }, [pathname, searchParams]);

    const onCopyToClipboard = async () => {
        setLoading(true);
        try {
            const filtersToSign =
                {
                    url: fullUrl,
                    filters: filtersResult,
                    pathname,
                    mongoFilters: buildFilters(filtersResult, {timezone: userTimezone}),
                    collection: dbTableName,
                    options
                };
            const signResponse = await fetch("/api/jwt/sign", {
                method: 'POST',
                body: JSON.stringify({payload: filtersToSign}),
            });
            if (signResponse.ok) {
                const {token} = await signResponse.json();
                copy(token)
                setShowCopiedMessage(true);
                setTimeout(() => {
                    setShowCopiedMessage(false)
                }, 2000)
            } else {
                showError('Error to sign the filters')
            }
        } catch (err) {
            alert("Error al copiar la URL");
            console.error("Error al copiar:", err);
        }
        setLoading(false);
    };


    const applyFilters = async () => {
        setShowSignError(false)
        setLoading(true)
        try {
            const validateResponse = await fetch("/api/jwt/validate", {
                method: 'POST',
                body: JSON.stringify({token: sharedToken}),
            });
            if (validateResponse.ok) {
                const payload = await validateResponse.json();
                setDebugFilters(payload)
                router.push('/debug')

            } else {
                setShowSignError(true)
            }
        } catch (error) {
            console.log('error:', error)
            setShowSignError(true);
        }
        setLoading(false)
    }

    return (
        <>
            <Tooltip title="Filter debug" variant="soft" placement="left">
                <AdbIcon
                    onClick={() => setOpen(true)}
                    sx={{
                        position: 'absolute',
                        right: '0',
                        marginRight: '10px',
                        cursor: 'pointer',
                        fontSize: '30px'
                    }}
                />
            </Tooltip>
            <Drawer
                size="md"
                variant="plain"
                open={open}
                anchor={"right"}
                onClose={() => setOpen(false)}
                sx={{marginTop: '40px', width: '400px'}}
                slotProps={{
                    content: {
                        sx: {
                            bgcolor: 'transparent',
                            p: {md: 3, sm: 0},
                            boxShadow: 'none',
                        },
                    },
                }}
            >
                <Sheet
                    sx={{
                        borderRadius: 'md',
                        p: 2,
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 2,
                        height: '100%',
                        overflow: 'auto',
                        marginTop: '40px',
                    }}
                >
                    <DialogTitle>Filter debug</DialogTitle>
                    <ModalClose/>
                    <Divider sx={{mt: 'auto'}}/>

                    <Tabs aria-label="tabs" defaultValue={0} sx={{bgcolor: 'transparent'}}
                          onChange={(event, value) => setTab(Number(value))}>
                        <TabList
                            disableUnderline
                            sx={{
                                gap: 0.4,
                                borderRadius: 'xl',
                                bgcolor: 'background.level1',
                                [`& .${tabClasses.root}[aria-selected="true"]`]: {
                                    boxShadow: 'sm',
                                    bgcolor: 'background.surface',
                                },
                            }}
                        >
                            <Tab sx={{width: '50%'}} disableIndicator>Share filters</Tab>
                            <Tab sx={{width: '50%'}} disableIndicator>Paste filters</Tab>
                        </TabList>
                    </Tabs>


                    <DialogContent sx={{gap: 2}}>
                        {tab === COPY_FILTERS_TAB &&
                            <CopyFiltersComponent filters={filtersResult}
                                                  onCopyToClipboard={onCopyToClipboard}
                                                  showCopiedMessage={showCopiedMessage}
                                                  loading={loading}
                                                  fullUrl={fullUrl}
                                                  timezone={userTimezone}
                                                  dbTableName={dbTableName}
                            />
                        }
                        {tab === PASTE_FILTERS_TAB &&
                            <ApplyYourFilters
                                applyFilters={applyFilters}
                                loading={loading}
                                setSharedToken={setSharedToken}
                                sharedToken={sharedToken}
                                showSignError={showSignError}/>
                        }

                    </DialogContent>
                </Sheet>
            </Drawer>
        </>
    );
};
