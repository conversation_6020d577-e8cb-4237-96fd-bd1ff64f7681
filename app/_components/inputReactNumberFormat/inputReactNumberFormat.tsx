import * as React from 'react';
import {NumericFormat, NumericFormatProps} from 'react-number-format';
import Input from '@mui/joy/Input';

interface CustomProps {
    onChange: (event: { target: { name: string; value: string } }) => void;
    name: string;
}

const NumericFormatAdapter = React.forwardRef<NumericFormatProps, CustomProps>(
    function NumericFormatAdapter(props, ref) {
        const {onChange, ...other} = props;

        return (
            <NumericFormat
                {...other}
                getInputRef={ref}
                onValueChange={(values) => {
                    onChange({
                        target: {
                            name: props.name,
                            value: values.value,
                        },
                    });
                }}
                thousandSeparator
                valueIsNumericString
                prefix="$"
            />
        );
    },
);

export const InputReactNumberFormat: React.FC<any> = ({value,changeValue}) => {
    return (
        <Input
            value={value}
            onChange={(event) => changeValue(event.target.value)}
            placeholder="$0.00"
            slotProps={{
                input: {
                    component: NumericFormatAdapter,
                },
            }}
        />
    );
}
