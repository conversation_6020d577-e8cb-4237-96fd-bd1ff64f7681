import React, {useEffect, useState} from "react";
import {useEntity} from "simpler-state";
import {Table} from "@/app/_components/table/Table";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import _ from "lodash";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {phraseGlobalSearch} from "@/app/_components/table/states/globalSearchStates";
import {globalSearchSettings} from "@/app/_components/mongo/functions/globalSearchSettings";
import * as onFiltersCustomMethods from "../../_lib/utils/filters/index"
import {TableData} from "@/app/_components/queries/TableData";
import {useRouter} from "next/navigation";
import {debugFilters} from "@/app/_components/table/states/debugFiltersState";

export const TableMongoProvider: React.FC<any> = ({
                                                      globalSearchConfig,
                                                      tableColumns,
                                                      dataTable,
                                                      dbTableName,
                                                      count,
                                                      tableDetails,
                                                      defaultFilters,
                                                      fullTableConfiguration,
                                                      hasSearchParams,
                                                      onClearFilters,
                                                      dbName
                                                  }) => {
    const {
        paginateConfigEntity,
        filtersEntity,
        setPaginateConfigEntity,
        setFiltersEntity,
    } = createTableEntities(dbTableName);
    const router = useRouter();
    const paginateConfig = useEntity(paginateConfigEntity);
    const globalSearch = useEntity(phraseGlobalSearch);
    const debugFiltersEntity = useEntity(debugFilters)
    const filters = useEntity(filtersEntity);
    const queryClient = useQueryClient();
    const [showColumns] = useState(tableColumns)

    useEffect(() => {
        if (globalSearch && !_.isEmpty(globalSearch)) {
            globalSearchSettings(
                setPaginateConfigEntity,
                setFiltersEntity,
                globalSearch,
                globalSearchConfig?.key,
                dbTableName,
                queryClient
            );
        }
    }, [globalSearch]);


    const onCustomFilterChange = (event: any) => {
        const onFilterFunc = onFiltersCustomMethods[event.customFilter.onFilter];
        if (onFilterFunc) {
            const customFilters = onFilterFunc(event);
            setFiltersEntity({...filters, ...customFilters})
        }
    }

    const {
        data,
        dataUpdatedAt,
        refetch,
        isFetching
    } = useQuery(TableData(dbTableName, dbName, filters, paginateConfig, tableColumns, tableDetails, router, debugFiltersEntity));

    const onDeleteRow = async (row: any) => {
        await refetch()
    }

    return (
        <div className="flex flex-col">
            <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                <div className='border-b border-gray-200 sm:rounded-lg p-1'>
                    <Table
                        globalSearchConfig={globalSearchConfig}
                        tableColumns={showColumns}
                        tableDetails={tableDetails}
                        dbTableName={dbTableName}
                        dbName={dbName}
                        dataUpdatedAt={dataUpdatedAt}
                        isLoading={isFetching}
                        reFetch={refetch}
                        count={count}
                        data={data}
                        loading={isFetching}
                        fullTableConfiguration={fullTableConfiguration}
                        onCustomFilterChange={onCustomFilterChange}
                        hasSearchParams={hasSearchParams}
                        onClearFilters={onClearFilters}
                        onDeleteRow={onDeleteRow}
                    />
                </div>
            </div>
        </div>
    );
};

