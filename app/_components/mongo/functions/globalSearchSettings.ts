import {setShowFiltersGS, setShowOnlyPagBut} from "@/app/_components/table/states/globalSearchStates.js";
import {QueryClient} from "@tanstack/react-query";

export const globalSearchSettings = (
    setPaginateConfigEntity: any,
    setFiltersEntity: any,
    globalSearch: string,
    searchKey: string,
    dbTableName: string,
    queryClient: QueryClient
) => {
    const buildName = `${searchKey}-globalS`
    const newObj = {[buildName]: {value: globalSearch, filterType: "globalS"}}
    setPaginateConfigEntity({
        action: "NEXT",
        count: 5000,
        cursor: 0,
        hasNextPage: true,
        minCursor: 0,
        page: 0,
        size: 5,
        totalPages: 1000
    })
    setFiltersEntity(newObj)
    setShowOnlyPagBut(true)
    setShowFiltersGS(false)
    queryClient.invalidateQueries({queryKey: [`table_data_${dbTableName}`]});
}
