'use client';
import Box from "@mui/joy/Box";
import React, {useEffect} from "react";
import {DrawerAddNewRegister} from "@/app/_components/registers/DrawerAddNewRegister";
import {TableReports} from "@/app/_components/reports/TableReports";
import {MatchUnknownDataComponent} from "@/app/_components/admin/tools/MatchUnknownDataComponent";
import {DebugFilters} from "@/app/_components/debug/DebugFilters";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity.js";
import {useEntity} from "simpler-state";
import {changeShowColumnsEntity} from "@/app/_components/table/states/changeShowColumns.js";
import {UploadClientPostback} from "@/app/(private)/entities/clients/_components/UploadClientPostback.jsx";
import {usePathname} from 'next/navigation';
import {getToolsByRoute} from "@/app/_components/utils/getToolsByRoute.js";

export const Tools = ({tableConfig, additionalTools = [], dataForTool = [], hasTitle = false, tableColumns}) => {
    const {
        filtersEntity,
        columnsVisibilityEntityConfiguration,
    } = createTableEntities(tableConfig?.tableName);
    const filters = useEntity(filtersEntity);
    const columns = useEntity(columnsVisibilityEntityConfiguration);
    const changeShowColumns = useEntity(changeShowColumnsEntity);
    const pathname = usePathname();

    useEffect(() => {
        //useEffect for update columns
    }, [changeShowColumns])

    const toolComponents = [
        {
            key: 'new',
            component: <DrawerAddNewRegister tableName={tableConfig?.tableName}
                                             tableColumns={tableConfig?.tableColumns}/>
        },
        {key: 'reporting', component: <TableReports table={tableConfig?.tableName}/>},
        {key: 'upload', component: <UploadClientPostback table={tableConfig?.tableName} clients={dataForTool}/>},
        {key: 'match', component: <MatchUnknownDataComponent table={tableConfig?.tableName}/>},
        {
            key: 'filterDebug',
            component: <DebugFilters filters={filters} dbTableName={tableConfig?.tableName} options={columns}/>
        },
    ];

    const getTools = (pathname, tools) => {
        const pathWithoutFirstSlash = pathname.slice(1);
        return getToolsByRoute(pathWithoutFirstSlash, tools)
    }

    const getTool = (key)=>{
        return toolComponents.find((tool) => tool.key === key)
    }

    return (
        <Box sx={{
            width: 50,
            position: 'absolute',
            right: 0,
            textAlign: 'center',
            background: 'transparent',
            marginTop: '70px'
        }}>
            {getTools(pathname, tableConfig.userMetadata?.menus).map((tool) => (
                <Box key={tool} sx={{mb: 3}}>
                    {getTool(tool)?.component}
                </Box>
            ))}
            {additionalTools.map((tool, index) => (
                <Box key={index} sx={{mb: 3}}>
                    {tool}
                </Box>
            ))}
        </Box>
    );
};
