import {RegistryWidgetsType} from "@rjsf/utils";
import {CustomCheckbox} from "@/app/_components/customForms/widgets/CustomCheckbox";
import {styledName} from "@/app/_components/utils/styledName";
import {ObjectTabsAdminTemplate} from "@/app/_components/customForms/templates/ObjectTabsAdminTemplate";
import {ObjectContentAdminTemplate} from "@/app/_components/customForms/templates/ObjectContentAdminTemplate";
import {buildMetadataForm} from "@/app/_components/admin/funcs/buildMetadataForm";
import {removeStyleString} from "@/app/_components/utils/removeStyleString";
import {ObjectAdminAccordingTemplate} from "@/app/_components/customForms/templates/ObjectAdminAccordingTemplate";
import _ from "lodash";
import {
    ObjectTableBodyUserAdminTemplate
} from "@/app/_components/customForms/templates/ObjectTableBodyUserAdminTemplate";

export const buildSchemaTableConfig = ({
                                           collections,
                                           menuConfiguration,
                                           keysCollections,
                                           vendors,
                                           clients
                                       }, authMetadata: any, textSubmit: string): any => {
    const groupKeysColl = ['leads', 'transfers', 'postbacks']
    const widgets: RegistryWidgetsType = {
        CheckboxWidget: CustomCheckbox,
    }

    const formData = buildMetadataForm({collections, menuConfiguration, keysCollections, vendors, clients}, authMetadata);

    const properties = {
        menus: {
            "type": "array",
            "properties": []
        },
        restrictions: {
            "type": "object",
            "properties": {
                clients: {
                    "type": "object",
                    "properties": {}
                },
                vendors: {
                    "type": "object",
                    "properties": {}
                },
            }
        },
        table_keys: {
            "type": "object",
            "properties": {}
        }
    };
    const uiSchema = {
        "ui:submitButtonOptions": {
            "submitText": textSubmit,
            "norender": (_.isEmpty(textSubmit)),
            "props": {"type": "primary"}
        },
        "ui:ObjectFieldTemplate": ObjectTabsAdminTemplate,
        menus: {"ui:options": {label: false}},
        restrictions: {
            "ui:ObjectFieldTemplate": ObjectAdminAccordingTemplate,
            vendors: {"ui:ObjectFieldTemplate": ObjectContentAdminTemplate, "ui:options": {label: false}},
            clients: {"ui:ObjectFieldTemplate": ObjectContentAdminTemplate, "ui:options": {label: false}}
        },
        table_keys: {"ui:ObjectFieldTemplate": ObjectAdminAccordingTemplate}
    };


    collections.forEach((collObj: any) => {
        const collNameStyled = removeStyleString(collObj.name)
        properties.menus.properties[collNameStyled] = {
            "type": "array",
            "properties": []
        }
        properties.menus.properties[collNameStyled].properties.showMenu = {
            "type": "boolean",
        }
        uiSchema.menus[collNameStyled] = {
            "ui:ObjectFieldTemplate": ObjectTableBodyUserAdminTemplate
        }
        uiSchema.menus[collNameStyled].showMenu = {
            "ui:widget": "checkbox",
            "ui:options": {label: false}
        }
        if (collObj?.tools && collObj?.tools.length > 0) {
            collObj?.tools.forEach((toolName: string) => {
                properties.menus.properties[collNameStyled].properties[toolName] = {
                    "type": "boolean",
                    "title": `${styledName(toolName)}`
                }
                uiSchema.menus[collNameStyled][toolName] = {
                    "ui:widget": "checkbox"
                }
            })
        }
        if (keysCollections[collObj.name] && keysCollections[collObj.name].length > 0) {
            properties.table_keys.properties[collNameStyled] = {
                "type": "object",
                "title": `${styledName(collObj.name)}`,
                "properties": {}
            }
            uiSchema.table_keys[collNameStyled] = {
                "ui:options": {label: false},
                "ui:ObjectFieldTemplate": ObjectContentAdminTemplate,
            }
            keysCollections[collObj.name].forEach((keyName: any) => {
                let propKeyName = keyName.key
                let propShowName = keyName.show
                if (_.includes(groupKeysColl, collObj.name)) {
                    if (_.includes(propKeyName, "vendor") || _.includes(propKeyName, "client")) {
                        propKeyName = (_.includes(propKeyName, "vendor")) ? "vendor" : "client"
                        propShowName = propKeyName
                    }
                }
                properties.table_keys.properties[collNameStyled].properties[propKeyName] = {
                    "type": "boolean",
                    "title": `${styledName(propShowName)}`
                }
                uiSchema.table_keys[collNameStyled][propKeyName] = {
                    "ui:widget": "checkbox"
                }
            })
        }
    })

    vendors.forEach((venData: any) => {
        properties.restrictions.properties.vendors.properties[venData.id] = {
            "type": "boolean",
            "title": `${styledName(venData.name)}`
        }
        uiSchema.restrictions.vendors[venData.id] = {
            "ui:widget": "checkbox"
        }
    })

    clients.forEach((cliData: any) => {
        properties.restrictions.properties.clients.properties[cliData.id] = {
            "type": "boolean",
            "title": `${styledName(cliData.name)}`
        }
        uiSchema.restrictions.clients[cliData.id] = {
            "ui:widget": "checkbox"
        }
    })

    return {
        schema: {
            type: 'object',
            properties
        },
        uiSchema,
        formData,
        widgets
    };
}
