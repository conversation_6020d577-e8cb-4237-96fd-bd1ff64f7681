export const buildFormDataSwitch = (formData: any, activeTab: string, valueSwitch: boolean) => {
    const updatedObj = {};
    const getObjFormData = formData[activeTab]
    for (const firstKey in getObjFormData) {
        if (typeof getObjFormData[firstKey] === 'boolean') {
            updatedObj[firstKey] = valueSwitch;
        } else {
            const secondObjFormData = getObjFormData[firstKey]
            updatedObj[firstKey] = {}
            for (const secondKey in secondObjFormData) {
                updatedObj[firstKey][secondKey] = valueSwitch
            }
        }
    }
    formData[activeTab] = updatedObj;
    return formData
}