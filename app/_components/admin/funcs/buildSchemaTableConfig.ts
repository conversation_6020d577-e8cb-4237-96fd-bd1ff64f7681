import {RegistryWidgetsType} from "@rjsf/utils";
import {CustomCheckbox} from "@/app/_components/customForms/widgets/CustomCheckbox";
import {ObjectTableBodyTemplate} from "@/app/_components/customForms/templates/ObjectTableBodyTemplate";

export const buildSchemaTableConfig = (configData: any): any => {
    const properties = {};
    const uiSchema = {};
    const formData = {};
    const widgets: RegistryWidgetsType = {
        CheckboxWidget: CustomCheckbox,
    };
    configData.forEach((item: any) => {
        if (item?.accessorKey !== "_id") {
            properties[item?.accessorKey] = {
                "type": "object",
                "title": item?.header,
                "properties": {
                    "editable": {
                        "type": "boolean",
                        "title": "Editable"
                    },
                    "show": {
                        "type": "boolean",
                        "title": "Show"
                    }
                }
            }
            uiSchema[item?.accessorKey] = {
                "ui:ObjectFieldTemplate": ObjectTableBodyTemplate,
                "editable": {"ui:widget": "checkbox", "ui:options": {label: false}},
                "show": {"ui:widget": "checkbox", "ui:options": {label: false}}
            }
            formData[item?.accessorKey] = {
                "editable": item?.config?.editable,
                "show": item?.config?.show
            }
        }
    })
    return {
        schema: {
            type: 'object',
            properties
        },
        uiSchema,
        formData,
        widgets
    };
}