import {removeStyleString} from "@/app/_components/utils/removeStyleString";

export const valueSwitchWithFormData = (activeTab: string, formData: any) => {
    let returnSwitchVal = true;
    const getKeyNameFormData = removeStyleString(activeTab)
    const getObjFormData = formData[getKeyNameFormData]
    for (const firstKey in getObjFormData) {
        if (typeof getObjFormData[firstKey] === 'boolean') {
            if (getObjFormData[firstKey] === false) {
                returnSwitchVal = false
                break;
            }
        } else {
            const secondObjFormData = getObjFormData[firstKey]
            for (const secondKey in secondObjFormData) {
                if (secondObjFormData[secondKey] === false) {
                    returnSwitchVal = false
                    break;
                }
                if (!returnSwitchVal) break;
            }
        }
    }
    return returnSwitchVal
}