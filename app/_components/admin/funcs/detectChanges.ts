export const detectChanges = (originalObj: any, newObj: any) => {
    for (const key in originalObj) {
        if (typeof originalObj[key] === 'object' && originalObj[key] !== null) {
            if (detectChanges(originalObj[key], newObj[key])) {
                return true;
            }
        } else {
            if (originalObj[key] !== newObj[key]) {
                return true;
            }
        }
    }
    return false;
}