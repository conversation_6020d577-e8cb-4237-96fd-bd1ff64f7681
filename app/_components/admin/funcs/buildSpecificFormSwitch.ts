export const buildSpecificFormSwitch = (formData: any, valueSwitch: boolean, userType: string) => {
    const updatedObj = { ...formData };

    if (userType === 'vendors' || userType === 'clients') {
        if (updatedObj.restrictions && updatedObj.restrictions[userType]) {
            Object.keys(updatedObj.restrictions[userType]).forEach(key => {
                updatedObj.restrictions[userType][key] = valueSwitch;
            });
        }
        if (updatedObj.table_keys && updatedObj.table_keys[userType]) {
            Object.keys(updatedObj.table_keys[userType]).forEach(key => {
                updatedObj.table_keys[userType][key] = valueSwitch;
            });
        }
        if (updatedObj.table_keys) {
            Object.keys(updatedObj.table_keys).forEach(table => {
                if (updatedObj.table_keys[table]) {
                    Object.keys(updatedObj.table_keys[table]).forEach(key => {
                        if (
                            (userType === 'vendors' && key.includes('vendor')) ||
                            (userType === 'clients' && key.includes('client'))
                        ) {
                            updatedObj.table_keys[table][key] = valueSwitch;
                        }
                    });
                }
            });
        }
        if (updatedObj.menus && updatedObj.menus[userType] !== undefined) {
            updatedObj.menus[userType] = valueSwitch;
        }
    }

    return updatedObj;
};
