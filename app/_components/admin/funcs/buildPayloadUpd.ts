import _ from "lodash";

export const buildPayloadUpd = (allData: any, newData: any): any => {
    let retData = allData
    const newAllConfiguration = []
    retData?.configuration.forEach((item: any) => {
        let newConfigObj = item
        if (_.has(newData, item?.accessorKey)) {
            newConfigObj.config.show = newData[item?.accessorKey].show
            newConfigObj.config.editable = newData[item?.accessorKey].editable
        }
        newAllConfiguration.push(newConfigObj)
    })
    retData.configuration = newAllConfiguration
    return {id: retData._id, data: _.omit(retData, '_id'), collection: 'tables_configuration'}
}