"use client"

import React, {useEffect, useState} from "react"
import {<PERSON>, <PERSON><PERSON>, Card, CardContent, Grid, Sheet, Switch, switchClasses, Table, Typography,} from "@mui/joy"
import {ExpandLess as ExpandLessIcon, ExpandMore as ExpandMoreIcon} from "@mui/icons-material"

interface SubMenuItem {
    id: string
    name: string
    enabled: boolean
    route: string
    tools: ToolItem[]
}

interface ToolItem {
    id: string
    name: string
    enabled: boolean
}

interface MenuItem {
    id: string
    name: string
    visible: boolean
    expanded: boolean
    tools: ToolItem[]
    subMenus: SubMenuItem[]
    route: string
}

export default function MenuPermissions({formData, onChange}: any) {
    const [menuItems, setMenuItems] = useState<MenuItem[]>([]);

    useEffect(() => {
        if (formData?.menus) {
            setMenuItems(formData?.menus);
        }
    }, []);

    useEffect(() => {
        if (menuItems.length > 0 && onChange && formData) {
            onChange({
                ...formData,
                menus: menuItems
            });
        }
    }, [menuItems, onChange, formData]);

    const handleVisibilityToggle = (menuId: string) => {
        setMenuItems(
            menuItems.map((item) => {
                if (item.id === menuId) {
                    const newVisibleState = !item.visible;
                    return {
                        ...item,
                        visible: newVisibleState,
                        tools: item.tools.map(tool => ({
                            ...tool,
                            enabled: newVisibleState ? tool.enabled : false
                        })),
                        subMenus: item.subMenus.map(subMenu => ({
                            ...subMenu,
                            enabled: newVisibleState ? subMenu.enabled : false,
                            tools: subMenu.tools.map(tool => ({
                                ...tool,
                                enabled: newVisibleState ? tool.enabled : false
                            }))
                        }))
                    };
                }
                return item;
            })
        );
    }

    const handleExpandToggle = (menuId: string) => {
        setMenuItems(
            menuItems.map((item) => {
                if (item.id === menuId) {
                    return {...item, expanded: !item.expanded}
                }
                return item
            }),
        )
    }

    const handleToolToggle = (menuId: string, toolId: string, isSubMenu: boolean = false, subMenuId?: string) => {
        setMenuItems(
            menuItems.map((item) => {
                if (item.id === menuId) {
                    if (isSubMenu && subMenuId) {
                        const updatedSubMenus = item.subMenus.map((subMenu) => {
                            if (subMenu.id === subMenuId) {
                                if (!subMenu.enabled) return subMenu;

                                const updatedTools = subMenu.tools.map((tool) => {
                                    if (tool.id === toolId) {
                                        return {...tool, enabled: !tool.enabled};
                                    }
                                    return tool;
                                });
                                return {...subMenu, tools: updatedTools};
                            }
                            return subMenu;
                        });
                        return {...item, subMenus: updatedSubMenus};
                    } else {
                        if (!item.visible) return item;

                        const updatedTools = item.tools.map((tool) => {
                            if (tool.id === toolId) {
                                return {...tool, enabled: !tool.enabled};
                            }
                            return tool;
                        });
                        return {...item, tools: updatedTools};
                    }
                }
                return item;
            })
        );
    }

    const handleSubMenuToggle = (menuId: string, subMenuId: string) => {
        setMenuItems(
            menuItems.map((item) => {
                if (item.id === menuId) {
                    const updatedSubMenus = item.subMenus.map((subMenu) => {
                        if (subMenu.id === subMenuId) {
                            const newEnabledState = !subMenu.enabled;
                            return {
                                ...subMenu,
                                enabled: newEnabledState,
                                tools: subMenu.tools.map(tool => ({
                                    ...tool,
                                    enabled: newEnabledState ? tool.enabled : false
                                }))
                            };
                        }
                        return subMenu;
                    });
                    return {...item, subMenus: updatedSubMenus};
                }
                return item;
            })
        );
    }

    const handleEnableAll = (enabled: boolean) => {
        setMenuItems(
            menuItems.map((item) => ({
                ...item,
                visible: enabled,
                tools: item.tools.map((tool) => ({...tool, enabled})),
                subMenus: item.subMenus.map((subMenu) => ({...subMenu, enabled})),
            })),
        )
    }

    const switchStyles = (theme: any) => ({
        '--Switch-thumbShadow': '0 3px 7px 0 rgba(0 0 0 / 0.12)',
        '--Switch-thumbSize': '17px',
        '--Switch-trackWidth': '45px',
        '--Switch-trackHeight': '25px',
        '--Switch-trackBackground': theme.vars.palette.background.level3,
        [`& .${switchClasses.thumb}`]: {
            transition: 'width 0.2s, left 0.2s',
        },
        '&:hover': {
            '--Switch-trackBackground': theme.vars.palette.background.level3,
        },
        '&:active': {
            '--Switch-thumbWidth': '25px',
        },
        [`&.${switchClasses.checked}`]: {
            '--Switch-trackBackground': 'rgb(48 209 88)',
            '&:hover': {
                '--Switch-trackBackground': 'rgb(48 209 88)',
            },
        },
    });

    return (
        <Box sx={{maxWidth: 1200, mx: "auto", p: 1}}>
            {menuItems && menuItems.length > 0 &&
                <Box sx={{py: 0}}>
                    <Sheet variant="outlined" sx={{borderRadius: "md", overflow: "auto"}}>
                        <Table>
                            <thead>
                            <tr>
                                <th
                                    style={{
                                        textAlign: "left",
                                        padding: "12px 16px",
                                        backgroundColor: "rgb(251 252 254)",
                                        fontWeight: 600,
                                    }}
                                >
                                    PAGES
                                </th>
                                <th
                                    style={{
                                        textAlign: "center",
                                        padding: "12px 16px",
                                        backgroundColor: "rgb(251 252 254)",
                                        fontWeight: 600,
                                    }}
                                >
                                    SHOW
                                </th>
                                <th
                                    style={{
                                        textAlign: "center",
                                        padding: "12px 16px",
                                        backgroundColor: "rgb(251 252 254)",
                                        fontWeight: 600,
                                    }}
                                >
                                    ACTIONS
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            {menuItems.map((item) => (
                                <React.Fragment key={item.id}>
                                    <tr>
                                        <td style={{padding: "16px", fontWeight: 500}}>{item.name}</td>
                                        <td style={{padding: "16px", textAlign: "center"}}>
                                            <Switch
                                                checked={item.visible}
                                                onChange={() => handleVisibilityToggle(item.id)}
                                                color={item.visible ? 'success' : 'neutral'}
                                                variant={item.visible ? 'solid' : 'outlined'}
                                                sx={switchStyles}
                                            />
                                        </td>
                                        <td style={{padding: "16px", textAlign: "center"}}>
                                            {(item?.tools.length > 0 || item?.subMenus.length > 0) ? (
                                                <Button
                                                    variant="plain"
                                                    color="neutral"
                                                    onClick={() => handleExpandToggle(item.id)}
                                                    startDecorator={item.expanded ? <ExpandLessIcon/> :
                                                        <ExpandMoreIcon/>}
                                                >
                                                    {item.expanded ? "Collapse" : "Expand"}
                                                </Button>
                                            ) : (
                                                <Typography
                                                    level="body-sm"
                                                    sx={{
                                                        color: "text.secondary",
                                                        fontStyle: "italic"
                                                    }}
                                                >
                                                    No additional settings
                                                </Typography>
                                            )}
                                        </td>
                                    </tr>
                                    {item.expanded && (
                                        <tr>
                                            <td colSpan={3} style={{padding: 0}}>
                                                <Box sx={{p: 2, bgcolor: "rgb(251 252 254)"}}>
                                                    <Grid container spacing={2}>
                                                        {item.tools.length > 0 && item?.subMenus.length === 0 && (
                                                            <Grid xs={12}>
                                                                <Card variant="outlined">
                                                                    <CardContent>
                                                                        <Typography
                                                                            level="title-md"
                                                                            sx={{
                                                                                mb: 2,
                                                                                pb: 1,
                                                                                borderBottom: "1px solid",
                                                                                borderColor: "divider"
                                                                            }}
                                                                        >
                                                                            TOOLS
                                                                        </Typography>
                                                                        <Box sx={{
                                                                            display: "flex",
                                                                            flexDirection: "column",
                                                                            gap: 2
                                                                        }}>
                                                                            {item.tools.map((tool) => (
                                                                                <Box
                                                                                    key={tool.id}
                                                                                    sx={{
                                                                                        display: "flex",
                                                                                        justifyContent: "space-between",
                                                                                        alignItems: "center",
                                                                                    }}
                                                                                >
                                                                                    <Typography>{tool.name}</Typography>
                                                                                    <Switch
                                                                                        checked={tool.enabled}
                                                                                        onChange={() => handleToolToggle(item.id, tool.id)}
                                                                                        disabled={!item.visible}
                                                                                        color={tool.enabled ? 'success' : 'neutral'}
                                                                                        variant={tool.enabled ? 'solid' : 'outlined'}
                                                                                        sx={switchStyles}
                                                                                    />
                                                                                </Box>
                                                                            ))}
                                                                        </Box>
                                                                    </CardContent>
                                                                </Card>
                                                            </Grid>
                                                        )}

                                                        {/* Submenus Section with their tools */}
                                                        {item.subMenus.length > 0 && (
                                                            <Grid xs={12}>
                                                                <Card variant="outlined">
                                                                    <CardContent>
                                                                        <Typography
                                                                            level="title-md"
                                                                            sx={{
                                                                                mb: 2,
                                                                                pb: 1,
                                                                                borderBottom: "1px solid",
                                                                                borderColor: "divider"
                                                                            }}
                                                                        >
                                                                            SUBMENUS
                                                                        </Typography>
                                                                        {item.subMenus.map((subMenu) => (
                                                                            <Card key={subMenu.id} variant="outlined"
                                                                                  sx={{mb: 2}}>
                                                                                <CardContent>
                                                                                    <Box
                                                                                        sx={{
                                                                                            display: "flex",
                                                                                            justifyContent: "space-between",
                                                                                            alignItems: "center",
                                                                                            mb: 2
                                                                                        }}
                                                                                    >
                                                                                        <Typography
                                                                                            level="title-sm">{subMenu.name}</Typography>
                                                                                        <Switch
                                                                                            checked={subMenu.enabled}
                                                                                            onChange={() => handleSubMenuToggle(item.id, subMenu.id)}
                                                                                            color={subMenu.enabled ? 'success' : 'neutral'}
                                                                                            variant={subMenu.enabled ? 'solid' : 'outlined'}
                                                                                            sx={switchStyles}
                                                                                        />
                                                                                    </Box>

                                                                                    {/* Submenu Tools Section */}
                                                                                    {subMenu.tools && subMenu.tools.length > 0 && (
                                                                                        <Box sx={{mt: 2}}>
                                                                                            <Typography
                                                                                                level="body-sm"
                                                                                                sx={{
                                                                                                    mb: 1,
                                                                                                    pb: 1,
                                                                                                    borderBottom: "1px solid",
                                                                                                    borderColor: "divider"
                                                                                                }}
                                                                                            >
                                                                                                Tools
                                                                                            </Typography>
                                                                                            <Box sx={{
                                                                                                display: "flex",
                                                                                                flexDirection: "column",
                                                                                                gap: 1
                                                                                            }}>
                                                                                                {subMenu.tools.map((tool) => (
                                                                                                    <Box
                                                                                                        key={tool.id}
                                                                                                        sx={{
                                                                                                            display: "flex",
                                                                                                            justifyContent: "space-between",
                                                                                                            alignItems: "center",
                                                                                                        }}
                                                                                                    >
                                                                                                        <Typography
                                                                                                            level="body-sm">{tool.name}</Typography>
                                                                                                        <Switch
                                                                                                            checked={tool.enabled}
                                                                                                            onChange={() => handleToolToggle(item.id, tool.id, true, subMenu.id)}
                                                                                                            disabled={!subMenu.enabled}
                                                                                                            color={tool.enabled ? 'success' : 'neutral'}
                                                                                                            variant={tool.enabled ? 'solid' : 'outlined'}
                                                                                                            size="sm"
                                                                                                            sx={switchStyles}
                                                                                                        />
                                                                                                    </Box>
                                                                                                ))}
                                                                                            </Box>
                                                                                        </Box>
                                                                                    )}
                                                                                </CardContent>
                                                                            </Card>
                                                                        ))}
                                                                    </CardContent>
                                                                </Card>
                                                            </Grid>
                                                        )}
                                                    </Grid>
                                                </Box>
                                            </td>
                                        </tr>
                                    )}
                                </React.Fragment>
                            ))}
                            </tbody>
                        </Table>
                    </Sheet>
                </Box>
            }
        </Box>
    )
}
