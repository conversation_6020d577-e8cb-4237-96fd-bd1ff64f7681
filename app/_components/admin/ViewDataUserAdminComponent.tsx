import React, {useEffect, useState} from "react";
import {Alert, Autocomplete, AutocompleteOption, Grid, IconButton, Modal} from "@mui/joy";
import Typography from "@mui/joy/Typography";
import {styledName} from "@/app/_components/utils/styledName";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import CheckOutlinedIcon from "@mui/icons-material/CheckOutlined";
import CloseIcon from "@mui/icons-material/Close";
import {FormUserAdmin} from "@/app/_components/admin/userAdmin/FormUserAdmin";
import _ from "lodash";
import {showError, showSuccess, showWarning} from "@/app/_components/alerts/toast/ToastMessages";
import {
    formPermissions,
    setFormPermissions,
    setOriginalTemplate,
    setRolSelected,
    setTabsAdminData,
    setTimezoneSelected
} from "@/app/_components/table/states/adminEditStates";
import {loading, setLoading} from "@/app/_components/Loading/loadingState";
import {useEntity} from "simpler-state";

export const ViewDataUserAdminComponent: React.FC<any> = ({data, userSelected, resetSelection}) => {
    const generalLoading = useEntity(loading)
    const valuesFormData = useEntity(formPermissions)
    const [openModal, setOpenModal] = useState(false);
    const [nameSaveChange, setNameSaveChange] = useState("");
    const [dataBuilt, setDataBuilt] = useState(null);
    const [timeZoneUser, setTimezoneUser] = useState(null);
    const [rolUser, setRolUser] = useState(null);
    const [warningPermissions, setWarningPermissions] = useState(false);
    const [timeVersion, setTimeVersion] = useState(null);
    const [allTimezones, setAllTimezones] = useState([]);
    const [allRoles, setAllRoles] = useState([]);

    useEffect(() => {
        setTabsAdminData(data?.tabs)
        setAllRoles(data?.roles)
        setAllTimezones(data?.timezones)
        setTimeVersion(data?.templateVersion)
        setRolUser(data?.userRole)
        setTimezoneSelected(data?.timezoneSelected)
        setTimezoneUser(data?.timezoneUser?.label ? data?.timezoneUser : "")
        setFormPermissions(data?.formPermissions)
        setOriginalTemplate(data?.originalTemplate)
        setWarningPermissions(data?.warningPermissions)
        setDataBuilt(data?.dataBuild)
    }, []);

    const saveOnChangeDP = async () => {
        setOpenModal(false)
        setLoading(true);
        const objToUpd = {
            data: valuesFormData,
            newRole: rolUser?.role,
            timezone: timeZoneUser?.timezone,
            userId: userSelected,
            type: "quick"
        }
        const getRespUpd = await fetch("/api/betterAuth/updateUserMetadata", {
            method: "POST",
            body: JSON.stringify(objToUpd),
        });
        if (getRespUpd.ok) {
            showSuccess("Updated successfully");
            if (resetSelection) resetSelection()
        } else {
            const {error} = await getRespUpd.json();
            showError(error || "Something wrong happened");
        }
        setNameSaveChange("")
        setLoading(false);
    };

    const handleChangeRole = (event: React.SyntheticEvent | null, roleSelect: any | null) => {
        if (_.isString(roleSelect)) {
            showWarning('Invalid role, please select a valid role')
            setRolUser(null)
            setRolSelected(null)
        } else {
            if (roleSelect?.role === 'read_only') {
                showWarning('Remember to set the clients, vendors and keys restricted to this user with read only')
            }
            setRolUser(roleSelect)
            setRolSelected(roleSelect)
            if (!_.isEmpty(roleSelect)) {
                setOpenModal(true)
                setNameSaveChange('role')
            } else {
                showWarning('Remember to choose a Role to save the permissions')
            }
        }
    };

    const handleChangeTZ = (event: React.SyntheticEvent | null, roleSelect: any | null) => {
        if (_.isString(roleSelect)) {
            showWarning('Invalid timezone, please select a valid timezone')
            setTimezoneUser(null)
            setTimezoneSelected(null)
        } else {
            setTimezoneUser(roleSelect)
            setTimezoneSelected(roleSelect?.timezone || null)
            if (!_.isEmpty(roleSelect)) {
                setOpenModal(true)
                setNameSaveChange('timezone')
            } else {
                showWarning('Remember to choose a Timezone to save the permissions.')
            }
        }
    };

    return (<>
        {_.isEmpty(data) ? <div className="flex justify-center mt-3">
                <Typography level="title-md">
                    Rol assigned
                </Typography>
            </div> :
            <Grid xs={12} md={4} lg={4}>
                <div className="flex justify-center mt-3">
                    <Typography level="title-md">
                        Rol assigned
                    </Typography>
                </div>
                <div className="flex justify-center mt-3">
                    {(allRoles && allRoles.length > 0) &&
                        <Autocomplete
                            options={allRoles.map((option: any) => {
                                return {label: styledName(option.role), ...option}
                            })}
                            value={rolUser}
                            placeholder="Roles available"
                            freeSolo
                            onChange={handleChangeRole}
                            sx={{
                                height: "32px",
                                width: "120px",
                                fontSize: '12px'
                            }}
                            renderOption={(props: any, option: any) => {
                                const {key, ...rest} = props;
                                return <AutocompleteOption key={key}  {...rest}>
                                    <Typography level="body-xs">
                                        {option?.label}
                                    </Typography>
                                </AutocompleteOption>
                            }}
                        />}
                </div>
                {warningPermissions &&
                    <div className="flex justify-center mt-3">
                        <Typography level="body-sm">
                            Permissions not same as original role
                        </Typography>
                    </div>}
                {timeVersion &&
                    <div className="flex justify-center mt-3">
                        <Typography level="body-sm">
                            Date Version Template: {timeVersion}
                        </Typography>
                    </div>}
            </Grid>}
        <Grid xs={12} md={4} lg={4}>
            <div className="flex justify-center mt-3">
                <Typography level="title-md">
                    Timezone
                </Typography>
            </div>
            <div className="flex justify-center mt-3">
                {(allTimezones && allTimezones.length > 0) &&
                    <Autocomplete
                        options={allTimezones.map((option: any) => {
                            return {label: option.name, ...option}
                        })}
                        value={timeZoneUser}
                        placeholder="Timezones available"
                        freeSolo
                        onChange={handleChangeTZ}
                        sx={{
                            height: "32px",
                            width: "150px",
                            fontSize: '12px'
                        }}
                        renderOption={(props: any, option: any) => {
                            const {key, ...rest} = props;
                            return <AutocompleteOption key={key}  {...rest}>
                                <Typography level="body-xs">
                                    {option.label}
                                </Typography>
                            </AutocompleteOption>
                        }}
                    />}
            </div>
        </Grid>
        {(!generalLoading && dataBuilt) ?
            <FormUserAdmin
                widgets={dataBuilt?.widgets}
                uiSchema={dataBuilt?.uiSchema}
                schema={dataBuilt?.schema}
                rolUser={rolUser?.role}
                resetSelection={resetSelection}/> :
            (userSelected && !generalLoading && _.isEmpty(dataBuilt)) &&
            <div className="flex justify-center"
                 style={{position: 'absolute', width: '97%', marginTop: '20px'}}>
                <Typography level="h4">
                    {`Metadata can't show, check logs and errors.`}
                </Typography>
            </div>
        }

        <Modal
            aria-labelledby="modal-title"
            aria-describedby="modal-desc"
            open={openModal}
            onClose={() => setOpenModal(false)}
            sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
            <Alert
                startDecorator={<SaveOutlinedIcon/>}
                variant="solid"
                color="neutral"
                endDecorator={
                    <React.Fragment>
                        <IconButton variant="soft" size="sm" color="neutral" sx={{mr: 1}}
                                    onClick={() => saveOnChangeDP()}>
                            <CheckOutlinedIcon/>
                        </IconButton>
                        <IconButton variant="soft" size="sm" color="neutral" onClick={() => setOpenModal(false)}>
                            <CloseIcon/>
                        </IconButton>
                    </React.Fragment>
                }>
                Would you like to save the changes you made in {nameSaveChange} ?
            </Alert>
        </Modal>
    </>)
}
