"use client"
import React, {useState} from "react";
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Tooltip} from "@mui/joy";
import CheckOutlinedIcon from "@mui/icons-material/CheckOutlined";
import ListAltOutlinedIcon from '@mui/icons-material/ListAltOutlined';
import CloseIcon from "@mui/icons-material/Close";
import ContentPasteSearchOutlinedIcon from '@mui/icons-material/ContentPasteSearchOutlined';
import Box from "@mui/joy/Box";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";

export const MatchUnknownDataComponent: React.FC<any> = ({table}) => {
    const [showModal, setShowModal] = useState(false);

    const createQueueDataBroker = async () => {
        setShowModal(false);
        const payload = {
            table
        }
        const result = await fetch("/api/dataBroker/newQueue", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });
        if (result.status === 200) {
            showSuccess('The task has been created, please wait for the process to finish');
        } else {
            showError('We had a problem creating queue');
        }
    };

    return (<>
        <Tooltip title="Match Unknown Data" variant="soft" placement="left">
            <Box>
                <ContentPasteSearchOutlinedIcon
                    style={{fontSize: '25px', cursor: 'pointer'}}
                    onClick={() => setShowModal(true)}
                />
            </Box>
        </Tooltip>

        <Modal
            aria-labelledby="modal-title"
            aria-describedby="modal-desc"
            open={showModal}
            onClose={() => setShowModal(false)}
            sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
            <Alert
                startDecorator={<ListAltOutlinedIcon/>}
                variant="solid"
                color="neutral"
                endDecorator={
                    <React.Fragment>
                        <IconButton variant="soft" size="sm" color="neutral" sx={{mr: 1}}
                                    onClick={() => createQueueDataBroker()}>
                            <CheckOutlinedIcon/>
                        </IconButton>
                        <IconButton variant="soft" size="sm" color="neutral" onClick={() => setShowModal(false)}>
                            <CloseIcon/>
                        </IconButton>
                    </React.Fragment>
                }>
                This process will match unknown data to the {table}, do you want to continue ?
            </Alert>
        </Modal>
    </>)
}