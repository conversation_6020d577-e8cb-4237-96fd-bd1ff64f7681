import React, {useEffect, useState} from "react";
import {Grid, Switch, switchClasses} from "@mui/joy";
import validator from "@rjsf/validator-ajv8";
import {withTheme} from "@rjsf/core";
import {Theme as AntDTheme} from "@rjsf/antd";
import _ from "lodash";
import {useEntity} from "simpler-state";
import {
    activeTabUserMetadata,
    formPermissions,
    originalTemplate,
    rolSelected,
    setFormPermissions,
    timezoneSelected,
    userIdForEdit
} from "@/app/_components/table/states/adminEditStates";
import {setLoading} from "@/app/_components/Loading/loadingState";
import {showError, showSuccess, showWarning} from "@/app/_components/alerts/toast/ToastMessages";
import {buildFormDataRole} from "@/app/_components/admin/funcs/buildFormDataRole";
import {buildFormDataSwitch} from "@/app/_components/admin/funcs/buildFormDataSwitch";
import {removeStyleString} from "@/app/_components/utils/removeStyleString";
import {valueSwitchWithFormData} from "@/app/_components/admin/funcs/valueSwitchWithFormData";
import {buildSpecificFormSwitch} from "@/app/_components/admin/funcs/buildSpecificFormSwitch";

const ThemedForm = withTheme(AntDTheme);

export const FormUserAdmin = ({schema, widgets, uiSchema, rolUser, resetSelection}) => {
    const [switchAll, setSwitchAll] = useState(false);
    const valuesFormData = useEntity(formPermissions)
    const originalFD = useEntity(originalTemplate)
    const activeTab = useEntity(activeTabUserMetadata)
    const userSelected = useEntity(userIdForEdit)
    const tzSelected = useEntity(timezoneSelected)
    const presetSelected = useEntity(rolSelected)

    useEffect(() => {
        if (!_.isEmpty(valuesFormData) && !_.isEmpty(presetSelected)) setPresetSelected();
    }, [presetSelected]);

    useEffect(() => {
        if (!_.isEmpty(valuesFormData) && !_.isEmpty(activeTab)) setSwitchValue(valuesFormData)
    }, [valuesFormData, activeTab]);

    const setSwitchValue = (checkFDValues: any) => {
        setSwitchAll(valueSwitchWithFormData(activeTab, checkFDValues));
    };

    const setPresetSelected = () => {
        const getFormData = _.cloneDeep(valuesFormData);
        const menus = presetSelected?.object?.menus ? presetSelected?.object?.menus : [];
        _.set(getFormData, 'menus', menus);
        const presetFD = buildFormDataRole(presetSelected, getFormData);
        setSwitchValue(presetFD);
        setFormPermissions(presetFD);
    };

    const changeSwitch = (event: any) => {
        if (!_.isEmpty(valuesFormData)) {
            setSwitchAll(event.target.checked);
            const getFormData = _.cloneDeep(valuesFormData);
            const getKeyNameFormData = removeStyleString(activeTab);
            const newFormData = buildFormDataSwitch(getFormData, getKeyNameFormData, event.target.checked);
            setFormPermissions(newFormData);
        }
    };

    const changeClients = (event: any) => {
        if (!_.isEmpty(valuesFormData)) {
            const getFormData = _.cloneDeep(valuesFormData);
            const newFormData = buildSpecificFormSwitch(getFormData, event.target.checked, "clients");
            setFormPermissions(newFormData);
        }
    };

    const onSubmit = async (submit: any) => {
        setLoading(true);
        if (_.isEmpty(rolUser) || _.isEmpty(tzSelected)) {
            showWarning('Remember to choose a Role or Timezone to save the permissions')
        } else {
            const objToUpd = {
                data: submit?.formData,
                newRole: rolUser,
                timezone: tzSelected,
                userId: userSelected,
                type: "all"
            }
            const getRespUpd = await fetch("/api/betterAuth/updateUserMetadata", {
                method: "POST",
                body: JSON.stringify(objToUpd),
            });
            if (getRespUpd.ok) {
                showSuccess("Updated successfully");
                if (resetSelection) resetSelection()
            } else {
                const {error} = await getRespUpd.json();
                showError(error || "Something wrong happened");
            }
        }
        setLoading(false);
    };

    function handleChangeFormData(formData: any) {
        setFormPermissions(formData)
    }

    return (
        <>
            <Grid xs={12}>
                <div className="flex justify-center mt-5 mb-5 space-x-3">
                    <Switch
                        checked={switchAll}
                        onChange={changeSwitch}
                        color={switchAll ? "success" : "neutral"}
                        variant={switchAll ? "solid" : "outlined"}
                        startDecorator={switchAll ? "Enable All: " : "Disable All: "}
                        sx={(theme) => ({
                            "--Switch-thumbShadow": "0 3px 7px 0 rgba(0 0 0 / 0.12)",
                            "--Switch-thumbSize": "17px",
                            "--Switch-trackWidth": "50px",
                            "--Switch-trackHeight": "27px",
                            "--Switch-trackBackground": theme.vars.palette.background.level3,
                            [`& .${switchClasses.thumb}`]: {
                                transition: "width 0.2s, left 0.2s",
                            },
                            "&:hover": {
                                "--Switch-trackBackground": theme.vars.palette.background.level3,
                            },
                            "&:active": {
                                "--Switch-thumbWidth": "25px",
                            },
                            [`&.${switchClasses.checked}`]: {
                                "--Switch-trackBackground": "rgb(48 209 88)",
                                "&:hover": {
                                    "--Switch-trackBackground": "rgb(48 209 88)",
                                },
                            },
                        })}
                    />
                </div>
            </Grid>
            <Grid xs={12}>
                <ThemedForm
                    schema={schema}
                    formData={valuesFormData}
                    validator={validator}
                    widgets={widgets}
                    onSubmit={onSubmit}
                    onChange={({formData}) => handleChangeFormData(formData)}
                    uiSchema={uiSchema}
                />
            </Grid>
        </>
    );
};
