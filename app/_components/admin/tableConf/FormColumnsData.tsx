'use client'
import React, {useEffect, useState} from "react";
import validator from "@rjsf/validator-ajv8";
import {withTheme} from "@rjsf/core";
import {Theme as AntDTheme} from "@rjsf/antd";
import {buildPayloadUpd} from "@/app/_components/admin/funcs/buildPayloadUpd";
import {buildSchemaTableConfig} from "@/app/_components/admin/funcs/buildSchemaTableConfig";
import {setLoading} from "@/app/_components/Loading/loadingState";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {HeaderFormTableAdmin} from "@/app/_components/admin/tableConf/HeaderFormTableAdmin";

const ThemedForm = withTheme(AntDTheme);

export const FormColumnsData = ({data}) => {
    const options = [
        {title: "Column", tooltip: "The column who be affected", size: 4},
        {title: "Editable", tooltip: "Editable mean if this column if the column can be editable", size: 4},
        {title: "Show", tooltip: "Show means if the column is view on the default columns", size: 4}
    ]
    const {schema, uiSchema, formData, widgets} = buildSchemaTableConfig(data?.configuration)
    const [stateDOM, setStateDOM] = useState(0);

    useEffect(() => {
    }, [stateDOM]);

    const onSaveInf = async (submitData: any) => {
        setLoading(true)
        try {
            const payload = buildPayloadUpd(data, submitData?.formData)
            const result = await fetch("/api/mongo/updateAllDataById", {
                method: 'POST',
                body: JSON.stringify(payload),
            });
            if (result.status === 200) {
                showSuccess('Updated successfully');
            } else {
                showError('We had a problem updating');
            }
            setLoading(false)
            setStateDOM(stateDOM + 1)
        } catch (error) {
            setStateDOM(stateDOM + 1)
            setLoading(false)
            showError('We had a problem updating');
            console.error('Error fetching data:', error);
        }
    }


    return (
        <div style={{width: '75vw'}}>
            <div className="min-w-full divide-y divide-gray-200">
                <HeaderFormTableAdmin options={options}/>
                <ThemedForm
                    schema={schema}
                    formData={formData}
                    validator={validator}
                    widgets={widgets}
                    onSubmit={onSaveInf}
                    uiSchema={{
                        "ui:submitButtonOptions": {
                            "submitText": "Save",
                            "norender": false,
                            "props": {
                                "type": "primary"
                            }
                        },
                        ...uiSchema,
                    }}/>
            </div>
        </div>
    )
}
