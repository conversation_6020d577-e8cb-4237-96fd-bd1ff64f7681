import {Grid, Tooltip} from "@mui/joy";
import React from "react";

export const HeaderFormTableAdmin = ({options}) => {
    // style={{position: 'sticky', top: '260px', zIndex: 10000}}
    return (
        <Grid className="bg-gray-50" container>
                {options.map((item: any, index: number) => (
                    <Grid key={index} xs={12} md={item.size} lg={item.size}>
                        <Tooltip title={item.tooltip} variant="soft"
                                 placement="bottom">
                            <div
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {item.title}
                            </div>
                        </Tooltip>
                    </Grid>
                ))}
        </Grid>
    )
}