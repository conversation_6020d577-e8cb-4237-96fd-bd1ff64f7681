import {useEntity} from "simpler-state";
import {tableNameForEdit} from "@/app/_components/table/states/adminEditStates";
import {useQuery} from "@tanstack/react-query";
import React from "react";
import Typography from "@mui/joy/Typography";
import {FormColumnsData} from "@/app/_components/admin/tableConf/FormColumnsData";
import _ from "lodash";
import {TablesConfiguration} from "@/app/_components/queries/TablesConfiguration";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";

export const EditFormAdmin = () => {
    const tableNameEnt = useEntity(tableNameForEdit)

    const {data, isFetching} = useQuery(TablesConfiguration(tableNameEnt));

    return (
        <>
            {isFetching ? <LoadingMessage message="Loading tables..."/> :
                (!_.isEmpty(data)) ?
                    <FormColumnsData data={data}/> :
                    <Typography level="title-md">
                        No columns to edit
                    </Typography>}
        </>
    )
}
