import Typography from "@mui/joy/Typography";
import {Autocomplete, AutocompleteOption, Option, Select} from "@mui/joy";
import {styledName} from "@/app/_components/utils/styledName";
import React from "react";
import {loadingGetData, setTableNameForEdit} from "@/app/_components/table/states/adminEditStates";
import {useEntity} from "simpler-state";

export const HeadAdminView = ({collectionData}) => {
    const loadingDataEnt = useEntity(loadingGetData)

    const handleChangeTabKey = (
        event: React.SyntheticEvent | null,
        tableKeySel: any | null
    ) => {
        setTableNameForEdit(tableKeySel?.table_name)
    };

    return (<>
        {(collectionData && collectionData.length > 0) ? <>
                <div className="flex justify-center mt-3">
                    <Typography level="title-md">
                        Select a table to change the configuration of its columns.
                    </Typography>
                </div>
                <div className="flex justify-center mt-3">
                    <Autocomplete
                        options={collectionData.map((option: any) => {
                            return {label: styledName(option.table_name), ...option}
                        })}
                        disabled={loadingDataEnt}
                        placeholder="Tables available"
                        freeSolo
                        onChange={handleChangeTabKey}
                        sx={{height: "32px", width: "200px"}}
                        renderOption={(props: any, option: any) => {
                            const {key, ...rest} = props;
                            return <AutocompleteOption key={key}  {...rest}>
                                <Typography level="body-md">
                                    {option.label}
                                </Typography>
                            </AutocompleteOption>
                        }}
                    />
                </div>
                <div className="flex justify-center mt-3">
                    <Typography level="title-sm">
                        Note: now you can only change whether the column is displayed in the default values and also
                        whether
                        you want to edit the data.
                    </Typography>
                </div>
            </> :
            <div className="flex justify-center"
                 style={{position: 'absolute', width: '97%', marginTop: '20px'}}>
                <Typography level="h4">
                    No configurations to show
                </Typography>
            </div>
        }
    </>)
}