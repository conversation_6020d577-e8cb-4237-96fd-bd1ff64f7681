'use client';

import React, { useState } from 'react';
import {
    <PERSON>dal,
    ModalDialog,
    DialogTitle,
    DialogContent,
    Button,
    Input,
    Stack,
    Typography
} from '@mui/joy';
import { authClient } from "@/app/_lib/auth/auth-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { showError, showSuccess } from "@/app/_components/alerts/toast/ToastMessages";

interface CreateOrganizationModalProps {
    open: boolean;
    onClose: () => void;
}

export const CreateOrganizationModal = ({ open, onClose }: CreateOrganizationModalProps) => {
    const [name, setName] = useState('');
    const [slug, setSlug] = useState('');
    const queryClient = useQueryClient();

    const createOrgMutation = useMutation({
        mutationFn: () => authClient.organization.create({ name, slug }),
        onSuccess: () => {
            showSuccess("Organization created successfully");
            queryClient.invalidateQueries({ queryKey: ['organizations'] });
            onClose();
            setName('');
            setSlug('');
        },
        onError: (error) => {
            showError("Error creating organization");
            console.error("Error creating organization:", error);
        }
    });

    const handleCreate = () => {
        if (!name.trim() || !slug.trim()) {
            showError("Please fill in all fields");
            return;
        }
        createOrgMutation.mutate();
    };

    return (
        <Modal open={open} onClose={onClose}>
            <ModalDialog>
                <DialogTitle>Create New Organization</DialogTitle>
                <DialogContent>
                    <Stack spacing={2}>
                        <Input
                            placeholder="Organization Name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                        />
                        <Input
                            placeholder="Organization Slug"
                            value={slug}
                            onChange={(e) => setSlug(e.target.value)}
                        />
                        <Typography level="body-sm" color="neutral">
                            The slug will be used in URLs and must be unique
                        </Typography>
                        <Button
                            onClick={handleCreate}
                            loading={createOrgMutation.isPending}
                        >
                            Create Organization
                        </Button>
                    </Stack>
                </DialogContent>
            </ModalDialog>
        </Modal>
    );
};