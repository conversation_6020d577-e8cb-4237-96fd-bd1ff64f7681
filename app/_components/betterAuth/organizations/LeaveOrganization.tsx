'use client';

import React, { useState } from 'react';
import { <PERSON>ton, Modal, <PERSON>dal<PERSON>og, DialogT<PERSON>le, <PERSON>alog<PERSON>ontent, Stack, Typography } from '@mui/joy';
import { authClient } from "@/app/_lib/auth/auth-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { showError, showSuccess } from "@/app/_components/alerts/toast/ToastMessages";
import ExitToAppIcon from '@mui/icons-material/ExitToApp';

interface LeaveOrganizationProps {
    organizationId: string;
    userRole?: string;
}

export const LeaveOrganization = ({ organizationId, userRole }: LeaveOrganizationProps) => {
    const [open, setOpen] = useState(false);
    const queryClient = useQueryClient();

    const leaveOrganizationMutation = useMutation({
        mutationFn: () => authClient.organization.leave({
            organizationId
        }),
        onSuccess: () => {
            showSuccess("Successfully left the organization");
            queryClient.invalidateQueries({ queryKey: ['organizations'] });
            setOpen(false);
        },
        onError: (error) => {
            showError("Failed to leave organization");
            console.error("Error leaving organization:", error);
        }
    });

    // No mostrar el botón si el usuario es owner
    if (userRole === 'owner') {
        return null;
    }

    return (
        <>
            <Button
                variant="soft"
                color="danger"
                size="sm"
                startDecorator={<ExitToAppIcon />}
                onClick={() => setOpen(true)}
            >
                Leave Organization
            </Button>

            <Modal open={open} onClose={() => setOpen(false)}>
                <ModalDialog>
                    <DialogTitle>Leave Organization</DialogTitle>
                    <DialogContent>
                        <Stack spacing={2}>
                            <Typography level="body-md">
                                Are you sure you want to leave this organization? This action cannot be undone.
                            </Typography>
                            <Stack direction="row" spacing={1} justifyContent="flex-end">
                                <Button
                                    variant="plain"
                                    color="neutral"
                                    onClick={() => setOpen(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="solid"
                                    color="danger"
                                    onClick={() => leaveOrganizationMutation.mutate()}
                                    loading={leaveOrganizationMutation.isPending}
                                >
                                    Leave
                                </Button>
                            </Stack>
                        </Stack>
                    </DialogContent>
                </ModalDialog>
            </Modal>
        </>
    );
};