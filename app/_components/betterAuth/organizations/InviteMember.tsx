import React, { useState } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON>og,
    DialogTitle,
    DialogContent,
    Button,
    Input,
    Stack,
    Typography
} from '@mui/joy';
import { authClient } from "@/app/_lib/auth/auth-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { showError, showSuccess } from "@/app/_components/alerts/toast/ToastMessages";
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import EmailIcon from '@mui/icons-material/Email';

interface InviteMemberProps {
    organizationId: string;
    open: boolean;
    onClose: () => void;
}

export const InviteMember = ({ organizationId, open, onClose }: InviteMemberProps) => {
    const [email, setEmail] = useState('');
    const queryClient = useQueryClient();

    const inviteMemberMutation = useMutation({
        mutationFn: () =>
            authClient.organization.inviteMember({
                organizationId,
                email,
                role: 'member'
            }),
        onSuccess: () => {
            showSuccess("Invitation sent successfully");
            queryClient.invalidateQueries({ queryKey: ['organization_members'] });
            queryClient.invalidateQueries({ queryKey: ['pending_invitations'] });
            onClose();
            setEmail('');
        },
        onError: (error) => {
            showError("Error sending invitation");
            console.error("Error sending invitation:", error);
        }
    });

    const onKeyUp = (event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter') {
            handleInvite();
        }
    }

    const handleInvite = () => {
        if (!email.trim() || !email.includes('@')) {
            showError("Please enter a valid email address");
            return;
        }
        inviteMemberMutation.mutate();
    };

    return (
        <Modal open={open} onClose={onClose}>
            <ModalDialog>
                <DialogTitle>Invite Member</DialogTitle>
                <DialogContent>
                    <Stack spacing={2}>
                        <Input
                            placeholder="Enter email address"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            onKeyUp={onKeyUp}
                            startDecorator={<EmailIcon />}
                            error={inviteMemberMutation.isError}
                        />
                        <Typography level="body-sm" color="neutral">
                            An invitation will be sent to this email address
                        </Typography>
                        <Button
                            variant="solid"
                            color="neutral"
                            onClick={handleInvite}
                            loading={inviteMemberMutation.isPending}
                            startDecorator={<PersonAddIcon />}
                        >
                            Send Invitation
                        </Button>
                    </Stack>
                </DialogContent>
            </ModalDialog>
        </Modal>
    );
};
