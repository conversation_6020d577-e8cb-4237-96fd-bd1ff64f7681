'use client';

import React, {useState} from 'react';
import {
    <PERSON>ge,
    Box,
    Button,
    DialogContent,
    DialogTitle,
    IconButton,
    List,
    ListItem,
    Modal,
    ModalDialog,
    Stack,
    Typography
} from '@mui/joy';
import {useMutation, useQuery, useQueryClient} from "@tanstack/react-query";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import MailIcon from '@mui/icons-material/Mail';
import CancelIcon from '@mui/icons-material/Cancel';
import {PendingInvitationsQuery as PendingInvitationsQuery} from '@/app/_components/queries/PendingInvitationsQuery';
import {authClient} from "@/app/_lib/auth/auth-client";

interface PendingInvitationsProps {
    organizationId: string;
    userRole: string;
}

export const PendingInvitations = ({organizationId, userRole}: PendingInvitationsProps) => {
    const [open, setOpen] = useState(false);
    const queryClient = useQueryClient();

    const {data: pendingInvitations = [], isFetching} = useQuery(
        PendingInvitationsQuery(organizationId)
    );

    const cancelInvitationMutation = useMutation({
        mutationFn: async (invitationId: string) => {
            return await authClient.organization.cancelInvitation({
                invitationId
            });
        },
        onSuccess: () => {
            showSuccess("Invitation cancelled successfully");
            queryClient.invalidateQueries({queryKey: ['pending_invitations']});
        },
        onError: (error) => {
            showError("Error cancelling invitation");
            console.error("Error cancelling invitation:", error);
        }
    });

    if (userRole !== 'owner' || pendingInvitations.length === 0) {
        return null;
    }

    return (
        <>
            <Badge badgeContent={pendingInvitations.length} color="neutral">
                <IconButton
                    variant="outlined"
                    color="neutral"
                    onClick={() => setOpen(true)}>
                    <MailIcon/>
                </IconButton>
            </Badge>

            <Modal open={open} onClose={() => setOpen(false)}>
                <ModalDialog>
                    <DialogTitle>Pending Invitations</DialogTitle>
                    <DialogContent>
                        <List>
                            {pendingInvitations.map((invitation: any) => (
                                <ListItem key={invitation._id}>
                                    <Stack
                                        direction="row"
                                        spacing={2}
                                        width="100%"
                                        alignItems="center"
                                    >
                                        <Box sx={{flexGrow: 1}}>
                                            <Typography level="body-md">
                                                {invitation.email}
                                            </Typography>
                                            <Typography
                                                level="body-sm"
                                                color="neutral"
                                            >
                                                Expires: {new Date(invitation.expiresAt).toLocaleDateString()}
                                            </Typography>
                                        </Box>
                                        <Button
                                            variant="soft"
                                            color="danger"
                                            size="sm"
                                            startDecorator={<CancelIcon/>}
                                            onClick={() => cancelInvitationMutation.mutate(invitation._id)}
                                            loading={cancelInvitationMutation.isPending}
                                        >
                                            Cancel
                                        </Button>
                                    </Stack>
                                </ListItem>
                            ))}
                        </List>
                    </DialogContent>
                </ModalDialog>
            </Modal>
        </>
    );
};
