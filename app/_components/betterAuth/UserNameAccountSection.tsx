import React, { useState } from "react";
import { <PERSON>, <PERSON>ton, Card, Input, Typography } from "@mui/joy";
import { authClient } from "@/app/_lib/auth/auth-client";
import { showError, showSuccess } from "@/app/_components/alerts/toast/ToastMessages";
import SaveIcon from '@mui/icons-material/Save';
import BadgeOutlinedIcon from '@mui/icons-material/BadgeOutlined';
import { useMutation } from "@tanstack/react-query";

export const UserNameSection = ({ user }) => {
    const [newUsername, setNewUsername] = useState(user?.name || "");

    const updateUsernameMutation = useMutation({
        mutationFn: (name: string) => authClient.updateUser({ name }),
        onSuccess: () => {
            showSuccess("Name updated successfully");
        },
        onError: (error) => {
            showError("Error updating name");
            console.error("Error updating name:", error);
        }
    });

    const handleChangeUsername = () => {
        updateUsernameMutation.mutate(newUsername);
    };

    const isUsernameValid = newUsername.trim().length <= 32 && newUsername.trim() !== user?.name;

    return (
        <Card variant="outlined" sx={{ p: 2, background: "white", borderColor: "neutral.outlinedBorder" }}>
            <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <BadgeOutlinedIcon />
                <Typography level="title-md">Display Name</Typography>
            </Box>

            <Typography level="body-sm" sx={{ mb: 2 }}>
                Please enter your full name, or a display name you are comfortable with.
            </Typography>

            <Input
                placeholder="Display name"
                fullWidth
                value={newUsername}
                onChange={(e) => setNewUsername(e.target.value)}
                error={newUsername.trim().length > 32}
                sx={{ mb: 1 }}
            />

            <Typography
                level="body-xs"
                color={newUsername.trim().length > 32 ? "danger" : "neutral"}
                sx={{ mb: 2 }}
            >
                Please use 32 characters at maximum.
            </Typography>

            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 1 }}>
                <Button
                    onClick={handleChangeUsername}
                    variant="outlined"
                    color="neutral"
                    disabled={!isUsernameValid || updateUsernameMutation.isPending}
                    startDecorator={<SaveIcon />}
                >
                    Save Changes
                </Button>
            </Box>
        </Card>
    );
};
