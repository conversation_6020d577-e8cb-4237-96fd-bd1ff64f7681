import React, {useState} from "react";
import {<PERSON>, <PERSON><PERSON>, Card, DialogActions, DialogContent, DialogTitle, Modal, ModalDialog, Typography} from "@mui/joy";
import {authClient} from "@/app/_lib/auth/auth-client";
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import WarningIcon from '@mui/icons-material/Warning';
import {useMutation} from "@tanstack/react-query";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {tryit} from "radash";

export const DeleteAccountSection = () => {
    const [open, setOpen] = useState(false);

    const deleteAccountMutation = useMutation({
        mutationFn: async () => {
            const [cookieErr] = await tryit(fetch)(
                '/api/organization/removeCookie',
                {
                    method: 'GET',
                }
            );
            if (cookieErr) {
                console.error('Error clearing org cookie before deleting account:', cookieErr);
            }

            const [deleteErr] = await tryit(authClient.deleteUser)();
            if (deleteErr) throw deleteErr;
        },
        onSuccess: () => {
            showSuccess("Account deleted successfully");
            window.location.reload();
        },
        onError: (error) => {
            showError("Error deleting account");
            console.error("Error deleting account:", error);
            setOpen(false);
        }
    });

    const handleDelete = () => {
        deleteAccountMutation.mutate();
    };

    return (
        <Card variant="outlined" sx={{p: 2, background: "white", borderColor: "danger.outlinedBorder"}}>
            <Box sx={{mb: 2, display: 'flex', alignItems: 'center', gap: 1}}>
                <DeleteForeverIcon/>
                <Typography level="title-md">Delete Account</Typography>
            </Box>

            <Typography level="body-sm" sx={{mb: 2}}>
                Permanently remove your account and all of its contents. This action is not reversible, so please
                continue with caution.
            </Typography>

            <Box sx={{display: "flex", justifyContent: "flex-end", gap: 1}}>
                <Button
                    color="danger"
                    variant="outlined"
                    onClick={() => setOpen(true)}
                    startDecorator={<DeleteForeverIcon/>}
                    disabled={deleteAccountMutation.isPending}
                >
                    Delete Account
                </Button>
            </Box>

            <Modal open={open} onClose={() => !deleteAccountMutation.isPending && setOpen(false)}>
                <ModalDialog variant="outlined" role="alertdialog">
                    <DialogTitle sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                        <WarningIcon/>
                        Confirm Account Deletion
                    </DialogTitle>
                    <DialogContent>
                        <Typography>
                            Are you sure you want to delete your account? This action cannot be undone and will result
                            in the permanent loss of all your data.
                        </Typography>
                    </DialogContent>
                    <DialogActions>
                        <Button
                            variant="solid"
                            color="danger"
                            onClick={handleDelete}
                            loading={deleteAccountMutation.isPending}
                            startDecorator={<DeleteForeverIcon/>}>
                            Delete Account
                        </Button>
                        <Button
                            variant="outlined"
                            color="neutral"
                            onClick={() => setOpen(false)}
                            disabled={deleteAccountMutation.isPending}>
                            Cancel
                        </Button>
                    </DialogActions>
                </ModalDialog>
            </Modal>
        </Card>
    );
};
