"use client";

import React, { useState } from "react";
import { authClient } from "@/app/_lib/auth/auth-client";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  showError,
  showSuccess,
} from "@/app/_components/alerts/toast/ToastMessages";

export const PasskeySection = () => {
  const [isRegistering, setIsRegistering] = useState(false);

  const {
    data: passkeys,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ["passkeys"],
    queryFn: async () => {
      const { data, error } = await authClient.passkey.listUserPasskeys();
      if (error) {
        console.error("Error fetching passkeys:", error);
        return [];
      }
      return data || [];
    },
  });

  const registerPasskeyMutation = useMutation({
    mutationFn: async () => {
      setIsRegistering(true);
      const result = await authClient.passkey.addPasskey();
      if (result?.error) {
        setIsRegistering(false);
        console.error("Passkey sign in error:", result?.error);
        throw new Error(result?.error?.message || "Error registering passkey");
      }
    },
    onSuccess: () => {
      showSuccess("Passkey registered successfully");
      refetch();
    },
    onError: (error) => {
      showError("Failed to register passkey");
      console.error("Failed to register passkey", error);
    },
    onSettled: () => {
      setIsRegistering(false);
    },
  });

  const deletePasskeyMutation = useMutation({
    mutationFn: (passkeyId: string) =>
      authClient.passkey.deletePasskey({ id: passkeyId }),
    onSuccess: () => {
      showSuccess("Passkey deleted successfully");
      refetch();
    },
    onError: (error) => {
      showError("Failed to delete passkey");
      console.error("Failed to delete passkey", error);
    },
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="user-settings-section">
      <h2 className="user-settings-section-title">
        🔑 Passkeys ({passkeys?.length || 0})
      </h2>

      {isFetching ? (
        <div className="user-settings-loading">Loading passkeys...</div>
      ) : (
        <div className="user-settings-stack">
          {passkeys?.map((passkey) => (
            <div key={passkey.id} className="user-settings-passkey-item">
              <div className="user-settings-passkey-info">
                <p>
                  <strong>Name:</strong>{" "}
                  {passkey.name || `Passkey ${passkey.id.slice(0, 8)}`}
                </p>
                <p>
                  <strong>Created:</strong> {formatDate(passkey.createdAt)}
                </p>
                {passkey.deviceType && (
                  <p>
                    <strong>Device:</strong> {passkey.deviceType}
                  </p>
                )}
              </div>
              <button
                className="user-settings-button user-settings-button-danger"
                onClick={() => deletePasskeyMutation.mutate(passkey.id)}
                disabled={deletePasskeyMutation.isPending}
              >
                {deletePasskeyMutation.isPending ? "Deleting..." : "Delete"}
              </button>
            </div>
          ))}

          <button
            className="user-settings-button user-settings-button-secondary user-settings-button-full"
            onClick={() => registerPasskeyMutation.mutate()}
            disabled={isRegistering || registerPasskeyMutation.isPending}
          >
            {isRegistering ? "Registering..." : "Add New Passkey"}
          </button>
        </div>
      )}
    </div>
  );
};
