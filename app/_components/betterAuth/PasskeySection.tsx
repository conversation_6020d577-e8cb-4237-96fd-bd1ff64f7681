"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Typography,
  List,
  ListItem,
  Divider,
} from "@mui/joy";
import { authClient } from "@/app/_lib/auth/auth-client";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  showError,
  showSuccess,
} from "@/app/_components/alerts/toast/ToastMessages";
import KeyIcon from "@mui/icons-material/Key";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";

export const PasskeySection = () => {
  const [isRegistering, setIsRegistering] = useState(false);

  const {
    data: passkeys,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ["passkeys"],
    queryFn: async () => {
      const { data, error } = await authClient.passkey.listUserPasskeys();
      if (error) {
        console.error("Error fetching passkeys:", error);
        return [];
      }
      return data || [];
    },
  });

  const registerPasskeyMutation = useMutation({
    mutationFn: async () => {
      setIsRegistering(true);
      const result = await authClient.passkey.addPasskey();
      if (result?.error) {
        setIsRegistering(false);
        console.error("Passkey sign in error:", result?.error);
        throw new Error(result?.error?.message || "Error registering passkey");
      }
    },
    onSuccess: () => {
      showSuccess("Passkey registrado exitosamente");
      refetch();
    },
    onError: (error) => {
      showError("Error al registrar passkey");
      console.error("Failed to register passkey", error);
    },
    onSettled: () => {
      setIsRegistering(false);
    },
  });

  const deletePasskeyMutation = useMutation({
    mutationFn: (passkeyId: string) =>
      authClient.passkey.deletePasskey({ id: passkeyId }),
    onSuccess: () => {
      showSuccess("Passkey eliminado exitosamente");
      refetch();
    },
    onError: (error) => {
      showError("Error al eliminar passkey");
      console.error("Failed to delete passkey", error);
    },
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("es-ES", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <Card
      variant="outlined"
      sx={{ p: 2, background: "white", borderColor: "neutral.outlinedBorder" }}
    >
      <Box sx={{ mb: 2, display: "flex", alignItems: "center", gap: 1 }}>
        <KeyIcon />
        <Typography level="title-md">
          Passkeys ({passkeys?.length || 0})
        </Typography>
      </Box>

      {isFetching ? (
        <Typography level="body-sm" color="neutral">
          Cargando passkeys...
        </Typography>
      ) : (
        <Box>
          {passkeys && passkeys.length > 0 ? (
            <List>
              {passkeys.map((passkey, index) => (
                <React.Fragment key={passkey.id}>
                  <ListItem>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography level="body-md" fontWeight="bold">
                        {passkey.name || `Passkey ${passkey.id.slice(0, 8)}`}
                      </Typography>
                      <Typography level="body-sm" color="neutral">
                        Creado: {formatDate(passkey.createdAt)}
                      </Typography>
                      {passkey.deviceType && (
                        <Typography level="body-sm" color="neutral">
                          Dispositivo: {passkey.deviceType}
                        </Typography>
                      )}
                    </Box>
                    <Button
                      variant="soft"
                      color="danger"
                      size="sm"
                      startDecorator={<DeleteIcon />}
                      onClick={() => deletePasskeyMutation.mutate(passkey.id)}
                      disabled={deletePasskeyMutation.isPending}
                    >
                      {deletePasskeyMutation.isPending
                        ? "Eliminando..."
                        : "Eliminar"}
                    </Button>
                  </ListItem>
                  {index < passkeys.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          ) : (
            <Typography level="body-sm" color="neutral" sx={{ mb: 2 }}>
              No tienes passkeys configurados.
            </Typography>
          )}

          <Button
            variant="outlined"
            color="neutral"
            fullWidth
            startDecorator={<AddIcon />}
            onClick={() => registerPasskeyMutation.mutate()}
            disabled={isRegistering || registerPasskeyMutation.isPending}
          >
            {isRegistering ? "Registrando..." : "Agregar Nuevo Passkey"}
          </Button>
        </Box>
      )}
    </Card>
  );
};
