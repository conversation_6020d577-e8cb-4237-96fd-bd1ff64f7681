import React, {Fragment} from "react";
import {<PERSON>, <PERSON><PERSON>, Card, Divider, List, ListItem, Stack, Typography} from "@mui/joy";
import {authClient} from "@/app/_lib/auth/auth-client";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import DevicesIcon from '@mui/icons-material/Devices';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import {DateTime} from "luxon";
import * as UAParser from 'ua-parser-js';
import {useMutation, useQuery, useQueryClient} from "@tanstack/react-query";
import {Sessions} from "@/app/_components/queries/Sessions";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";
import _ from "lodash";

export const ActiveSessionsSection = () => {
    const queryClient = useQueryClient();

    const {data: sessions = [], isFetching} = useQuery(Sessions());

    const revokeSessionMutation = useMutation({
        mutationFn: (token: string) => authClient.revokeSession({token}),
        onSuccess: () => {
            showSuccess("Session revoked successfully");
            queryClient.invalidateQueries({queryKey: ['get_sessions']});
        },
        onError: (error) => {
            showError("Error revoking session");
            console.error("Error revoking session:", error);
        }
    });

    const revokeAllSessionsMutation = useMutation({
        mutationFn: () => authClient.revokeOtherSessions(),
        onSuccess: () => {
            showSuccess("All sessions revoked successfully");
            queryClient.invalidateQueries({queryKey: ['get_sessions']});
        },
        onError: (error) => {
            showError("Error revoking all sessions");
            console.error("Error revoking all sessions:", error);
        }
    });

    const formatDate = (dateString: Date) => {
        return DateTime.fromJSDate(dateString)
            .setZone("America/Chicago")
            .toFormat("MM/dd/yyyy hh:mm a ZZZZ");
    };

    const getBrowserInfo = (userAgent: string) => {
        const parser = new UAParser.UAParser(userAgent);
        const browser = parser.getBrowser();
        const os = parser.getOS();
        return `${browser.name} on ${os.name}`;
    };

    const currentSessionId = _.last(sessions)?.id;

    return (<>

        <Card variant="outlined" sx={{p: 2, background: "white"}}>
            <Box sx={{mb: 2, display: 'flex', alignItems: 'center', gap: 1}}>
                <DevicesIcon/>
                <Typography level="title-md">Active Sessions</Typography>
            </Box>

            {(isFetching) ? <LoadingMessage message="Getting sessions..."/> :
                <List>
                    {sessions.map((session) => (
                        <Fragment key={session.id}>
                            <ListItem>
                                <Stack direction="row" spacing={2} width="100%" alignItems="center">
                                    <Box sx={{flexGrow: 1}}>
                                        <Typography level="body-md">
                                            {getBrowserInfo(session.userAgent)}
                                            {session.id === currentSessionId && " (Current)"}
                                        </Typography>
                                        <Typography level="body-sm" color="neutral">
                                            Created: {formatDate(session.createdAt)}
                                        </Typography>
                                        <Typography level="body-sm" color="neutral">
                                            Expires: {formatDate(session.expiresAt)}
                                        </Typography>
                                    </Box>
                                    {session.id !== currentSessionId && (
                                        <Button
                                            variant="soft"
                                            color="danger"
                                            startDecorator={<HighlightOffIcon/>}
                                            onClick={() => revokeSessionMutation.mutate(session.token)}
                                            disabled={revokeSessionMutation.isPending || revokeAllSessionsMutation.isPending}
                                        >
                                            Revoke
                                        </Button>
                                    )}
                                </Stack>
                            </ListItem>
                            <Divider/>
                        </Fragment>
                    ))}
                </List>}

            {sessions.length > 1 && (
                <Box sx={{mt: 2}}>
                    <Button
                        variant="outlined"
                        color="danger"
                        onClick={() => revokeAllSessionsMutation.mutate()}
                        disabled={revokeSessionMutation.isPending || revokeAllSessionsMutation.isPending}
                        fullWidth
                    >
                        Revoke All Other Sessions
                    </Button>
                </Box>
            )}
        </Card>
    </>);
};
