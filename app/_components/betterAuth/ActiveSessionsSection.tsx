import React, { Fragment } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Divider,
  List,
  ListItem,
  Stack,
  Typography,
} from "@mui/joy";
import { authClient } from "@/app/_lib/auth/auth-client";
import {
  showError,
  showSuccess,
} from "@/app/_components/alerts/toast/ToastMessages";
import DevicesIcon from "@mui/icons-material/Devices";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import { DateTime } from "luxon";
import * as UAParser from "ua-parser-js";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Sessions } from "@/app/_components/queries/Sessions";
import { LoadingMessage } from "@/app/_components/Loading/LoadingMessage";
import _ from "lodash";

export const ActiveSessionsSection = () => {
  const queryClient = useQueryClient();

  const { data: sessions = [], isFetching } = useQuery(Sessions());

  const revokeSessionMutation = useMutation({
    mutationFn: (token: string) => authClient.revokeSession({ token }),
    onSuccess: () => {
      showSuccess("Sesión revocada exitosamente");
      queryClient.invalidateQueries({ queryKey: ["get_sessions"] });
    },
    onError: (error) => {
      showError("Error al revocar sesión");
      console.error("Error revoking session:", error);
    },
  });

  const revokeAllSessionsMutation = useMutation({
    mutationFn: () => authClient.revokeOtherSessions(),
    onSuccess: () => {
      showSuccess("Todas las sesiones revocadas exitosamente");
      queryClient.invalidateQueries({ queryKey: ["get_sessions"] });
    },
    onError: (error) => {
      showError("Error al revocar todas las sesiones");
      console.error("Error revoking all sessions:", error);
    },
  });

  const formatDate = (dateString: Date) => {
    return DateTime.fromJSDate(dateString)
      .setZone("America/Chicago")
      .toFormat("MM/dd/yyyy hh:mm a ZZZZ");
  };

  const getBrowserInfo = (userAgent: string) => {
    const parser = new UAParser.UAParser(userAgent);
    const browser = parser.getBrowser();
    const os = parser.getOS();
    return `${browser.name} on ${os.name}`;
  };

  const currentSessionId = _.last(sessions)?.id;

  return (
    <>
      <Card variant="outlined" sx={{ p: 2, background: "white" }}>
        <Box sx={{ mb: 2, display: "flex", alignItems: "center", gap: 1 }}>
          <DevicesIcon />
          <Typography level="title-md">Sesiones Activas</Typography>
        </Box>

        {isFetching ? (
          <LoadingMessage message="Obteniendo sesiones..." />
        ) : (
          <List>
            {sessions.map((session) => (
              <Fragment key={session.id}>
                <ListItem>
                  <Stack
                    direction="row"
                    spacing={2}
                    width="100%"
                    alignItems="center"
                  >
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography level="body-md">
                        {getBrowserInfo(session.userAgent)}
                        {session.id === currentSessionId && " (Current)"}
                      </Typography>
                      <Typography level="body-sm" color="neutral">
                        Creado: {formatDate(session.createdAt)}
                      </Typography>
                      <Typography level="body-sm" color="neutral">
                        Expira: {formatDate(session.expiresAt)}
                      </Typography>
                    </Box>
                    {session.id !== currentSessionId && (
                      <Button
                        variant="soft"
                        color="danger"
                        startDecorator={<HighlightOffIcon />}
                        onClick={() =>
                          revokeSessionMutation.mutate(session.token)
                        }
                        disabled={
                          revokeSessionMutation.isPending ||
                          revokeAllSessionsMutation.isPending
                        }
                      >
                        Revocar
                      </Button>
                    )}
                  </Stack>
                </ListItem>
                <Divider />
              </Fragment>
            ))}
          </List>
        )}

        {sessions.length > 1 && (
          <Box sx={{ mt: 2 }}>
            <Button
              variant="outlined"
              color="danger"
              onClick={() => revokeAllSessionsMutation.mutate()}
              disabled={
                revokeSessionMutation.isPending ||
                revokeAllSessionsMutation.isPending
              }
              fullWidth
            >
              Revocar Todas las Otras Sesiones
            </Button>
          </Box>
        )}
      </Card>
    </>
  );
};
