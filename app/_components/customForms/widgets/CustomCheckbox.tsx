import {Switch, switchClasses} from "@mui/joy";
import React from "react";
import {WidgetProps} from "@rjsf/utils";

export const CustomCheckbox = (props: WidgetProps) => {
    return (
        <Switch
            checked={props.value}
            onChange={() => props.onChange(!props.value)}
            color={props.value ? 'success' : 'neutral'}
            variant={props.value ? 'solid' : 'outlined'}
            endDecorator={props.value ? 'Yes' : 'No'}
            sx={(theme) => ({
                "--Switch-thumbShadow": "0 3px 7px 0 rgba(0 0 0 / 0.12)",
                "--Switch-thumbSize": "17px",
                "--Switch-trackWidth": "50px",
                "--Switch-trackHeight": "27px",
                "--Switch-trackBackground": theme.vars.palette.background.level3,
                [`& .${switchClasses.thumb}`]: {
                    transition: "width 0.2s, left 0.2s",
                },
                "&:hover": {
                    "--Switch-trackBackground": theme.vars.palette.background.level3,
                },
                "&:active": {
                    "--Switch-thumbWidth": "15px",
                },
                [`&.${switchClasses.checked}`]: {
                    "--Switch-trackBackground": "rgb(48 209 88)",
                    "&:hover": {
                        "--Switch-trackBackground": "rgb(48 209 88)",
                    },
                },
            })}
        />
    );
};