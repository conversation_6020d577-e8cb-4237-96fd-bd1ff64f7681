import React from "react";
import {WidgetProps} from "@rjsf/utils";
import {Autocomplete, AutocompleteOption} from "@mui/joy";
import _ from "lodash";
import Typography from "@mui/joy/Typography";

export const CustomSelectWidget = (props: WidgetProps) => {
    const {options, value, onChange, id, schema} = props;

    const handleSelectChange = (event: React.SyntheticEvent | null, valuesSel: any | null) => {
        onChange(valuesSel)
    };

    const handleInputChange = (newInputValue: any) => {
        const getValue = newInputValue.target.value
        if (!_.includes(schema.enum, getValue) && !_.isEmpty(getValue)) {
            schema.enum.push(getValue)
        }
        onChange(getValue)
    };

    return (
        <Autocomplete
            placeholder="Select an option"
            id={id}
            options={options.enumOptions.map((option) => option.label)}
            value={value || ""}
            freeSolo
            onBlur={handleInputChange}
            onChange={handleSelectChange}
            sx={{height: "32px", fontSize: '12px', backgroundColor: 'white'}}
            renderOption={(props, option) => {
                // @ts-ignore
                const {key, ...rest} = props;
                return <AutocompleteOption key={key}  {...rest}>
                    <Typography level="body-xs">
                        {option}
                    </Typography>
                </AutocompleteOption>
            }}
        />
    );
};
