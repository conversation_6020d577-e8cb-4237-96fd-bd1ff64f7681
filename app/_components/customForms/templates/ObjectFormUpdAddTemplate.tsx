import React from "react";
import {Grid} from "@mui/joy";

export const ObjectFormUpdAddTemplate = (props: any): any => {
    return (
        <div>
            {props.title}
            {props.description}
            <Grid container spacing={2} sx={{maxWidth: '1800px'}}>
                {props.properties.map((element: any, index: any) => {
                        const {required} = element.content.props
                        return (required) ?
                            <Grid key={`father-${index}`} xs={12} md={12} lg={12}>
                                {element.content}
                            </Grid> : null
                    }
                )}
            </Grid>
        </div>
    );
};