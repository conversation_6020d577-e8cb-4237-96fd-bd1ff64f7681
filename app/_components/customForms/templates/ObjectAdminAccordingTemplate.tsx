import React from "react";
import {Accordion, AccordionDetails, AccordionGroup, AccordionSummary} from "@mui/joy";
import {styledName} from "@/app/_components/utils/styledName";

export const ObjectAdminAccordingTemplate = (props: any): any => {
    return (
        <>
            <AccordionGroup transition="0.2s ease" sx={{maxWidth: "90vw"}} size="lg">
                {props.properties.map((element: any, index: any) =>
                    <Accordion key={`tab-cont-${index}`}>
                        <AccordionSummary>{styledName(element.name)}</AccordionSummary>
                        <AccordionDetails>
                            {element.content}
                        </AccordionDetails>
                    </Accordion>
                )}
            </AccordionGroup>
        </>
    );
};