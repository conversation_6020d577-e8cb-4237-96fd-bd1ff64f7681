import {Autocomplete, AutocompleteOption, Typography} from "@mui/joy";
import React, {useState} from "react";

export const AutoCompleteAddNodes = ({orphanNodes, onAddOption, onSelectOrphan}) => {
    const [inputValue, setInputValue] = useState('');

    const handleBlur = () => {
        if (
            inputValue &&
            orphanNodes.findIndex((node: any) => node.objective === inputValue) === -1
        ) {
            onAddOption(inputValue);
        }
    };

    const handleInputChange = (event: any, newInputValue: string) => {
        setInputValue(newInputValue);
    };

    const handleChangeSelect = (event: any, newValue: any) => {
        if (newValue) onSelectOrphan(event, newValue)
    };

    return (
        <Autocomplete
            options={orphanNodes.map((option: any) => ({
                label: option?.objective,
                ...option,
            }))}
            placeholder="Search or select a node"
            freeSolo
            onChange={handleChangeSelect}
            onInputChange={handleInputChange}
            onBlur={handleBlur}
            sx={{height: "32px", maxWidth: "700px", fontSize: '12px', backgroundColor: 'white'}}
            renderOption={(props: any, option: any) => {
                const {key, ...rest} = props;
                return (
                    <AutocompleteOption key={key} {...rest}>
                        <Typography level="body-xs">
                            {option.label ?? option?.objective}
                        </Typography>
                    </AutocompleteOption>
                );
            }}
        />
    )
}