import React, {useEffect, useState} from "react";
import {Option, Select, Tab, Tab<PERSON>ist, Tab<PERSON>anel, Tabs} from "@mui/joy";
import {
    setActiveTabUserMetadata,
    setFormPermissions,
    tabsAdminData
} from "@/app/_components/table/states/adminEditStates";
import {useEntity} from "simpler-state";
import {styledName} from "@/app/_components/utils/styledName";
import MenuPermissions from "@/app/_components/admin/permissions/menu-permissions";

export const ObjectTabsAdminTemplate = (props: any): any => {
    const userPermissions = ['All Permissions', 'Vendor', 'Client'];
    const tabsAvailable = useEntity(tabsAdminData);

    const getInitialSelectedPermission = () => {
        if (props.formData && props.formData.table_keys) {
            const tableKeys = props.formData.table_keys;

            const allVendorsActive = tableKeys.vendors && Object.values(tableKeys.vendors).every(value => value === true);
            const allVendorsInactive = tableKeys.vendors && Object.values(tableKeys.vendors).every(value => value === false);
            const allClientsActive = tableKeys.clients && Object.values(tableKeys.clients).every(value => value === true);
            const allClientsInactive = tableKeys.clients && Object.values(tableKeys.clients).every(value => value === false);
            const anyVendorsActive = tableKeys.vendors && Object.values(tableKeys.vendors).some(value => value === true);
            const anyClientsActive = tableKeys.clients && Object.values(tableKeys.clients).some(value => value === true);

            const restrictions = props.formData.restrictions;

            const allVendorsActiveRes = restrictions.vendors && Object.values(restrictions.vendors).every(value => value === true);
            const allClientsActiveRes = restrictions.clients && Object.values(restrictions.clients).every(value => value === true);

            if (allVendorsActiveRes && allClientsActiveRes) {
                return 'All Permissions';
            } else {
                if (allVendorsActive && allClientsInactive || anyVendorsActive && allClientsInactive) return 'Vendor';
                if (allClientsActive && allVendorsInactive || anyClientsActive && allVendorsInactive) return 'Client';
            }
        }
        return 'All Permissions';
    };

    const [selectedPermission, setSelectedPermission] = useState<string>(getInitialSelectedPermission());
    const [activeTab, setActiveTab] = useState<number>(0);
    const [updateKey, setUpdateKey] = useState<number>(0);
    const [initialLoad, setInitialLoad] = useState<boolean>(true);

    useEffect(() => {
        if (initialLoad) {
            const initialSelected = getInitialSelectedPermission();
            setSelectedPermission(initialSelected);
            if (initialSelected === 'All Permissions') {
            } else {
                updateFormDataBasedOnSelection(initialSelected, true);
            }
            setInitialLoad(false);
        } else {
            if (selectedPermission === 'All Permissions') {
                updateFormDataBasedOnSelection(selectedPermission);
            } else {
                updateFormDataBasedOnSelection(selectedPermission);
            }
        }
        setUpdateKey(prevKey => prevKey + 1);
    }, [selectedPermission]);

    const changeTab = (event: any, newValue: number) => {
        setActiveTab(newValue);
        setActiveTabUserMetadata(event.target.innerText || "");
    };

    const handleSelectChange = (event: React.SyntheticEvent, value: string) => {
        setSelectedPermission(value);
    };

    const buildSpecificFormSwitch = (formData: any, valueSwitch: boolean, userType: string) => {
        const updatedObj = {...formData};

        if (updatedObj.restrictions && updatedObj.restrictions[userType]) {
            Object.keys(updatedObj.restrictions[userType]).forEach(key => {
                updatedObj.restrictions[userType][key] = valueSwitch;
            });
        }

        if (updatedObj.table_keys && updatedObj.table_keys[userType]) {
            Object.keys(updatedObj.table_keys[userType]).forEach(key => {
                updatedObj.table_keys[userType][key] = valueSwitch;
            });
        }
        return updatedObj;
    };

    const updateTableKeys = (formData: any, selection: string) => {
        const updatedObj = {...formData};
        const filterTables = ['leads', 'accounting', 'transfers', 'postbacks', 'rvn_exp'];

        if (updatedObj.table_keys) {
            Object.keys(updatedObj.table_keys).forEach(table => {
                if (filterTables.includes(table)) {
                    if (updatedObj.table_keys[table]) {
                        Object.keys(updatedObj.table_keys[table]).forEach(key => {
                            if (
                                (selection === 'Vendor' && key.includes('client')) ||
                                (selection === 'Client' && key.includes('vendor'))
                            ) {
                                updatedObj.table_keys[table][key] = false;
                            }
                        });
                    }
                }
            });

            Object.keys(updatedObj.table_keys).forEach(table => {
                if (filterTables.includes(table)) {
                    if (updatedObj.table_keys[table]) {
                        Object.keys(updatedObj.table_keys[table]).forEach(key => {
                            if (
                                (selection === 'Vendor' && key.includes('vendor')) ||
                                (selection === 'Client' && key.includes('client'))
                            ) {
                                updatedObj.table_keys[table][key] = true;
                            }
                        });
                    }
                }
            });

            if (selection === 'Vendor') {
                if (updatedObj.table_keys && updatedObj.table_keys.vendors) {
                    Object.keys(updatedObj.table_keys.vendors).forEach(key => {
                        updatedObj.table_keys.vendors[key] = true;
                    });
                }
            } else if (selection === 'Client') {
                if (updatedObj.table_keys && updatedObj.table_keys.clients) {
                    Object.keys(updatedObj.table_keys.clients).forEach(key => {
                        updatedObj.table_keys.clients[key] = true;
                    });
                }
            } else if (selection === 'All Permissions') {
                Object.keys(updatedObj.table_keys).forEach(table => {
                    if (updatedObj.table_keys[table]) {
                        Object.keys(updatedObj.table_keys[table]).forEach(key => {
                            updatedObj.table_keys[table][key] = true;
                        });
                    }
                });
            }
        }
        return updatedObj;
    };

    const updateFormDataBasedOnSelection = (selection: string, initialLoad: boolean = false) => {
        let updatedFormData = {...props.formData};
        let updatedUiSchema = {...props.uiSchema};
        setFormPermissions(props.formData);

        const updateVisibility = (keysObject: any, visibility: string) => {
            if (keysObject) {
                Object.keys(keysObject).forEach(key => {
                    keysObject[key]["ui:widget"] = visibility;
                });
            }
        };

        if (selection === 'Vendor') {
            updatedFormData = buildSpecificFormSwitch(updatedFormData, false, 'clients');
            updateVisibility(updatedUiSchema.restrictions?.clients, "hidden");
            updateVisibility(updatedUiSchema.table_keys?.clients, "hidden");
            if (!initialLoad) {
                if (updatedFormData.restrictions && updatedFormData.restrictions.vendors) {
                    Object.keys(updatedFormData.restrictions.vendors).forEach(key => {
                        updatedFormData.restrictions.vendors[key] = false;
                    });
                }
                updatedFormData = updateTableKeys(updatedFormData, 'Vendor');
            }
            updateVisibility(updatedUiSchema.restrictions?.vendors, "checkbox");
            updateVisibility(updatedUiSchema.table_keys?.vendors, "checkbox");

        } else if (selection === 'Client') {
            updatedFormData = buildSpecificFormSwitch(updatedFormData, false, 'vendors');
            updateVisibility(updatedUiSchema.restrictions?.vendors, "hidden");
            updateVisibility(updatedUiSchema.table_keys?.vendors, "hidden");

            if (!initialLoad) {
                if (updatedFormData.restrictions && updatedFormData.restrictions.clients) {
                    Object.keys(updatedFormData.restrictions.clients).forEach(key => {
                        updatedFormData.restrictions.clients[key] = false;
                    });
                }
                updatedFormData = updateTableKeys(updatedFormData, 'Client');
            }
            updateVisibility(updatedUiSchema.restrictions?.clients, "checkbox");
            updateVisibility(updatedUiSchema.table_keys?.clients, "checkbox");

        } else {
            updatedFormData = buildSpecificFormSwitch(updatedFormData, true, 'vendors');
            updatedFormData = buildSpecificFormSwitch(updatedFormData, true, 'clients');
            updateVisibility(updatedUiSchema.restrictions?.vendors, "checkbox");
            updateVisibility(updatedUiSchema.restrictions?.clients, "checkbox");

            updateVisibility(updatedUiSchema.table_keys?.vendors, "checkbox");
            updateVisibility(updatedUiSchema.table_keys?.clients, "checkbox");
            updatedFormData = updateTableKeys(updatedFormData, 'All Permissions');
        }

        if (typeof props.onChange === 'function') {
            props.onChange({
                formData: updatedFormData,
                uiSchema: updatedUiSchema,
            });
        }
    };
    return (
        <div>
            {props.title}
            {props.description}
            <Tabs value={activeTab} onChange={changeTab} sx={{'& .MuiTabs-flexContainer': {justifyContent: 'center'}}}>
                <TabList sticky='top' sx={{display: 'inline-flex', justifyContent: 'center'}}>
                    {(tabsAvailable.length > 0) && tabsAvailable.map((tabName: any, index: any) => (
                        <Tab key={`tab-${index}`} value={index} sx={{minWidth: 20}}>
                            {styledName(tabName)}
                        </Tab>
                    ))}
                </TabList>
                {props.properties.map((element: any, index: any) => (
                    <TabPanel key={`father-${index}-${updateKey}`} value={index}>
                        {(element.name === 'restrictions' || element.name === 'table_keys')
                            && <div style={{display: 'flex', justifyContent: 'center'}}>
                                <Select
                                    value={selectedPermission}
                                    onChange={(event: React.SyntheticEvent, value: string) => handleSelectChange(event, value)}
                                    sx={{m: 2}}
                                >
                                    {userPermissions.map((permission: any) => (
                                        <Option key={permission} value={permission}>
                                            {permission}
                                        </Option>
                                    ))}
                                </Select>
                            </div>
                        }
                        {element.name === 'restrictions' || element.name === 'table_keys' ? element.content
                            : <>
                                <MenuPermissions
                                    formData={props.formData} onChange={(updatedFormData) => {
                                    props.formData.menus = updatedFormData.menus;
                                    setFormPermissions(props.formData);
                                }}/>
                            </>}
                    </TabPanel>
                ))}
            </Tabs>
        </div>
    );
};
