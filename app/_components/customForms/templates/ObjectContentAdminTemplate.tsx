import React, {useEffect, useState} from "react";
import {Grid} from "@mui/joy";
import Typography from "@mui/joy/Typography";
import _ from "lodash";

export const ObjectContentAdminTemplate = (props: any): any => {
    const [showContent, setShowContent] = useState(false)

    useEffect(() => {
        if (props.formData && typeof props.formData?.showMenu !== "undefined") {
            setShowContent(props.formData.showMenu);
        }
    }, [props.formData]);

    return (
        <>
            {props.title}
            <Grid container spacing={2} sx={{maxWidth: '1800px'}}>
                {!_.isEmpty(props.properties) ?
                    props.properties.map((element: any, index: any) =>
                        <Grid key={`tab-cont-${index}`} xs={6} md={3} lg={2}>
                            {_.has(props?.formData, "showMenu") ? element.content && element.name === "showMenu" ?
                                    (element.content) :
                                    (showContent && element.content) :
                                (element.content)}
                        </Grid>
                    ) : <div className="flex justify-center mt-3">
                        <Typography level="title-md">
                            No options available
                        </Typography>
                    </div>}
            </Grid>
        </>
    );
};