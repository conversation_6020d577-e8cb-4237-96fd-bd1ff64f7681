import {Divider, Grid} from "@mui/joy";
import React from "react";

export const ObjectTableBodyTemplate = (props: any): any => {
    return (
        <>
            <Grid container>
                <Grid className="px-4 py-5 whitespace-nowrap" xs={4}>
                    <span>
                        {props.title}
                    </span>
                </Grid>
                {props.properties.map((element: any, index: any) =>
                    <Grid
                        className="py-3 whitespace-nowrap"
                        xs={4}
                        key={index}>
                        {element.content}
                    </Grid>
                )}
            </Grid>
            <Divider/>
        </>
    );
};