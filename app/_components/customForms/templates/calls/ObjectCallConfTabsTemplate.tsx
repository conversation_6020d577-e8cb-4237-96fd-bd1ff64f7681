import {Tab, tabClasses, Tab<PERSON>ist, TabPanel, Tabs} from "@mui/joy";
import React from "react";

export const ObjectCallConfTabsTemplate = (props: any): any => {
    return (
        <Tabs aria-label="tabs" defaultValue={0} sx={{ bgcolor: 'transparent' }}>
            <TabList
                disableUnderline
                sx={{
                    p: 0.5,
                    gap: 0.5,
                    borderRadius: 'xl',
                    bgcolor: 'background.level1',
                    [`& .${tabClasses.root}[aria-selected="true"]`]: {
                        boxShadow: 'sm',
                        bgcolor: 'background.surface',
                    },
                }}>
                <Tab disableIndicator>Greetings</Tab>
                <Tab disableIndicator>Tasks</Tab>
            </TabList>
            {props.properties.map((element: any, index: any) => (
                <TabPanel key={`father-call-${index}`} value={index}>
                    {element.content}
                </TabPanel>
            ))}
        </Tabs>
    );
};