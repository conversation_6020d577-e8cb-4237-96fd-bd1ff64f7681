import {Divider, Grid, Typography} from "@mui/joy";
import React, {useEffect, useState} from "react";
import _ from "lodash";

export const ObjectTableBodyUserAdminTemplate = (props: any): any => {
    const [showContent, setShowContent] = useState(false)

    useEffect(() => {
        if (props.formData && typeof props.formData?.showMenu !== "undefined") {
            setShowContent(props.formData.showMenu);
            if (!props.formData.showMenu) {
                setAllToFalse(props.formData)
            }
        }
    }, [props.formData]);

    const setAllToFalse = (obj: any) => {
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                obj[key] = false;
            }
        }
    }

    return (
        <>
            <Grid container>
                <Grid className="px-4 py-5 whitespace-nowrap" xs={12} md={2.5} lg={2.5}>
                    <Typography level="body-md">
                        {props.title}
                    </Typography>
                </Grid>
                {props.properties.map((element: any, index: any) =>
                    <React.Fragment key={`table-menus-${index}`}>
                        {_.has(props?.formData, "showMenu") ? element.content && element.name === "showMenu" ?
                                <Grid key={`showMn-${index}`} xs={12} md={2.5} lg={2.5}>
                                    {element.content}
                                </Grid> :
                                (showContent && element.content) :
                            <Grid className="py-3 whitespace-nowrap" xs={12} md={7} lg={7} key={`tool-${index}`}>
                                {element.content}
                            </Grid>
                        }
                    </React.Fragment>
                )}
            </Grid>
            <Divider/>
        </>
    );
};