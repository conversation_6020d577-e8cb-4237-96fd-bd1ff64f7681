import {Grid, Switch, switchClasses, Typography} from "@mui/joy";
import React, {useState} from "react";
import _ from "lodash";

export const ObjectQuickFormBodyTemplate = (props: any): any => {
    const [switchAll, setSwitchAll] = useState(false)

    const changeSwitch = (event: any) => {
        const newState = event.target.checked;
        setSwitchAll(newState);
        const updatedFormData = _.cloneDeep(props.formData);
        for (let key in updatedFormData) {
            if (updatedFormData.hasOwnProperty(key)) {
                updatedFormData[key] = newState;
            }
        }
        if (props.formContext && typeof props.formContext.handleFormChange === "function") {
            props.formContext.handleFormChange(updatedFormData, props.title);
        }
    }

    return (
        <>
            <Grid container spacing={2} sx={{maxWidth: '1800px'}}>
                <Grid xs={12}>
                    <Switch
                        checked={switchAll}
                        onChange={changeSwitch}
                        color={switchAll ? "success" : "neutral"}
                        variant={switchAll ? "solid" : "outlined"}
                        startDecorator={switchAll ? "Enable All: " : "Disable All: "}
                        sx={(theme) => ({
                            "--Switch-thumbShadow": "0 3px 7px 0 rgba(0 0 0 / 0.12)",
                            "--Switch-thumbSize": "17px",
                            "--Switch-trackWidth": "50px",
                            "--Switch-trackHeight": "27px",
                            "--Switch-trackBackground": theme.vars.palette.background.level3,
                            [`& .${switchClasses.thumb}`]: {
                                transition: "width 0.2s, left 0.2s",
                            },
                            "&:hover": {
                                "--Switch-trackBackground": theme.vars.palette.background.level3,
                            },
                            "&:active": {
                                "--Switch-thumbWidth": "15px",
                            },
                            [`&.${switchClasses.checked}`]: {
                                "--Switch-trackBackground": "rgb(48 209 88)",
                                "&:hover": {
                                    "--Switch-trackBackground": "rgb(48 209 88)",
                                },
                            },
                        })}
                    />
                </Grid>
                {!_.isEmpty(props.properties) ?
                    props.properties.map((element: any, index: any) =>
                        <Grid key={`tab-cont-${index}`} xs={6} md={3} lg={2}>
                            <div className="flex justify-start">
                                {element.content}
                            </div>
                        </Grid>
                    ) :
                    <div className="flex justify-center mt-3">
                        <Typography level="title-md">
                            No options available
                        </Typography>
                    </div>}
            </Grid>
        </>
    );
};