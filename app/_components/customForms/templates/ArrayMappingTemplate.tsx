import React, {useState} from 'react';
import {<PERSON><PERSON>, <PERSON>, Button, Card, Grid, IconButton, Modal, Typography} from '@mui/joy';
import CheckOutlinedIcon from "@mui/icons-material/CheckOutlined";
import CloseIcon from "@mui/icons-material/Close";
import Warning from '@mui/icons-material/Warning';
import DeleteIcon from "@mui/icons-material/Delete";

export const ArrayMappingTemplate = (props: any): any => {
    const {items, canAdd, onAddClick, title, onChange} = props;
    const [open, setOpen] = useState(false);
    const [isNewNodeRemove, setIsNewNodeRemove] = useState(false);
    const [removeFunc, setRemoveFunc] = useState<(() => void) | null>(null);

    const handleWarning = (removeFunction: any, isNewNode: boolean) => {
        setRemoveFunc(removeFunction);
        setIsNewNodeRemove(isNewNode);
        setO<PERSON>(true);
    };

    const handleClose = () => {
        setOpen(false);
        setRemoveFunc(null);
    };

    const handleConfirm = () => {
        if (removeFunc) removeFunc();
        handleClose();
    };

    return (
        <Box>
            <Card
                key={'transferred_to'}
                variant="outlined"
                sx={{
                    marginBottom: '5px',
                    padding: '16px',
                    backgroundColor: 'rgb(249 250 251)'
                }}>
                <Grid container spacing={2}>

                    <Grid xs={6}>
                        <Typography level="body-lg">{title}</Typography>
                    </Grid>
                    {canAdd && (
                        <Grid xs={6}>
                            <Box sx={{display: 'flex', justifyContent: 'flex-end', mr: '15px', mb: '15px', mt: '10px'}}>
                                <Button onClick={onAddClick} color="neutral" variant="solid">
                                    Add new field
                                </Button>
                            </Box>
                        </Grid>
                    )}
                    <Modal
                        aria-labelledby="modal-title"
                        aria-describedby="modal-desc"
                        open={open}
                        onClose={handleClose}
                        sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
                        <Alert
                            variant="soft"
                            color="neutral"
                            invertedColors
                            startDecorator={<Warning sx={{fontSize: '25px'}}/>}
                            sx={{alignItems: 'flex-start', gap: '1rem'}}>
                            <Box sx={{flex: 1}}>
                                <Typography level="title-md">
                                    Are you sure you want to remove this field ?
                                </Typography>
                                {!isNewNodeRemove &&
                                    <Typography level="body-sm">
                                        This process will cause the child nodes to be inherited.
                                    </Typography>}
                                <Box sx={{mt: 2, display: 'flex', justifyContent: 'flex-end', gap: 1}}>
                                    <IconButton variant="soft" size="sm" color="neutral" onClick={handleClose}>
                                        <CloseIcon/>
                                    </IconButton>
                                    <IconButton variant="soft" size="sm" color="neutral" sx={{mr: 1}}
                                                onClick={handleConfirm}>
                                        <CheckOutlinedIcon/>
                                    </IconButton>
                                </Box>
                            </Box>
                        </Alert>
                    </Modal>

                    {items && items.map((element: any, index: number) => (
                        <Card key={element.index} variant="outlined" sx={{padding: '20px', marginBottom: '20px', width: '100%'}}>
                            <Box sx={{display: 'flex', flexDirection: 'column'}}>
                                <Grid
                                    container
                                    key={element.index}
                                    alignItems="center"
                                    xs={12}
                                    spacing={2}>
                                    <Grid xs={11} container spacing={2}>
                                        <Grid xs={12}>
                                            {React.cloneElement(element.children, {
                                                uiSchema: {
                                                    ...element.children.props.uiSchema,
                                                    'ui:title': `Field ${index + 1}`,
                                                },
                                                formData: element.children.props.formData,
                                            })}
                                        </Grid>
                                    </Grid>
                                    <Grid xs={1} sx={{alignSelf: 'flex-start'}}>
                                        <Box sx={{display: 'flex', justifyContent: 'center'}}>
                                        <IconButton
                                            color="danger" variant="solid"
                                            onClick={() => handleWarning(() => element.onDropIndexClick(element.index), element)}>
                                            <DeleteIcon/>
                                        </IconButton>
                                        </Box>
                                    </Grid>
                                </Grid>
                            </Box>
                        </Card>)
                    )}
                </Grid>
            </Card>
        </Box>);
}
