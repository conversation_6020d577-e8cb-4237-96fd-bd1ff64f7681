import * as React from 'react';
import Drawer from '@mui/joy/Drawer';
import Checkbox from '@mui/joy/Checkbox';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';
import ModalClose from '@mui/joy/ModalClose';
import Divider from '@mui/joy/Divider';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
import Sheet from '@mui/joy/Sheet';
import Typography from '@mui/joy/Typography';
import Done from '@mui/icons-material/Done';
import SettingsIcon from '@mui/icons-material/Settings';
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import _, {map} from 'lodash';
import {setChangeShowColumns} from "@/app/_components/table/states/changeShowColumns";
import {Box, Button, Tooltip} from '@mui/joy';
import {useEntity} from "simpler-state";

export const DrawerColumnsVisibilityConfiguration: React.FC<any> = ({dbTableName, defaultColumnsVisibility}) => {
    const [open, setOpen] = React.useState(false);
    const {
        columnsVisibilityEntityConfiguration,
        setColumnsVisibilityEntityConfiguration
    } = createTableEntities(dbTableName);
    const columnsVisibilityEntityConfigurationEntity = useEntity(columnsVisibilityEntityConfiguration)
    const handleCheckboxChange = (key: string, value: any) => {
        setColumnsVisibilityEntityConfiguration({
            ...columnsVisibilityEntityConfiguration.get(),
            [key]: {...value, show: !value.show}
        });
        setChangeShowColumns();
    };

    const onEnableAll = () => {
        const updatedData = _.mapValues(columnsVisibilityEntityConfigurationEntity, (value: any) => {
            return {...value, show: true};
        });
        setColumnsVisibilityEntityConfiguration(updatedData)
        setChangeShowColumns();
    }

    const onDisableAll = () => {
        const updatedData = _.mapValues(columnsVisibilityEntityConfigurationEntity, (value: any) => {
            return {...value, show: false};
        });
        setColumnsVisibilityEntityConfiguration(updatedData)
        setChangeShowColumns();
    }

    const onSetDefault = () => {
        setColumnsVisibilityEntityConfiguration(defaultColumnsVisibility)
        setChangeShowColumns()
    }

    return (
        <>
            <Tooltip title="Table Settings" variant="soft" placement="left">
                <SettingsIcon
                    onClick={() => setOpen(true)}
                    sx={{
                        position: 'absolute',
                        right: '0',
                        marginRight: '10px',
                        cursor: 'pointer',
                        top: '70px',
                        fontSize: '30px'
                    }}
                />
            </Tooltip>
            <Drawer
                size="md"
                variant="plain"
                open={open}
                anchor={"right"}
                onClose={() => setOpen(false)}
                sx={{marginTop: '40px', width: '400px'}}
                slotProps={{
                    content: {
                        sx: {
                            bgcolor: 'transparent',
                            p: {md: 3, sm: 0},
                            boxShadow: 'none',
                        },
                    },
                }}
            >
                <Sheet
                    sx={{
                        borderRadius: 'md',
                        p: 2,
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 2,
                        height: '100%',
                        overflow: 'auto',
                        marginTop: '40px',
                    }}
                >
                    <DialogTitle>Table config</DialogTitle>
                    <ModalClose/>
                    <Divider sx={{mt: 'auto'}}/>
                    <DialogContent sx={{gap: 2}}>
                        <Typography level="title-md" fontWeight="bold" sx={{mt: 1}}>
                            Columns
                        </Typography>
                        <div role="group" aria-labelledby="rank" className="p-2">
                            <Box sx={{marginBottom: '20px', textAlign: 'center'}}>
                                <Button onClick={onSetDefault} color={'neutral'} variant="outlined"
                                        style={{fontSize: '12px'}}>Default</Button>
                                <Button onClick={onEnableAll} color={'neutral'} className=""
                                        sx={{fontSize: '12px', marginLeft: '10px'}}
                                        variant="outlined">Enable All</Button>
                                <Button onClick={onDisableAll} color={'neutral'} variant="outlined" className="mt-4"
                                        sx={{fontSize: '12px', marginLeft: '10px'}}>Disable All</Button>

                            </Box>
                            <Divider/>
                            <List
                                orientation="horizontal"
                                wrap
                                sx={{
                                    '--List-gap': '8px',
                                    '--ListItem-radius': '20px',
                                    '--ListItem-minHeight': '32px',
                                    '--ListItem-gap': '4px',
                                    marginTop: '20px'
                                }}
                            >
                                {map(columnsVisibilityEntityConfigurationEntity, (value: any, key) => {
                                    const {label, show} = value;

                                    return (
                                        <ListItem key={key}>
                                            {show && (
                                                <Done
                                                    color="primary"
                                                    sx={{ml: -0.5, zIndex: 2, pointerEvents: 'none', fontSize: '15px'}}
                                                />
                                            )}
                                            <Checkbox
                                                size="sm"
                                                disableIcon
                                                overlay
                                                label={label}
                                                checked={show}
                                                variant={show ? 'soft' : 'outlined'}
                                                onChange={() => handleCheckboxChange(key, value)}
                                                slotProps={{
                                                    action: ({checked}) => ({
                                                        sx: checked
                                                            ? {
                                                                border: '1px solid',
                                                                borderColor: 'primary.500',
                                                            }
                                                            : {},
                                                    }),
                                                }}
                                            />
                                        </ListItem>
                                    );
                                })}
                            </List>
                        </div>
                    </DialogContent>
                </Sheet>
            </Drawer>
        </>
    );
};
