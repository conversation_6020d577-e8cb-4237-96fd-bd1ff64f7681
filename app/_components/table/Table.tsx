'use client'
import React, {useEffect, useState} from "react";
import {getCoreRowModel, getPaginationRowModel, useReactTable} from "@tanstack/react-table";
import dynamic from "next/dynamic";
import {buildColumns, buildRowsConfigurations, hasDifferentKeys} from "@/app/_components/table/functions/buildColumns";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import {DrawerColumnsVisibilityConfiguration} from "@/app/_components/table/DrawerColumnsVisibilityConfiguration";
import {useEntity} from "simpler-state";
import {changeShowColumnsEntity} from "@/app/_components/table/states/changeShowColumns";
import _ from "lodash";
import Loading from "react-loading";
import {GlobalSearchComponent} from "@/app/_components/table/dataComponents/GlobalSearchComponent";
import {Button, Grid, Tooltip} from "@mui/joy";
import {DateTime} from "luxon";
import {setReFetchDataCount} from "@/app/_components/table/states/changeDataState";
import {CustomFilters} from "@/app/_components/filters/custom";

const TableRows: any = dynamic(() => import('@/app/_components/table/TableRows'), {ssr: false})
const PaginationComponent: any = dynamic(() => import('@/app/_components/table/paginationComponent'), {ssr: false})
const TableHead: any = dynamic(() => import('@/app/_components/table/TableHead'), {ssr: false})

export const Table: React.FC<any> = ({
                                         globalSearchConfig,
                                         tableColumns,
                                         dbTableName,
                                         dbName,
                                         tableDetails,
                                         dataUpdatedAt,
                                         isLoading,
                                         reFetch,
                                         count,
                                         loading,
                                         data,
                                         fullTableConfiguration,
                                         onCustomFilterChange,
                                         hasSearchParams,
                                         onClearFilters,
                                         onDeleteRow
                                     }) => {
    const [shouldFetchCount, setShouldFetchCount] = useState(false)
    const getTZ = tableDetails?.userMetadata?.timezone ?? "America/Chicago"
    const lastFetchedDate = DateTime.fromMillis(dataUpdatedAt)
        .setZone(getTZ)
        .toFormat('LLL dd yy, HH:mm ZZZZ');
    const {columns, rowsConfigurations} = buildColumns(tableColumns);
    const {
        columnsVisibilityEntityConfiguration,
        setColumnsVisibilityEntityConfiguration
    } = createTableEntities(dbTableName);
    const columnsVisibilityConfiguration = useEntity(columnsVisibilityEntityConfiguration);
    const columnVisibilityInitValue =
        buildRowsConfigurations(_.isEmpty(columnsVisibilityConfiguration) || hasDifferentKeys(columnsVisibilityConfiguration, rowsConfigurations) ? rowsConfigurations : columnsVisibilityConfiguration);
    const [columnVisibility, setColumnVisibility] = useState(columnVisibilityInitValue);
    const changeShowColumns = useEntity(changeShowColumnsEntity);

    useEffect(() => {
        if (!_.isEmpty(data)) setShouldFetchCount(!shouldFetchCount)
        const columns = _.isEmpty(columnsVisibilityConfiguration) || hasDifferentKeys(columnsVisibilityConfiguration, rowsConfigurations) ? rowsConfigurations : columnsVisibilityConfiguration
        setColumnsVisibilityEntityConfiguration(columns);
    }, []);

    useEffect(() => {
        if (changeShowColumns !== 0) {
            const newColumnsVisibility = buildRowsConfigurations(columnsVisibilityConfiguration);
            setColumnVisibility(newColumnsVisibility);
        }
    }, [changeShowColumns]);

    const table = useReactTable({
        data: data || [],
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        manualPagination: true,
        pageCount: count,
        state: {
            columnVisibility: columnVisibility
        },
        meta: {
            dbTableName: dbTableName
        }
    });

    const onEnableAll = () => {
    }

    const onDisableAll = () => {
    }

    const reFetchData = () => {
        setReFetchDataCount()
        reFetch()
    }

    const onFilter = (event: any, customFilter: any) => {
        onCustomFilterChange({event, customFilter})
    }

    return (
        <>
            <DrawerColumnsVisibilityConfiguration
                dbTableName={dbTableName}
                defaultColumnsVisibility={rowsConfigurations}
                enableAll={onEnableAll}
                disableAll={onDisableAll}
            />
            <PaginationComponent
                dbTableName={dbTableName}
                dbName={dbName}
                tableColumns={tableColumns}
                tableDetails={tableDetails}
                loading={loading}
                dataTable={data}
                shouldFetchCount={shouldFetchCount}
                onClearFilters={onClearFilters}
            />
            <Grid container spacing={2} sx={{margin: '15px 0'}}>
                {globalSearchConfig?.show &&
                    <Grid xs={12} md={6} lg={6}>
                        <GlobalSearchComponent
                            loading={loading}
                            fieldsSearch={globalSearchConfig?.fields}
                        />
                    </Grid>
                }
                {!hasSearchParams && fullTableConfiguration?.filterComponents?.map((customFilter, index) => (
                    <Grid xs={12} key={`custom-filter-${index}`}>
                        <CustomFilters component={customFilter.component}
                                       onFilter={(event) => onFilter(event, customFilter)}
                                       defaultFilter={customFilter.defaultFilters}
                                       hasSearchParams={hasSearchParams}

                        />
                    </Grid>
                ))}

                <Grid xs={12} sx={{width: '100%'}}
                      md={globalSearchConfig?.show ? 6 : 12}
                      lg={globalSearchConfig?.show ? 6 : 12}>
                    <Tooltip title="Update data" variant="soft" placement="top">
                        <Button size="md" variant='soft'
                                loading={isLoading}
                                onClick={() => reFetchData()} color="neutral">
                            Last search on: {lastFetchedDate}
                        </Button>
                    </Tooltip>
                </Grid>
            </Grid>
            <div className={'justify-center mx-auto items-center w-100 gap-2 overflow-auto block'}>
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                    <TableHead table={table} dbTableName={dbTableName}/>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                    {!loading ? (
                        <TableRows
                            fullTableConfiguration={fullTableConfiguration}
                            table={table}
                            tableColumns={tableColumns}
                            tableDetails={tableDetails}
                            dbTableName={dbTableName}
                            onDeleteRow={onDeleteRow}
                        />
                    ) : (
                        <tr style={{height: '45px'}}>
                            <td colSpan={100} className="text-center py-4" style={{padding: 0}}>
                                <div className="flex justify-center"
                                     style={{position: 'absolute', width: '97%', marginTop: '-12px'}}>
                                    <Loading color={'black'} width={'30px'} height={'30px'} type={'spin'}/>
                                </div>
                            </td>
                        </tr>
                    )}
                    </tbody>
                </table>
            </div>
        </>
    );
};
