'use client'
import React from "react";
import {DataComponentFactory} from "@/app/_components/table/dataComponents/DataComponentFactory";
import {updateRow} from "@/app/_lib/supabase/updateRow";

export const TableCell: React.FC<any> = ({getValue, row, column, table}) => {
    const initialValue = getValue();
    const {config} = column.columnDef.meta
    const dbOptionsColumns = table?.options?.columns
    const dbTableName = table.options?.meta?.dbTableName
    const onUpdate = async (event: any) => {
        try{
            const id = table.getRowModel().rows[row.index].original.id;
            const dbTableName = table.options.meta.dbTableName;
            await updateRow(id, event, dbTableName);
        }catch (error) {
            console.error('Error to update data: ',error);
            console.error('payload: ', event)
        }
    };

    return (
        <>
            <DataComponentFactory
                onUpdate={onUpdate}
                initialValue={initialValue}
                configuration={config}
                columnName={column.id}
                dbTableName={dbTableName}
            />
        </>
    );
};
