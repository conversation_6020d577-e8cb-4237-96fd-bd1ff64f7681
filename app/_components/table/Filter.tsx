import React, {useEffect, useState} from "react";
import {useEntity} from "simpler-state";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import FilterFactory from "@/app/_components/table/filters/FilterFactory";
import {getColumnId} from "@/app/_components/table/functions/getColumnId";
import {showFiltersGS} from "@/app/_components/table/states/globalSearchStates";
import SwapVertOutlinedIcon from "@mui/icons-material/SwapVertOutlined";
import NorthOutlinedIcon from '@mui/icons-material/NorthOutlined';
import SouthOutlinedIcon from '@mui/icons-material/SouthOutlined';
import {Grid, IconButton, Tooltip} from "@mui/joy";
import {styledName} from "@/app/_components/utils/styledName";
import _ from "lodash";
import {useQueryClient} from "@tanstack/react-query";
import {clearFiltersChange} from "@/app/_components/table/states/clearFiltersState";
import {debugFilters} from "@/app/_components/table/states/debugFiltersState";

const Filter: React.FC<any> = ({column, dbTableName, filterComponent}) => {
    const {
        paginateConfigEntity,
        setPaginateConfigEntity,
        filtersEntity,
        setFiltersEntity,
        defaultFiltersEntity
    } = createTableEntities(dbTableName);
    const columnsExcludes = ['actions', 'rules', 'tags', 'tags_names']
    const filters = useEntity(filtersEntity);
    const debugFilter = useEntity(debugFilters);

    const paginateConfig = useEntity(paginateConfigEntity);
    const showFilters = useEntity(showFiltersGS);
    const clearFiltersChangeEntity = useEntity(clearFiltersChange);
    const [orderCol, setOrderCol] = useState(0);
    const defaultTitleTT = `Order ${styledName(column?.columnDef?.header || "column")} by ${(orderCol === -1) ? "DESC" : "ASC"}`
    const [titleTT, setTitleTT] = useState(defaultTitleTT);
    const columnId = getColumnId(column)
    const [activeSortButton, setActiveSortButton] = useState(false);
    const queryClient = useQueryClient();

    useEffect(() => {
        setActiveSortButton(false)
        setTitleTT(defaultTitleTT)
        const checkExistCol = _.has(paginateConfig?.sort, columnId)
        if (checkExistCol) {
            setActiveSortButton(checkExistCol)
        }
    }, [orderCol]);

    useEffect(() => {
        if (clearFiltersChangeEntity != 0) {
            const filters = debugFilter && debugFilter.filters && !_.isEmpty(debugFilter.filters) ? debugFilter.filters : defaultFiltersEntity.get()
            setFiltersEntity(filters)
        }
    }, [clearFiltersChangeEntity]);

    const applyFilterColumn = (value: any, filter: any) => {
        const updatedFilters = Object.keys(filters)
            .filter(key => !key.startsWith('tags-'))
            .reduce((acc, key) => {
                acc[key] = filters[key];
                return acc;
            }, {});

        const newFilters = {
            ...filters,
            [`${columnId}-${filter.filterType}`]: {...filter, label: column.id}
        }

        setFiltersEntity(newFilters);
        localStorage.setItem(`db_${dbTableName}-filters`, JSON.stringify(newFilters));
        column.setFilterValue(value);
        setPaginateConfigEntity({...paginateConfig, page: 0, cursor: 0, minCursor: 0});
        invalidateQuery();
    }

    const getNextOrderState = (currentState: number) => {
        const states = [-1, 0, 1];
        const currentIndex = _.indexOf(states, currentState);
        const nextIndex = (currentIndex + 1) % states.length;
        return states[nextIndex];
    };

    const sortButton = () => {
        let newValueOrd = getNextOrderState(orderCol)
        const orderText = newValueOrd === 1 ? 'ASC' : newValueOrd === -1 ? 'DESC' : 'NONE';
        const newTTName = `Order ${styledName(column?.columnDef?.header || "column")} by ${orderText}`;
        setOrderCol(newValueOrd)
        setTitleTT(newTTName)
        const objValue = (newValueOrd === 0 && _.has(paginateConfig?.sort, columnId)) ?
            _.omit(paginateConfig, ["sort"]) : {...paginateConfig, sort: {[columnId]: newValueOrd}}
        setPaginateConfigEntity(objValue)
        invalidateQuery()
    }

    const invalidateQuery = () => {
        queryClient.invalidateQueries({queryKey: [`${dbTableName}-count`]});
        queryClient.invalidateQueries({queryKey: [`table_data_${dbTableName}`]});
    }

    const getIconSort = () => {
        switch (orderCol) {
            case -1:
                return <SouthOutlinedIcon sx={{fontSize: '16px'}}/>
            case 1:
                return <NorthOutlinedIcon sx={{fontSize: '16px'}}/>
            case 0:
                return <SwapVertOutlinedIcon/>
        }
    }

    return (<>
        <Grid container spacing={3}>
            {filterComponent && !_.isEmpty(filterComponent) && showFilters &&
                <Grid xs={10}>
                    <FilterFactory component={filterComponent}
                                   applyFilterColumn={applyFilterColumn}
                                   filters={filters}
                                   column={column}
                                   dbTableName={dbTableName}/>
                </Grid>}
            {!_.includes(columnsExcludes, columnId) &&
                <Grid xs={(filterComponent) ? 2 : 12}>
                    <Tooltip title={titleTT} variant="soft" placement="top">
                        <IconButton size="sm" sx={{height: '34px'}} variant={activeSortButton ? "solid" : "soft"}
                                    onClick={() => sortButton()}>
                            {getIconSort()}
                        </IconButton>
                    </Tooltip>
                </Grid>}
        </Grid>
    </>)
}
export default Filter;
