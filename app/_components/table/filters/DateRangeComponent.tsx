import React, {useEffect, useState} from "react";
import Modal from "@mui/joy/Modal";
import Sheet from "@mui/joy/Sheet";
import ModalClose from "@mui/joy/ModalClose";
import {DateRange} from "react-date-range";
import Button from "@mui/joy/Button";
import CalendarMonthTwoToneIcon from "@mui/icons-material/CalendarMonthTwoTone";
import {DateTime} from "luxon";
import Box from "@mui/joy/Box";
import {Grid, Input, Typography} from "@mui/joy";
import {getISOWeek, isWithinInterval} from "date-fns";
import {pickRangeDateByButtons} from "@/app/_components/table/functions/pickRangeDateByButtons";
import {getPlaceholderDates} from "@/app/_components/table/functions/getPlaceholderDates";
import {generateWeeksForMonth} from "@/app/_components/table/functions/generateWeeksMonth";
import {getWeekNumbers} from "@/app/_components/reports/func/getWeeksNumbers";

export const DateRangeComponent: React.FC<any> = ({
                                                      onApplyFilters,
                                                      defaultValues,
                                                      inputStyles,
                                                      hiddeClear,
                                                      viewNormalButton = true,
                                                      isLoading = false
                                                  }) => {
    const [showDateRangeModal, setShowDateRangeModal] = useState(false);
    const [startDate, setStartDate] = useState(defaultValues?.startDate);
    const [endDate, setEndDate] = useState(defaultValues?.endDate);
    const [allButton, setAllButton] = useState(false);
    const [selectedWeek, setSelectedWeek] = useState(null);
    const initialDate = DateTime.fromISO(startDate);
    const [selectedYear, setSelectedYear] = useState(initialDate.year);
    const [selectedMonth, setSelectedMonth] = useState(initialDate.month);
    const [weeks, setWeeks] = useState(generateWeeksForMonth(selectedYear, selectedMonth));
    const [staticFilter, setStaticFilter] = useState('custom');
    const [dateRange, setDateRange] = useState<any[]>([]);
    const [placeHolder, setPlaceholder] = useState(getPlaceholderDates(startDate, endDate));

    useEffect(() => {
    }, [weeks]);

    useEffect(() => {
        const startDate = getDefaultStateRangeDate(defaultValues.startDate);
        const endDate = getDefaultStateRangeDate(defaultValues.endDate);
        setDateRange([{startDate, endDate, key: 'selection'}]);
        setStartDate(defaultValues?.startDate);
        setEndDate(defaultValues?.endDate);
        setStaticFilter('lastWeek');
        setPlaceholder(getPlaceholderDates(defaultValues?.startDate, defaultValues?.endDate));
    }, [defaultValues]);

    useEffect(() => {
        const placeholder = getPlaceholderDates(startDate, endDate);
        setPlaceholder(placeholder);
        setDateRange([{
            startDate: getDefaultStateRangeDate(startDate),
            endDate: getDefaultStateRangeDate(endDate), key: 'selection'
        }]);
    }, [startDate, endDate]);

    const getDefaultStateRangeDate = (date: any) => {
        return date && date !== '' && !date.includes('NA_FILTER') ? DateTime.fromISO(date).toJSDate() : new Date();
    };

    const clearFilter = () => {
        setStartDate('NA_FILTER');
        setEndDate('NA_FILTER');
        setShowDateRangeModal(false);
        applyCustomFilter('today');
        onApplyFilters({startDate: 'NA_FILTER', endDate: 'NA_FILTER'});
    };

    const applyDateFilter = () => {
        if (dateRange && dateRange.length > 0) {
            const dateRangeStartDate = dateRange[0].startDate ? dateRange[0].startDate : new Date();
            const dateRangeEndDate = dateRange[0].endDate ? dateRange[0].endDate : new Date();

            const startDateFormatted = (!viewNormalButton)
                ? DateTime.fromJSDate(dateRangeStartDate).startOf('week').toFormat('yyyy-MM-dd')
                : DateTime.fromJSDate(dateRangeStartDate).toFormat('yyyy-MM-dd');
            const endDateFormatted = (!viewNormalButton)
                ? DateTime.fromJSDate(dateRangeEndDate).endOf('week').toFormat('yyyy-MM-dd')
                : DateTime.fromJSDate(dateRangeEndDate).toFormat('yyyy-MM-dd');

            const weeks = (!viewNormalButton) ? getWeekNumbers(startDateFormatted, endDateFormatted) : [];

            setStartDate(startDateFormatted);
            setEndDate(endDateFormatted);
            onApplyFilters({startDate: startDateFormatted, endDate: endDateFormatted, weeks});
        }
        setShowDateRangeModal(false);
    };

    const applyCustomFilter = (filter: string) => {
        setStaticFilter(filter);
        setDateRange(pickRangeDateByButtons(filter))
    };

    const handleDateChange = (item: any) => {
        const {startDate, endDate, key} = item?.selection;
        setSelectedWeek(null);
        setDateRange(prevDateRange => {
            const newDateRange = [...prevDateRange];
            if (startDate !== prevDateRange[0].startDate) {
                newDateRange[0].startDate = startDate;
            }
            if (endDate !== prevDateRange[0].endDate) {
                newDateRange[0].endDate = endDate;
            }
            return newDateRange;
        });
    };

    const viewDateChange = (item: any) => {
        const initialDate = DateTime.fromJSDate(new Date(item));
        const year = initialDate.year;
        const month = initialDate.month;
        setWeeks(generateWeeksForMonth(year, month));
        setSelectedWeek(null);
        setAllButton(false);
    }

    const handleWeekSelect = (weekObj: any) => {
        setAllButton(false);
        setSelectedWeek(weekObj);
        const startDate = DateTime.fromISO(weekObj?.startDate).toJSDate();
        const endDate = DateTime.fromISO(weekObj?.endDate).toJSDate();
        setDateRange([{startDate, endDate, key: "selection"}])
    };

    const handleSelectAllWeeks = () => {
        setAllButton(true);
        setSelectedWeek(null);
        const startDate = DateTime.fromISO(weeks[0].startDate).toJSDate();
        const endDate = DateTime.fromISO(weeks[weeks.length - 1].endDate).toJSDate();
        setDateRange([{startDate, endDate, key: "selection"}])
    }

    const findSelectedWeeks = (weekNumber: number) => {
        return selectedWeek?.weekNumber === weekNumber;
    }

    const getFilterButtons = () => {
        const staticFilters = [
            {label: 'Today', value: 'today'},
            {label: 'Yesterday', value: 'yesterday'},
            {label: 'Last Week', value: 'lastWeek'},
            {label: DateTime.now().minus({months: 0}).toFormat('MMMM'), value: 'thisMonth'},
        ];

        const dynamicFilters = Array.from({length: 6}, (_, i) => {
            const monthLabel = DateTime.now().minus({months: i + 1}).toFormat('MMMM');
            return {label: monthLabel, value: `last${i + 1}Month`};
        });

        const allFilters = [...staticFilters, ...dynamicFilters];

        return allFilters.map(({label, value}, index) => (
            <Grid xs={5} sm={6} key={`${value}-${index}`} justifyContent={"center"}>
                <Box sx={{
                    display: {
                        xs: 'flex',
                        sm: 'flex',
                        md: 'contents'
                    }, justifyContent: 'center'
                }}>
                    <Button
                        sx={{
                            width: '130px',
                            marginTop: '15px',
                            marginLeft: {xs: '5px'},
                        }}
                        size="md"
                        className={value !== 'today' ? 'sm:ms-2' : ''}
                        variant={(staticFilter === value) ? 'solid' : 'outlined'}
                        onClick={() => applyCustomFilter(value)}
                        color="neutral">
                        {label}
                    </Button>
                </Box>
            </Grid>
        ));
    };

    const renderDayContent = (date: any, range: any) => {
        const weekNumber = getISOWeek(date);
        const isSelected = range &&
            isWithinInterval(date, {start: range[0].startDate, end: range[0].endDate});
        return (
            <div style={{position: 'relative'}}>
                <div style={{
                    position: 'relative',
                    color: isSelected ? '#ffffff' : '#636B74',
                    top: '-4px'
                }}>
                    {date.getDate()}
                </div>
                <div style={{
                    fontSize: '0.7em',
                    color: isSelected ? '#ffffff' : '#636B74',
                    position: 'absolute',
                    top: '7px', left: '-2px'
                }}>
                    W:{weekNumber}
                </div>
            </div>
        );
    };

    return (
        <>
            <Modal
                aria-labelledby="modal-title"
                aria-describedby="modal-desc"
                open={showDateRangeModal}
                onClose={() => setShowDateRangeModal(false)}
                sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    overflowY: 'auto',
                    maxHeight: '80%',
                    marginTop: {
                        xs: '10vh',
                        sm: '10vh',
                        md: 0,
                        lg: 0
                    },
                    overflowX: 'hidden',
                    zIndex: 9999
                }}>
                <Sheet
                    variant="outlined"
                    sx={{
                        maxWidth: 730,
                        borderRadius: 'md',
                        p: 3,
                        boxShadow: 'lg',
                        padding: '40px'
                    }}>
                    <ModalClose variant="plain" sx={{m: 1}}/>
                    <Box sx={{marginTop: {xs: '60%', sm: '35%', md: '35px', lg: '30px'}}}
                         className="sm:block md:flex items-center">
                        <div className="date-range-container flex justify-center">
                            <DateRange
                                editableDateInputs={true}
                                onChange={handleDateChange}
                                onShownDateChange={viewDateChange}
                                ranges={dateRange}
                                weekStartsOn={1}
                                retainEndDateOnFirstSelection={true}
                                dayContentRenderer={(date) => renderDayContent(date, dateRange)}
                                rangeColors={['#636B74']}
                                color={"#000000"}
                            />
                        </div>
                        <div>
                            <Grid container spacing={2} ml={1} justifyContent={"center"}>
                                {viewNormalButton ? getFilterButtons() : <>
                                    <Grid xs={12} key="select-all-weeks">
                                        <Button
                                            fullWidth
                                            variant={allButton ? 'solid' : 'outlined'}
                                            color="neutral"
                                            onClick={() => handleSelectAllWeeks()}>
                                            All Weeks
                                        </Button>
                                    </Grid>
                                    {weeks.map((item: any) => (
                                        <Grid xs={12} key={item?.weekNumber}>
                                            <Button
                                                fullWidth
                                                variant={findSelectedWeeks(item?.weekNumber) ? 'solid' : 'outlined'}
                                                color="neutral"
                                                onClick={() => handleWeekSelect(item)}>
                                                <div className="flex flex-col justify-center">
                                                    <Typography
                                                        textColor={findSelectedWeeks(item?.weekNumber) ? 'white' : 'black'}
                                                        level="body-sm" textAlign="center">
                                                        Week {item?.weekNumber}
                                                    </Typography>
                                                    <Typography
                                                        textColor={findSelectedWeeks(item?.weekNumber) ? 'white' : 'black'}
                                                        level="body-xs" textAlign="center">
                                                        {item?.startView} to {item?.endView}
                                                    </Typography>
                                                </div>
                                            </Button>
                                        </Grid>
                                    ))}
                                </>}
                            </Grid>
                        </div>
                    </Box>
                    <div className={'text-right mt-5'}>
                        {!hiddeClear &&
                            <Button sx={{marginRight: "25px"}} onClick={clearFilter} size="md" variant='outlined'
                                    color="neutral">
                                Clear
                            </Button>}
                        <Button onClick={applyDateFilter} className="ms-3" size="md" variant='outlined' color="neutral">
                            Apply
                        </Button>
                    </div>
                </Sheet>
            </Modal>
            <Input
                disabled={isLoading}
                placeholder={placeHolder}
                onClick={() => setShowDateRangeModal(true)}
                readOnly
                startDecorator={<CalendarMonthTwoToneIcon sx={{fontSize: '16px'}}/>}
                style={{
                    ...inputStyles,
                    backgroundColor: startDate && startDate !== '' && !startDate.includes('NA_FILTER') ? '#DCF0F5' : 'white',
                    height: '23px !important',
                    cursor: 'pointer',
                    marginRight: '45px',
                    width: '230px',
                }}
                slotProps={{
                    input: {
                        style: {
                            cursor: 'pointer',
                            fontSize: '12px',
                        },
                    },
                }}
            />
        </>
    );
};
