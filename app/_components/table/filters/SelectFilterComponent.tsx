import React, {useEffect, useState} from 'react';
import {FilterComponentProps} from "@/app/_components/table/filters/func/filterFactoryProps.interface";
import {clearFiltersChange} from "@/app/_components/table/states/clearFiltersState";
import {useEntity} from "simpler-state";
import {checkArray} from "@/app/_components/table/functions/checkArray";
import {checkFilterValue} from "@/app/_components/table/filters/func/checkFilterValue";

const SelectFilterComponent: React.FC<FilterComponentProps> = ({
                                                                   columnFilterValue,
                                                                   applyFilterColumn,
                                                                   column,
                                                                   dbTableName
                                                               }) => {
    let {selectOptions} = column.columnDef.meta.config || {selectOptions: []};
    const checkSelOpts = checkArray(selectOptions)
    const [selects, setSelects] = useState(checkSelOpts)
    const [optionSelected, setOptionSelected] = useState("")
    const clearFiltersChangeEntity = useEntity(clearFiltersChange);

    useEffect(() => {
        setOptionSelected(checkFilterValue(columnFilterValue) ?? '')
    }, [columnFilterValue]);

    useEffect(() => {
        if (clearFiltersChangeEntity != 0) {
            setOptionSelected((columnFilterValue ?? '') as string)
        }
    }, [clearFiltersChangeEntity]);

    const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const selectedValue = event.target.value;
        setOptionSelected(selectedValue);
        let value: any = selectedValue;
        if (selectedValue === "NULL") {
            value = null;
        }
        if (selectedValue === "true") {
            value = true;
        }
        if (selectedValue === "false") {
            value = false;
        }
        applyFilterColumn(selectedValue, {value, filterType: 'eq'});
    };

    const getBackgroundFilter = () => {
        return optionSelected && optionSelected != '' && optionSelected != 'NA_FILTER' ? '#DCF0F5' : 'white';
    }

    return (
        <div>
            <select
                className='shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline'
                value={optionSelected}
                style={{backgroundColor: getBackgroundFilter(), maxWidth: '300px'}}
                onChange={handleSelectChange}>
                <option value="" disabled>
                    Select an option
                </option>
                <option value="NA_FILTER">All</option>
                <option value="NULL">Null</option>
                {selects && selects.map((option: any, index: number) => (
                    <option key={index} value={option.value}>{option.label}</option>))}
            </select>
        </div>
    );
};

export default SelectFilterComponent;
