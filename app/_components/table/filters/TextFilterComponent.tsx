import React, {useEffect, useState} from "react";
import {FilterComponentProps} from "@/app/_components/table/filters/func/filterFactoryProps.interface";
import {useEntity} from "simpler-state";
import {clearFiltersChange} from "@/app/_components/table/states/clearFiltersState";
import {checkFilterValue} from "@/app/_components/table/filters/func/checkFilterValue";

const TextFilterComponent: React.FC<FilterComponentProps> = ({
                                                                 columnFilterValue,
                                                                 applyFilterColumn,
                                                             }) => {
    const [value, setValue] = useState('')
    const [changed, setChanged] = useState(false);
    const clearFiltersChangeEntity = useEntity(clearFiltersChange);

    useEffect(() => {
        const getCustomValue = columnFilterValue ? checkFilterValue(columnFilterValue.replaceAll('%', '')) : '';
        setValue(getCustomValue);
    }, [columnFilterValue]);

    useEffect(() => {
        if (clearFiltersChangeEntity != 0) {
            setValue(columnFilterValue ? columnFilterValue.replaceAll('%', '') : '');
        }
    }, [clearFiltersChangeEntity]);

    const onChangeValue = (e: any) => {
        setValue(e.target.value);
        setChanged(true);
    }

    const onKeyUp = (event: any) => {
        if (event.key === 'Enter' && changed) {
            applyFilterColumn(value, {value: value === '' ? 'NA_FILTER' : value, filterType: 'match'})
            setChanged(false)
        }
    }

    const onBlur = (event: any) => {
        if (changed) {
            applyFilterColumn(value, {value: value === '' ? 'NA_FILTER' : value, filterType: 'match'})
            setChanged(false);
        }
    }
    return (<>
        <input
            style={{backgroundColor: value && value != '' ? '#DCF0F5' : 'white', maxWidth: '300px'}}
            type="text"
            value={value as string}
            onChange={e => onChangeValue(e)}
            onKeyUp={onKeyUp}
            onBlur={onBlur}
            placeholder={`Search...`}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
        />
    </>)
}

export default TextFilterComponent;
