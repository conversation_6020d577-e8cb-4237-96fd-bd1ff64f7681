import React, {useEffect, useState} from "react";
import {FilterComponentProps} from "@/app/_components/table/filters/func/filterFactoryProps.interface";
import {useEntity} from "simpler-state";
import {clearFiltersChange} from "@/app/_components/table/states/clearFiltersState";
import {checkFilterValue} from "@/app/_components/table/filters/func/checkFilterValue";

const NullableComponent: React.FC<FilterComponentProps> = ({
                                                               columnFilterValue,
                                                               applyFilterColumn,
                                                               column,
                                                               dbTableName
                                                           }) => {

    let {nullLabels} = column.columnDef.meta.config || {nullLabels: []};
    const [nullOptionSelected, setNullOptionSelected] = useState('')
    const clearFiltersChangeEntity = useEntity(clearFiltersChange);

    useEffect(() => {
        setNullOptionSelected((checkFilterValue(columnFilterValue) ?? ''))
    }, [columnFilterValue]);

    useEffect(() => {
        if (clearFiltersChangeEntity != 0) {
            setNullOptionSelected((`${columnFilterValue}` ?? '') as string)
        }
    }, [clearFiltersChangeEntity]);

    const handleSelectChange = (event: any) => {
        setNullOptionSelected(event.target.value);
        if (event.target.value === 'notNull') {
            applyFilterColumn(null, {value: null, filterType: 'eq', notFilter: 'not'})
        } else if (event.target.value === null || event.target.value === 'null') {
            applyFilterColumn(null, {value: null, filterType: 'eq', notFilter: 'is'})
        } else {
            applyFilterColumn(event.target.value, {value: `${event.target.value}`, filterType: 'eq', notFilter: 'eq'})
        }
    };

    const getBackgroundFilter = () => {
        return nullOptionSelected && nullOptionSelected != '' && nullOptionSelected != 'NA_FILTER' && nullOptionSelected != 'undefined' ? '#DCF0F5' : 'white';
    }

    return (
        <div>
            <select
                className='shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline'
                style={{backgroundColor: getBackgroundFilter(), maxWidth: '300px'}}
                value={nullOptionSelected}
                onChange={handleSelectChange}>
                <option value="" disabled>
                    Select an option
                </option>
                <option value="NA_FILTER">All</option>
                <option value="null">{nullLabels.null}</option>
                <option value="notNull">{nullLabels.notNull}</option>
            </select>
        </div>
    );
}

export default NullableComponent;
