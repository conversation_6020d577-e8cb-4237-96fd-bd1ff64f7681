import React, {useState} from "react";
import {Box, Button, Grid, Input, Modal, Option, Select, Sheet, Typography} from "@mui/joy";
import {DateTime} from "luxon";
import CalendarMonthTwoToneIcon from "@mui/icons-material/CalendarMonthTwoTone";
import ModalClose from "@mui/joy/ModalClose";
import _ from "lodash";
import {generateWeeksForMonth} from "@/app/_components/table/functions/generateWeeksMonth";

export const NumberWeekSelectorComponent: React.FC<any> = ({
                                                               inputStyles,
                                                               onApplyFilters,
                                                               isLoading
                                                           }) => {
    const [showModal, setShowModal] = useState(false);
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
    const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
    const [weeks, setWeeks] = useState(generateWeeksForMonth(selectedYear, selectedMonth));
    const [selectedWeeks, setSelectedWeeks] = useState([]);
    const [messageInput, setMessageInput] = useState("Select Weeks");
    const years = _.range(2020, 2031);

    const handleWeekSelect = (weekObj: any) => {
        setSelectedWeeks((prev) => {
            const isAlreadySelected = prev.some((week) => week.weekNumber === weekObj.weekNumber);
            return isAlreadySelected
                ? prev.filter((week) => week.weekNumber !== weekObj.weekNumber)
                : [...prev, weekObj];
        });
    };

    const handleYearMonthChange = (year: any, month: any) => {
        setSelectedYear(year);
        setSelectedMonth(month);
        setWeeks(generateWeeksForMonth(year, month));
        setSelectedWeeks([]);
    };

    const toggleSelectAllWeeks = () => {
        if (selectedWeeks.length === weeks.length) {
            setSelectedWeeks([]);
        } else {
            setSelectedWeeks(weeks);
        }
    };

    const handleApplyFilters = () => {
        onApplyFilters(selectedWeeks);
        setMessageInput(selectedWeeks.length > 0 ? `${selectedWeeks.length} Weeks Selected` : "Select Weeks");
        setShowModal(false);
    };

    const findSelectedWeeks = (weekNumber: number) => {
        return selectedWeeks.some((week) => week.weekNumber === weekNumber);
    }

    return (
        <>
            <Input
                placeholder={messageInput}
                onClick={() => setShowModal(true)}
                readOnly
                disabled={isLoading}
                startDecorator={<CalendarMonthTwoToneIcon sx={{fontSize: '16px'}}/>}
                style={{
                    ...inputStyles,
                    backgroundColor: "transparent",
                    height: '23px !important',
                    cursor: 'pointer',
                }}
                slotProps={{
                    input: {
                        style: {
                            cursor: 'pointer',
                            fontSize: '12px',
                        },
                    },
                }}
            />
            <Modal
                aria-labelledby="modal-title"
                aria-describedby="modal-desc"
                open={showModal}
                onClose={() => setShowModal(false)}
                sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    overflowY: 'auto',
                    maxHeight: '80%',
                    marginTop: {
                        xs: '10vh',
                        sm: '10vh',
                        md: 0,
                        lg: 0
                    },
                    overflowX: 'hidden',
                }}>
                <Sheet
                    variant="outlined"
                    sx={{
                        maxWidth: 680,
                        borderRadius: 'md',
                        p: 3,
                        boxShadow: 'lg',
                        padding: '30px'
                    }}>
                    <ModalClose variant="plain" sx={{m: 1}}/>
                    <Box sx={{display: 'flex', justifyContent: 'space-around', width: '100%', alignItems: 'center'}}>
                        <div className="flex flex-col items-center justify-center mt-5 mb-5">
                            <Typography level="body-md">
                                <b>Year</b>
                            </Typography>
                            <Select
                                placeholder="Select Year"
                                value={selectedYear}
                                sx={{fontSize: '12px', mt: '5px'}}
                                slotProps={{listbox: {sx: {width: '100%'}}}}
                                onChange={(e, valueY) => setSelectedYear(valueY)}>
                                {years.map((year, index) => (
                                    <Option key={index} sx={{fontSize: '12px'}} value={year}>
                                        {year}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                        <div className="flex flex-col items-center justify-center mt-5 mb-5">
                            <Typography level="body-md">
                                <b>Month</b>
                            </Typography>
                            <Select
                                placeholder="Select Month"
                                value={selectedMonth}
                                sx={{fontSize: '12px', mt: '5px'}}
                                slotProps={{listbox: {sx: {width: '100%'}}}}
                                onChange={(e, valueM) => handleYearMonthChange(selectedYear, valueM)}>
                                {Array.from({length: 12}, (_, i) => (
                                    <Option key={i} sx={{fontSize: '12px'}} value={i + 1}>
                                        {DateTime.local(2024, i + 1).toFormat('MMMM')}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                    </Box>
                    <div className="flex flex-col items-center justify-center">
                        <Typography level="body-md">
                            <b>Weeks</b>
                        </Typography>
                        <Button onClick={toggleSelectAllWeeks} variant="soft" color="neutral" sx={{mt: '5px'}}>
                            {selectedWeeks.length === weeks.length ? 'Unselect All Weeks' : 'Select All Weeks'}
                        </Button>
                    </div>
                    <Grid container spacing={2} mt={1}>
                        {weeks.map((item: any) => (
                            <Grid xs={6} key={item?.weekNumber}>
                                <Button
                                    fullWidth
                                    variant={findSelectedWeeks(item?.weekNumber) ? 'solid' : 'outlined'}
                                    color="neutral"
                                    onClick={() => handleWeekSelect(item)}>
                                    <div className="flex flex-col justify-center">
                                        <Typography
                                            textColor={findSelectedWeeks(item?.weekNumber) ? 'white' : 'black'}
                                            level="body-sm" textAlign="center">
                                            Week {item?.weekNumber}
                                        </Typography>
                                        <Typography textColor={findSelectedWeeks(item?.weekNumber) ? 'white' : 'black'}
                                                    level="body-xs" textAlign="center">
                                            {item?.startView} to {item?.endView}
                                        </Typography>
                                    </div>
                                </Button>
                            </Grid>
                        ))}
                    </Grid>
                    <Box textAlign="right" mt={2}>
                        <Button variant="outlined" color="neutral" onClick={handleApplyFilters}>
                            Apply
                        </Button>
                    </Box>
                </Sheet>
            </Modal>
        </>
    );
}
