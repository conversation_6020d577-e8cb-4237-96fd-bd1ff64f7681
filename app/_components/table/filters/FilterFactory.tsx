import React from "react";
import TextFilterComponent from "@/app/_components/table/filters/TextFilterComponent";
import RangeNumberFilterComponent from "@/app/_components/table/filters/RangeNumberFilterComponent";
import DateFilterComponent from "@/app/_components/table/filters/DateFilterComponent";
import SelectFilterComponent from "@/app/_components/table/filters/SelectFilterComponent";
import _, {find} from "lodash";
import NullableComponent from "@/app/_components/table/filters/NullableComponent";
import MultiSelectFilterComponent from "@/app/_components/table/filters/MultiSelectFilterComponent";

interface FilterFactoryProps {
    component: string;
    applyFilterColumn: any
    filters: any,
    column: any,
    dbTableName: string
}

const FilterFactory: React.FC<FilterFactoryProps> = ({
                                                         component,
                                                         applyFilterColumn,
                                                         filters,
                                                         column,
                                                         dbTableName
                                                     }) => {
    const getFilterComponent: any = () => {
        let foundFilter: any;
        let foundFilterGte: any;
        let foundFilterLte: any;
        switch (component) {
            case 'TextFilterComponent':
                foundFilter = find(filters, (value, key: string) => key === `${column.columnDef.accessorKey}-match`);
                return <TextFilterComponent applyFilterColumn={applyFilterColumn}
                                            columnFilterValue={foundFilter?.value}
                                            column={column} dbTableName={dbTableName}/>;
            case 'RangeNumberFilterComponent':
                foundFilterGte = find(filters, (value, key: string) => key === `${column.columnDef.accessorKey}-gte`);
                foundFilterLte = find(filters, (value, key: string) => key === `${column.columnDef.accessorKey}-lte`);
                return <RangeNumberFilterComponent applyFilterColumn={applyFilterColumn}
                                                   columnFilterValue={{
                                                       gte: foundFilterGte?.value,
                                                       lte: foundFilterLte?.value
                                                   }}
                                                   column={column} dbTableName={dbTableName}/>
            case 'DateFilterComponent':
                foundFilter = find(filters, (value, key: string) => key === `${column.columnDef.accessorKey}-between`);
                return <DateFilterComponent applyFilterColumn={applyFilterColumn}
                                            columnFilterValue={foundFilter ? foundFilter.value : {start: '', end: ''}}
                                            column={column} dbTableName={dbTableName}/>

            case 'SelectFilterComponent':
                foundFilter = find(filters, (value, key: string) => key === `${column.columnDef.accessorKey}-eq`);
                return <SelectFilterComponent applyFilterColumn={applyFilterColumn}
                                              columnFilterValue={foundFilter?.value}
                                              column={column} dbTableName={dbTableName}/>

            case 'MultiSelectFilterComponent':
                foundFilter = _.find(filters, (value, key: string) => {
                    let accessorKey = '';
                    if (_.includes(column.columnDef.accessorKey, "_name") || _.includes(column.columnDef.accessorKey, "_names")) {
                        accessorKey = _.replace(
                            _.replace(column.columnDef.accessorKey, "_names", ""),
                            "_name",
                            ""
                        );
                    } else {
                        accessorKey = column.columnDef.accessorKey
                    }
                    return _.includes(key, accessorKey)
                });

                return <MultiSelectFilterComponent applyFilterColumn={applyFilterColumn}
                                                   columnFilterValue={foundFilter?.value}
                                                   column={column} dbTableName={dbTableName}/>
            case 'NullableComponent':
                foundFilter = find(filters, (value, key: string) => key === `${column.columnDef.accessorKey}-is`);
                return <NullableComponent applyFilterColumn={applyFilterColumn}
                                          columnFilterValue={foundFilter?.value}
                                          column={column} dbTableName={dbTableName}/>;
            default:
                foundFilter = find(filters, (value, key: string) => key === `${column.columnDef.accessorKey}-ilike`);
                return <TextFilterComponent applyFilterColumn={applyFilterColumn}
                                            columnFilterValue={foundFilter?.value}
                                            column={column} dbTableName={dbTableName}/>;
        }
    };

    return <>{getFilterComponent()}</>;

};

export default FilterFactory;
