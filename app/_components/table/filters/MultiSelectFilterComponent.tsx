import React, {useEffect, useState} from "react";
import {FilterComponentProps} from "@/app/_components/table/filters/func/filterFactoryProps.interface";
import {useEntity} from "simpler-state";
import {clearFiltersChange} from "@/app/_components/table/states/clearFiltersState";
import {IconButton, Option, Select} from "@mui/joy";
import CloseRounded from "@mui/icons-material/CloseRounded";
import {checkArray} from "@/app/_components/table/functions/checkArray";
import {sortByNameSelectElements} from "@/app/_components/table/functions/sortByNameSelectElements";
import _ from "lodash";
import {checkFilterValue} from "@/app/_components/table/filters/func/checkFilterValue";

const MultiSelectFilterComponent: React.FC<FilterComponentProps> = ({
                                                                        columnFilterValue,
                                                                        applyFilterColumn,
                                                                        column,
                                                                        dbTableName,
                                                                    }) => {
    let {selectOptions} = column.columnDef.meta.config || {selectOptions: []};
    let selectConfig = column.columnDef.meta.config;
    const getIdNullToFind = selectConfig?.idNull ?? null;
    const checkSelOpts = checkArray(selectOptions)
    const [selects, setSelects] = useState(sortByNameSelectElements(checkSelOpts))
    const [optionsSel, setOptionsSel] = useState([])
    const clearFiltersChangeEntity = useEntity(clearFiltersChange);

    useEffect(() => {
        setOptionsSel(checkFilterValue(columnFilterValue) || [])
    }, [columnFilterValue]);

    useEffect(() => {
        if (clearFiltersChangeEntity != 0) {
            setOptionsSel([]);
        }
    }, [clearFiltersChangeEntity]);

    const handleChange = (
        event: React.SyntheticEvent | null,
        valuesSel: Array<string> | null,
    ) => {
        if (_.includes(valuesSel, "NA_FILTER")) {
            setOptionsSel([]);
            applyFilterColumn("NA_FILTER", {value: "NA_FILTER", filterType: "inF"});
        } else if (valuesSel.length === 0) {
            setOptionsSel([]);
            applyFilterColumn([], {value: "", filterType: "inF"});
        } else {
            setOptionsSel(valuesSel);
        }
    };

    const onClose = () => {
        changeValue();
    };

    const changeValue = () => {
        if (optionsSel && optionsSel.length > 0) {
            if (_.includes(optionsSel, "NA_FILTER")) {
                setOptionsSel([]);
                applyFilterColumn("NA_FILTER", {
                    value: "NA_FILTER",
                    filterType: "inF",
                });
            } else {
                setOptionsSel(optionsSel);
                if (_.includes(optionsSel, getIdNullToFind))
                    optionsSel.push("", null);
                const {customFilters, selectFilters} = buildFilters(optionsSel);
                if (customFilters.length === 0) {
                    applyFilterColumn(selectFilters, {
                        value: optionsSel && optionsSel.length < 0 ? "" : optionsSel,
                        filterType: "inF",
                    });
                }
                if (customFilters && customFilters.length > 0) {
                    if (selectFilters.length < 1) {
                        customFilters.forEach((customFilter) => {
                            const customFilterSplit = _.split(customFilter, "-");
                            const value = customFilterSplit[2];
                            applyFilterColumn(value, {
                                value: value,
                                filterType: customFilterSplit[1],
                            });
                        });
                    } else {
                        applyFilterColumn(selectFilters, {
                            value: selectFilters,
                            filterType: "multiTag",
                        });
                    }
                }
            }
        }
    };

    const buildFilters = (optionsSel: any) => {
        const {customFilters, selectFilters} = _.reduce(
            optionsSel,
            (acc, item) => {
                if (_.includes(item, "CUSTOM_FILTERS")) {
                    acc.customFilters.push(item);
                } else {
                    acc.selectFilters.push(item);
                }
                return acc;
            },
            {customFilters: [], selectFilters: []},
        );
        return {customFilters, selectFilters};
    };
    const getBackgroundFilter = () => {
        return optionsSel && optionsSel.length > 0 ? "#DCF0F5" : "white";
    };

    const selectCustom = () => {
    };

    const getDefaultValue = () => {
        return optionsSel;
    }

    return (
        <div>
            <Select
                value={getDefaultValue()}
                multiple
                placeholder="Select multiple options"
                onChange={handleChange}
                onClose={onClose}
                {...(optionsSel && optionsSel.length > 0 && {
                    endDecorator: (
                        <IconButton
                            size="sm"
                            variant="plain"
                            color="neutral"
                            onClick={() => {
                                handleChange(null, []);
                            }}
                        >
                            <CloseRounded/>
                        </IconButton>
                    ),
                    indicator: null,
                })}
                renderValue={(sel) => <span>{sel.length} option selected</span>}
                sx={{
                    backgroundColor: getBackgroundFilter(),
                    maxWidth: "300px",
                    height: "33px",
                    fontSize: "12px",
                }}
                slotProps={{
                    listbox: {sx: {width: "100%"}},
                }}
            >
                <Option sx={{fontSize: "12px"}} value="NA_FILTER">
                    All
                </Option>
                {selects && selects.length > 1 && selectConfig.showNull && (
                    <Option sx={{fontSize: "12px"}} value="NULL">
                        Null
                    </Option>
                )}
                {selectConfig &&
                    selectConfig.customSelects &&
                    selectConfig.customSelects.map((item: any, index: number) => (
                        <Option
                            key={`custom-${index}`}
                            sx={{fontSize: "12px"}}
                            onClick={selectCustom}
                            value={`CUSTOM_FILTERS-${item.filterType}-${item.value}`}
                        >
                            {item.label}
                        </Option>
                    ))}
                {selects &&
                    selects.map((itm: any, index: number) => (
                        <Option key={index} sx={{fontSize: "12px"}} value={itm.value}>
                            {itm.label}
                        </Option>
                    ))}
            </Select>
        </div>
    );
};

export default MultiSelectFilterComponent;
