import React, {useEffect, useState} from "react";
import {FilterComponentProps} from "@/app/_components/table/filters/func/filterFactoryProps.interface";
import {DateRangeComponent} from "@/app/_components/table/filters/DateRangeComponent";
import {useEntity} from "simpler-state";
import {clearFiltersChange} from "@/app/_components/table/states/clearFiltersState";
import {checkFilterValue} from "@/app/_components/table/filters/func/checkFilterValue";

const DateFilterComponent: React.FC<FilterComponentProps> = ({
                                                                 column,
                                                                 dbTableName,
                                                                 applyFilterColumn,
                                                                 columnFilterValue
                                                             }) => {
    const [rangeDates, setRangeDates]
        = useState({startDate: '', endDate: ''})
    const clearFiltersChangeEntity = useEntity(clearFiltersChange);

    useEffect(() => {
        setRangeDates(columnFilterValue.end && columnFilterValue.start ? {
            startDate: checkFilterValue(columnFilterValue.start),
            endDate: checkFilterValue(columnFilterValue.end)
        } : {startDate: '', endDate: ''});
    }, [columnFilterValue]);

    useEffect(() => {
        if (clearFiltersChangeEntity != 0) {
            setRangeDates(columnFilterValue.end && columnFilterValue.start
                ? {startDate: columnFilterValue.start, endDate: columnFilterValue.end}
                : {startDate: '', endDate: ''})
        }
    }, [clearFiltersChangeEntity]);

    const applyDateFilter = (event: any) => {
        if (event && event.startDate && event.startDate != '' && event.endDate && event.endDate != '') {
            applyFilterColumn(event, {
                value: {
                    start: `${event.startDate}`,
                    end: `${event.endDate}`
                },
                filterType: 'between'
            })
            setRangeDates(event)
        }
    }

    return (<>
        <DateRangeComponent defaultValues={rangeDates} onApplyFilters={applyDateFilter}/>
    </>)
}

export default DateFilterComponent;
