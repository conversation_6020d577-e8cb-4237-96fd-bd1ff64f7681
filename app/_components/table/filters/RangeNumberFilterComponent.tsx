import React, {useEffect, useState} from "react";
import {FilterComponentProps} from "@/app/_components/table/filters/func/filterFactoryProps.interface";
import {useEntity} from "simpler-state";
import {clearFiltersChange} from "@/app/_components/table/states/clearFiltersState";

const RangeNumberFilterComponent: React.FC<FilterComponentProps> = ({column, applyFilterColumn, columnFilterValue}) => {
    const [gte, setGte] = useState(-1)
    const [lte, setLte] = useState(-1)
    const [changed, setChanged] = useState(false)
    const clearFiltersChangeEntity = useEntity(clearFiltersChange);

    useEffect(() => {
        if(clearFiltersChangeEntity!=0){
        setGte(-1)
        setLte(-1)
        }
    }, [clearFiltersChangeEntity]);

    const onChangeValueLte = (e: any) => {
        setLte(Number(e.target.value))
        setChanged(true);
    }

    const onChangeValueGte = (e: any) => {
        setGte(Number(e.target.value))
        setChanged(true);
    }

    const onKeyUp = (event: any) => {
        if (event.key === 'Enter' && changed) {
            applyFilters(event.target.name)
            setChanged(false)
        }
    }

    const onBlur = (event: any) => {
        if (changed) {
            applyFilters(event.target.name)
            setChanged(false);
        }
    }


    const applyFilters = (inputName: any) => {
        if (inputName === 'gte') {
            if (gte != -1) {
                applyFilterColumn(gte, {value: gte, filterType: 'gte'})
            } else {
                applyFilterColumn(gte, {value: 'NA_FILTER', filterType: 'gte'})
            }
        }
        if (inputName === 'lte') {
            if (lte != 0 && lte != -1) {
                applyFilterColumn(lte, {value: lte, filterType: 'lte'})
            } else {
                applyFilterColumn(lte, {value: 'NA_FILTER', filterType: 'lte'})
            }
        }
    }

    return (<>
        <div className="flex space-x-2">
            <input
                type="number"
                name="gte"
                value={gte != -1 ? gte : ''}
                style={{backgroundColor: gte && gte != -1 ? '#DCF0F5' : 'white', maxWidth: '300px'}}
                onChange={e => onChangeValueGte(e)}
                placeholder={`Greater than`}
                onKeyUp={onKeyUp}
                onBlur={onBlur}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            />
            <input
                type="number"
                name="lte"
                style={{backgroundColor: lte && lte != -1 ? '#DCF0F5' : 'white', maxWidth: '300px'}}
                value={lte != -1 ? lte : ''}
                onChange={e => onChangeValueLte(e)}
                placeholder={`Less than`}
                onKeyUp={onKeyUp}
                onBlur={onBlur}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            />
        </div>
    </>)
}

export default RangeNumberFilterComponent;
