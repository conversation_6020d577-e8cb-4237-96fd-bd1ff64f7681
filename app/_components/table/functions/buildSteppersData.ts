import _ from "lodash";
import {styledName} from "@/app/_components/utils/styledName";
import {camelCaseToStylized} from "@/app/_components/utils/camelCaseToStylized";

export const buildSteppersData = (rulesJson: any) => {
    const returnStepperData = []

    if (!_.isEmpty(rulesJson)) {
        const getArrayReqKeys = _.get(rulesJson, "requiredKeys")
        const objReqKeys = {
            title: "Validation keys",
            subTitle: "Checking if the CSV file has the following keys: ",
            data: []
        }
        const getMapConf = rulesJson?.mappingConfiguration ?? {}
        if (!_.isEmpty(getMapConf)) {
            const objMapData = {
                title: "Mapping data",
                subTitle: "Renaming keys and transforming them with a certain type.",
                data: []
            }
            getArrayReqKeys.forEach((keyName: string) => {
                if (!_.isEmpty(getMapConf[keyName])) {
                    const objKeyRequired = {
                        title: keyName
                    }
                    objReqKeys.data.push(objKeyRequired)
                    const getDataMapKey = getMapConf[keyName]
                    const objMapKey = {
                        title: `Key: ${keyName}`,
                        description: `Rename: ${styledName(getDataMapKey?.output)}`,
                        description2: `Type: ${styledName(getDataMapKey?.transformer)}.`
                    }
                    objMapData.data.push(objMapKey)
                }
            })
            returnStepperData.push(objReqKeys)
            returnStepperData.push(objMapData)
        }

        const getCompleteData = rulesJson?.completeDataMethods ?? []
        if (!_.isEmpty(getCompleteData)) {
            const objStepsComData = {
                title: `Looking for related transfer or lead to add information`,
                subTitle: "The purpose of this process is to complete the information by comparing the phone with other fields from other data collections.",
                data: [
                    {
                        title: "If they match, the corresponding information is automatically populated with information that is subtracted from the transfer or lead, and a tag is attached to it.",
                        description: "In addition to this process, information about the vendor and what was found is also incorporated.",
                    }
                ]
            }
            returnStepperData.push(objStepsComData)
        }

        const getPreSaveMeth = rulesJson?.preConfigurationMethods ?? []
        if (!_.isEmpty(getPreSaveMeth)) {
            const objPreSave = {
                title: "Rechecking data before save",
                subTitle: "Before saving the data, it is checked with other methods to validate or add additional information, like:",
                data: []
            }
            getPreSaveMeth.forEach((nameMet: string) => {
                const objReqKeys = {
                    title: `${camelCaseToStylized(nameMet)} Method`,
                }
                objPreSave.data.push(objReqKeys)
            })
            returnStepperData.push(objPreSave)
        }
        const finalStep = {
            title: "Save client data",
            subTitle: "Saving the transformed data in the database",
            data: [
                {
                    title: rulesJson?.beforeSave,
                }
            ]
        }
        returnStepperData.push(finalStep)

        return {title: rulesJson?.title, data: returnStepperData}
    } else {
        return null
    }
}
