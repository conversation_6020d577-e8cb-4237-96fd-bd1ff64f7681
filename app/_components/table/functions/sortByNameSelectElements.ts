import _ from "lodash";

export const sortByNameSelectElements = (items: any) => {
    if (items.length <= 0) return []
    return items.sort((a: any, b: any) => {
        if (_.isString(a?.label) && _.isString(b?.label)) {
            const labelA = a?.label.toLowerCase();
            const labelB = b?.label.toLowerCase();
            if (labelA < labelB) {
                return -1;
            }
            if (labelA > labelB) {
                return 1;
            }
            return 0;
        } else {
            return 0;
        }
    });
}