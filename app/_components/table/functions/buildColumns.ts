import {TableCell} from "@/app/_components/table/TableCell";
import {createColumnHelper} from "@tanstack/react-table";
import _ from "lodash";

const columnHelper = createColumnHelper<any>();
export const buildColumns = (arrayColumns: any) => {
    const columns = arrayColumns.map((column: any) => {
        return columnHelper.accessor(column.accessorKey, {
            header: column.header,
            cell: TableCell,
            enableColumnFilter: false,
            meta: {filterComponent: column.filterComponent, config: column.config}
        });
    });

    const rowsConfigurations = _.chain(arrayColumns)
        .keyBy('accessorKey')
        .mapValues(({header, config}) => ({
            label: header,
            show: _.get(config, 'show', true)
        }))
        .value();
    return {columns, rowsConfigurations}
}

export const buildRowsConfigurations = (rowsConfigurations: any) => {
    return _.mapValues(rowsConfigurations, item => item.show);
}

export const hasDifferentKeys = (object1, object2) => {
    const keys1 = _.keys(object1);
    const keys2 = _.keys(object2);
    const difference1 = _.difference(keys1, keys2);
    const difference2 = _.difference(keys2, keys1);
    return difference1.length > 0 || difference2.length > 0;
}
