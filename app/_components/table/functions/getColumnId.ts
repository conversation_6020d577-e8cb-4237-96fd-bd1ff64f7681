export const getColumnId = (column: any): any => {
    const {filterComponent, config = {}} = column.columnDef.meta;
    if ((filterComponent === "SelectFilterComponent" || filterComponent === "MultiSelectFilterComponent" ) && config.select === 'external') {
        if (config.localFilterId && config.localFilterId !== '') {
            return config.localFilterId;
        } else {
            console.error(`filterId for external select not found..`);
            throw new Error(`filterId for external select not found..`);
        }
    } else {
        return column.id;
    }
};
