import {DateTime} from "luxon";

export const generateWeeksForMonth = (year: any, month: any) => {
    const startDate = DateTime.local(year, month, 1);
    const endDate = startDate.endOf('month');
    const weeks = [];

    let currentStart = startDate.startOf('week');

    while (currentStart <= endDate) {
        // const currentEnd = currentStart.endOf('week') <= endDate ? currentStart.endOf('week') : endDate;
        const currentEnd = currentStart.endOf('week', {});

        weeks.push({
            weekNumber: currentStart.weekNumber,
            startView: currentStart.toFormat('MMM-dd'),
            endView: currentEnd.toFormat('MMM-dd'),
            startDate: currentStart.toFormat('yyyy-MM-dd'),
            endDate: currentEnd.toFormat('yyyy-MM-dd')
        });

        currentStart = currentEnd.plus({days: 1});
    }

    return weeks;
    // const startDate = DateTime.fromObject({ year, month, day: 1 }).startOf('month');
    // const endDate = startDate.endOf('month');
    //
    // let currentStart = startDate;
    // const weeks = [];
    //
    // while (currentStart <= endDate) {
    //     const currentEnd = currentStart.plus({ days: 6 }) <= endDate ? currentStart.plus({ days: 6 }) : endDate;
    //
    //     weeks.push({
    //         weekNumber: currentStart.weekNumber,
    //         startView: currentStart.toFormat('MMM-dd'),
    //         endView: currentEnd.toFormat('MMM-dd'),
    //         startDate: currentStart.toFormat('yyyy-MM-dd'),
    //         endDate: currentEnd.toFormat('yyyy-MM-dd')
    //     });
    //
    //     currentStart = currentEnd.plus({days: 1});
    // }
    //
    // return weeks;
};