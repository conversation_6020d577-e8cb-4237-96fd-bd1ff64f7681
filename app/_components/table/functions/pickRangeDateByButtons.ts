import {DateTime} from "luxon";

export const pickRangeDateByButtons = (filter: any) => {
    switch (filter) {
        case 'today':
            const todayStart = DateTime.now().startOf('day').toJSDate();
            const todayEnd = DateTime.now().endOf('day').toJSDate();
            return [{startDate: todayStart, endDate: todayEnd, key: "selection"}];
        case 'yesterday':
            const yesterdayStart = DateTime.now().minus({days: 1}).startOf('day').toJSDate();
            const yesterdayEnd = DateTime.now().minus({days: 1}).endOf('day').toJSDate();
            return [{startDate: yesterdayStart, endDate: yesterdayEnd, key: "selection"}];
        case 'lastWeek':
            const lastWeekStartDate = DateTime.now().minus({weeks: 1}).startOf('week').toJSDate();
            const lastWeekEndDate = DateTime.now().startOf('week').minus({days: 1}).toJSDate();
            return [{startDate: lastWeekStartDate, endDate: lastWeekEndDate, key: "selection"}];
        case 'thisMonth':
            const thisMonthStartDate = DateTime.now().startOf('month').toJSDate();
            const thisMonthEndDate = DateTime.now().toJSDate();
            return [{startDate: thisMonthStartDate, endDate: thisMonthEndDate, key: "selection"}];
        case 'last1Month':
            const lastMonthStartDate = DateTime.now().minus({months: 1}).startOf('month').toJSDate();
            const lastMonthEndDate = DateTime.now().minus({months: 1}).endOf('month').toJSDate();
            return [{startDate: lastMonthStartDate, endDate: lastMonthEndDate, key: "selection"}];
        case 'last2Month':
            const last2MonthStartDate = DateTime.now().minus({months: 2}).startOf('month').toJSDate();
            const last2MonthEndDate = DateTime.now().minus({months: 2}).endOf('month').toJSDate();
            return [{startDate: last2MonthStartDate, endDate: last2MonthEndDate, key: "selection"}];
        case 'last3Month':
            const last3MonthStartDate = DateTime.now().minus({months: 3}).startOf('month').toJSDate();
            const last3MonthEndDate = DateTime.now().minus({months: 3}).endOf('month').toJSDate();
            return [{startDate: last3MonthStartDate, endDate: last3MonthEndDate, key: "selection"}];
        case 'last4Month':
            const last4MonthStartDate = DateTime.now().minus({months: 4}).startOf('month').toJSDate();
            const last4MonthEndDate = DateTime.now().minus({months: 4}).endOf('month').toJSDate();
            return [{startDate: last4MonthStartDate, endDate: last4MonthEndDate, key: "selection"}]
        case 'last5Month':
            const last5MonthStartDate = DateTime.now().minus({months: 5}).startOf('month').toJSDate();
            const last5MonthEndDate = DateTime.now().minus({months: 5}).endOf('month').toJSDate();
            return [{startDate: last5MonthStartDate, endDate: last5MonthEndDate, key: "selection"}]
        case 'last6Month':
            const last6MonthStartDate = DateTime.now().minus({months: 6}).startOf('month').toJSDate();
            const last6MonthEndDate = DateTime.now().minus({months: 6}).endOf('month').toJSDate();
            return [{startDate: last6MonthStartDate, endDate: last6MonthEndDate, key: "selection"}]
        default:
            const defaultDate = DateTime.now().toJSDate();
            return [{startDate: defaultDate, endDate: defaultDate, key: "selection"}];
    }
}