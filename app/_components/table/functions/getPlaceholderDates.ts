import {DateTime} from "luxon";

export const getPlaceholderDates = (startDate: string, endDate: string) => {
    const dateTimeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/;
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (dateTimeRegex.test(endDate) && dateTimeRegex.test(startDate)) {
        return `${DateTime.fromFormat(startDate, 'yyyy-MM-dd HH:mm').toFormat('DD')} to ${DateTime.fromFormat(endDate, 'yyyy-MM-dd HH:mm').toFormat('DD')}`;
    } else if (dateRegex.test(endDate) && dateRegex.test(startDate)) {
        const startDateFormat = DateTime.fromFormat(startDate, 'yyyy-MM-dd').toFormat('DD')
        const endDateFormat = DateTime.fromFormat(endDate, 'yyyy-MM-dd').toFormat('DD')
        return `${startDateFormat} to ${endDateFormat}`;
    } else {
        return 'Range Date';
    }
};