import React, {useEffect, useState} from "react";
import {Box, Button, Table, Typography} from "@mui/joy";
import Sheet from "@mui/joy/Sheet";
import ModalClose from "@mui/joy/ModalClose";
import Modal from "@mui/joy/Modal";

export const ObjectDataComponent: React.FC<any> = ({
                                                       initialValue,
                                                       onUpdate,
                                                       configuration,
                                                       tagsData,
                                                       columnName,
                                                       dbTableName
                                                   }) => {
    const [value, setValue] = useState<any>(initialValue || {});
    const [showModal, setShowModal] = useState(false);
    const [tagsMap, setTagsMap] = useState<{ [key: string]: string }>({});

    useEffect(() => {
        if (tagsData) {
            const map = tagsData.reduce((acc: { [key: string]: string }, tag: any) => {
                acc[tag._id] = tag.name;
                return acc;
            }, {});
            setTagsMap(map);
        }
    }, []);

    useEffect(() => {
        setValue(initialValue);
    }, [initialValue]);

    useEffect(() => {
        if (columnName !== 'pub_ids') {
            if (Array.isArray(value) && value.length > 0 && tagsMap) {
                const transformedValue = value.map((item) => ({
                    ...item,
                    tags: item.tags.map((id: string) => tagsMap[id] || id)
                }));
                setValue(transformedValue);
            }
        }
    }, [tagsMap]);

    const openModal = () => {
        setShowModal(true);
    }

    const flattenObject = (obj: any, prefix = '') => {
        let result = [];
        for (const key in obj) {
            const value = obj[key];
            const newKey = prefix ? `${prefix}.${key}` : key;
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                result = result.concat(flattenObject(value, newKey));
            } else {
                result.push({key: newKey, value});
            }
        }
        return result;
    };

    return (
        <>
            <Button variant="outlined" color="neutral" onClick={openModal}>
                {configuration?.linkLabel || 'View'}
            </Button>
            <Modal
                aria-labelledby="modal-title"
                aria-describedby="modal-desc"
                open={showModal}
                onClose={() => setShowModal(false)}
                sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
                <Sheet
                    variant="outlined"
                    sx={{
                        maxWidth: 600,
                        width: '100%',
                        borderRadius: 'md',
                        p: 5,
                        boxShadow: 'lg',
                    }}>
                    <ModalClose onClick={() => setShowModal(false)} variant="plain" sx={{m: 1}}/>
                    <Box sx={{maxHeight: '400px', overflowY: 'auto'}}>
                        {value ? (
                            dbTableName === 'campaigns' && typeof value === 'object' ? (
                                (() => {
                                    const flattenedData = flattenObject(value);
                                    return (
                                        <Table>
                                            <thead>
                                            <tr>
                                                <th><strong>Campo</strong></th>
                                                <th><strong>Valor</strong></th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            {flattenedData.map(({key, value}, index) => (
                                                <tr key={index}>
                                                    <td>{key}</td>
                                                    <td>{JSON.stringify(value)}</td>
                                                </tr>
                                            ))}
                                            </tbody>
                                        </Table>
                                    );
                                })()
                            ) : Array.isArray(value) && value.length > 0 ? (
                                <Table>
                                    <thead>
                                    <tr>
                                        {dbTableName === 'vendors' ? (
                                            <>
                                                <th><strong>PUB ID</strong></th>
                                                <th><strong>Lead Strategy</strong></th>
                                            </>
                                        ) : (
                                            <>
                                                <th><strong>Number</strong></th>
                                                <th><strong>Tags Name</strong></th>
                                            </>
                                        )}
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {value.map((item, index) => (
                                        <tr key={index}>
                                            {dbTableName === 'vendors' ? (
                                                <>
                                                    <td>{item.id}</td>
                                                    <td>{item.leadStrategy}</td>
                                                </>
                                            ) : (
                                                <>
                                                    <td>{item.number}</td>
                                                    <td>{Array.isArray(item.tagsName) ? item.tagsName.join(', ') : ''}</td>
                                                </>
                                            )}
                                        </tr>
                                    ))}
                                    </tbody>
                                </Table>
                            ) : (
                                <Typography level="body-md">No items to display</Typography>
                            )
                        ) : (
                            <Typography level="body-md">No items to display</Typography>
                        )}
                    </Box>
                </Sheet>
            </Modal>
        </>
    );
};
