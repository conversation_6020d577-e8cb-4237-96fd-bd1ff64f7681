import React, { useState, useEffect } from "react";
import { Button, Modal, Modal<PERSON>ialog, Typography, Input } from "@mui/joy";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { showError, showSuccess } from "@/app/_components/alerts/toast/ToastMessages";

export const UpdateConcurrentCalls: React.FC<any> = ({
    row,
    tableColumns,
    dbTableName,
}) => {
    const [open, setOpen] = useState(false);
    const [concurrentCallsLimit, setConcurrentCallsLimit] = useState(0);
    const [loading, setLoading] = useState(false);
    const queryClient = useQueryClient();
    useEffect(() => {
        if (row.original?.nobelBiz?.concurrentCallsLimit !== undefined) {
            setConcurrentCallsLimit(row.original.nobelBiz.concurrentCallsLimit);
        }
    }, [row]);

    const updateData = async (concurrentCallsLimit: number) => {
        setLoading(true);
        try {
            const payload = {
                collection: dbTableName,
                data: {
                    ...row.original,
                    nobelBiz: {
                        ...row.original.nobelBiz,
                        concurrentCallsLimit: concurrentCallsLimit,
                    },
                },
                id: row.original._id,
            };

            const result = await fetch("/api/mongo/updateAllDataById", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(payload),
            });

            if (result.status === 200) {
                showSuccess("Updated successfully");
            } else {
                const response = await result.json();
                showError(response.error || "We had a problem updating");
            }
        } catch (error) {
            console.error("Error updating data:", error);
            showError("Error updating data");
        } finally {
            setLoading(false);
        }
    };

    const mutation = useMutation({
        mutationFn: updateData,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [dbTableName] });
        },
    });

    const handleOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    const handleSave = () => {
        mutation.mutate(concurrentCallsLimit);
        handleClose();
    };

    return (
        <>
            <Button variant="outlined" color="neutral" onClick={handleOpen}>
                {concurrentCallsLimit}
            </Button>

            <Modal open={open} onClose={handleClose}>
                <ModalDialog>
                    <Typography mb={2}>
                        Update Concurrent Calls Limit
                    </Typography>
                    <Input
                        type="number"
                        value={concurrentCallsLimit}
                        onChange={(e) => setConcurrentCallsLimit(Number(e.target.value))}
                        fullWidth
                        sx={{ mt: 2 }}
                    />
                    <Button
                        variant="solid"
                        color="primary"
                        onClick={handleSave}
                        sx={{ mt: 2 }}
                        disabled={loading}
                    >
                        {loading ? "Updating..." : "Update"}
                    </Button>
                </ModalDialog>
            </Modal>
        </>
    );
};