import React, {useState} from "react";
import {Button} from "@mui/joy";
import {UpdateTableRow} from "@/app/_components/table/update/UpdateTableRow";
import {UpdateCollectionTableRow} from "@/app/_components/table/update/UpdateCollectionTableRow";
import {useRouter} from "next/navigation";
import {setLoading} from "@/app/_components/Loading/loadingState";
import {DeleteRowComponent} from "@/app/_components/table/dataComponents/DeleteRowComponent";
import {useQuery} from "@tanstack/react-query";
import {Tags} from "@/app/_components/queries/Tags";

export const ActionsDataComponent: React.FC<any> = ({
                                                        row,
                                                        tableColumns,
                                                        dbTableName,
                                                        fullTableConfiguration,
                                                        onDeleteRow
                                                    }) => {
    const router = useRouter();
    const [showModal, setShowModal] = useState(false);
    function openModal() {
        setShowModal(true);
    }

    function closeModal() {
        setShowModal(false);
    }

    const handleRedirect = () => {
        setLoading(true)
        router.push(`/call-center/script/${row?.original?._id}/0`);
    };

    const {data: tagsData} = useQuery(Tags(dbTableName, (fullTableConfiguration && !fullTableConfiguration?.editable_data), true));

    const getViewActions = () => {
        switch (dbTableName) {
            case 'campaigns':
                return <Button
                    variant="outlined"
                    color="neutral"
                    onClick={handleRedirect}>
                    Edit Script
                </Button>
            default:
                return <>
                    <Button variant="outlined" color="neutral" onClick={openModal}>Edit row</Button>
                    {showModal &&
                        <UpdateTableRow
                            row={row}
                            tagsData={tagsData}
                            dbOptionsColumns={tableColumns}
                            showModal={showModal}
                            onClose={closeModal}
                            dbTableName={dbTableName}
                        />}
                </>
        }
    }
    return (
        <>
            {(fullTableConfiguration
                && fullTableConfiguration.rjs_schema
                && fullTableConfiguration.ui_schema
                && fullTableConfiguration?.editable_data === true) ?
                <>
                    <Button variant="outlined" color="neutral" onClick={openModal} disabled={row?.original.lock}>
                        Edit row
                    </Button>
                    {showModal &&
                        <UpdateCollectionTableRow
                            row={row}
                            dbOptionsColumns={tableColumns}
                            showModal={showModal}
                            onClose={closeModal}
                            dbTableName={dbTableName}
                        />}
                </> : getViewActions()}
            {fullTableConfiguration && fullTableConfiguration?.delete_data === true &&
                <DeleteRowComponent dbTableName={dbTableName} row={row} onDeleteRow={onDeleteRow}
                                    fullTableConfiguration={fullTableConfiguration}/>}

        </>);
}
