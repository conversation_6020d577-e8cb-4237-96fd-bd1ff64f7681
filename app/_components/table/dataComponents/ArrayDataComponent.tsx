'use client'
import React, {useEffect, useState} from "react";
import {Box, Button} from "@mui/joy";
import Sheet from "@mui/joy/Sheet";
import ModalClose from "@mui/joy/ModalClose";
import Modal from "@mui/joy/Modal";
import Typography from "@mui/joy/Typography";
import _ from "lodash";
import JsonView from "react18-json-view";

export const ArrayDataComponent: React.FC<any> = ({
                                                      initialValue,
                                                      onUpdate,
                                                      configuration,
                                                      columnName
                                                  }) => {
    const [value, setValue] = useState(initialValue || '');
    const [showModal, setShowModal] = useState(false);
    const styles = {
        color: '#008eff',
        cursor: 'pointer',
        fontSize: '12px',
        fontWeight: 'bold',
        textDecoration: 'underline'
    }
    useEffect(() => {
        setValue(initialValue);
    }, [initialValue]);


    function openModal() {
        setShowModal(true)
    }

    return (
        <>
            <Button variant={"outlined"} color={"neutral"} onClick={openModal}>{configuration?.linkLabel}</Button>
            <Modal
                aria-labelledby="modal-title"
                aria-describedby="modal-desc"
                open={showModal}
                onClose={() => setShowModal(false)}
                sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}
            >
                <Sheet
                    variant="outlined"
                    sx={{
                        maxWidth: 600,
                        width: '100%',
                        borderRadius: 'md',
                        p: 3,
                        boxShadow: 'lg',
                    }}
                >
                    <ModalClose onClick={() => setShowModal(false)} variant="plain" sx={{m: 1}}/>
                    <Box sx={{maxHeight: '400px', overflowY: 'auto'}}>
                        {Array.isArray(value) && value.length > 0 ? (
                            value.map((item: any, index) => (
                                <Box  key={`array-${columnName}-${index}`}>
                                    {_.isObject(item)
                                        ? <JsonView src={item} theme="github" enableClipboard={true}
                                                    collapseStringsAfterLength={150}/>
                                        : <Typography level="body-md">{item}</Typography>}
                                </Box>
                            ))
                        ) : (
                            <Typography level="body-md">No items to display</Typography>
                        )}
                    </Box>
                </Sheet>
            </Modal>
        </>
    );
};
