'use client'
import React from "react";
import Chip from '@mui/joy/Chip'
import Box from "@mui/joy/Box";

export const TagsComponent: React.FC<any> = ({
                                                 initialValue,
                                                 onUpdate,
                                                 configuration,
                                                 columnName
                                             }) => {

    return (
        <>
            <Box>
                {initialValue && initialValue.length > 0 && initialValue.map((item, index) => (
                    <Chip color={"primary"} key={`${item}-${index}`} variant="soft" sx={{marginRight: '5px'}}>
                        {item}
                    </Chip>
                ))}
            </Box>
        </>
    );
};
