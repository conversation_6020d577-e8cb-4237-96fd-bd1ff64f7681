'use client'
import React, {useEffect, useState} from "react";
import {DateTime} from "luxon";
import {useSession} from "@/app/_lib/auth/auth-client";

export const TextComponent: React.FC<any> = ({
                                                 initialValue,
                                                 onUpdate,
                                                 configuration,
                                                 columnName
                                             }) => {
    const {data: session, isPending} = useSession()
    const [value, setValue] = useState(initialValue || '');
    const [timezone, setTimezone] = useState(initialValue || '');


    useEffect(() => {
        const {user}: any = session || {user: null};
        setValue(initialValue);
        setTimezone((user) ? user?.clientReadOnlyMetadata?.timezone : "America/Chicago")
    }, [initialValue]);

    const onBlur = async () => {
        if (initialValue != value) {
            const payload = {[columnName]: value};
            onUpdate(payload)
        }
    };

    const displayValue = (value: any): any => {
        if (configuration?.date && value) {
            const originalDate = DateTime.fromISO(value, {zone: timezone});
            return `${originalDate.toFormat(configuration.date + " ZZZZ")}`;
        } else {
            return `${value != undefined ? value : ''}`
        }
    }

    return (
        <>
            <span>{displayValue(value)}</span>
        </>
    );
};
