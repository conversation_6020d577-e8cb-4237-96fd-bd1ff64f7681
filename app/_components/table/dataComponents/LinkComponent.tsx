import {Box, CircularProgress, Link} from "@mui/joy";
import React from "react";
import {
    linkDetailValue,
    setLinkDetailChange,
    setLinkDetailValue
} from "@/app/_components/table/states/linkDetailChangeState";
import {useEntity} from "simpler-state";
import Modal from '@mui/joy/Modal';
import ModalClose from '@mui/joy/ModalClose';
import Sheet from '@mui/joy/Sheet';
import Typography from "@mui/joy/Typography";
import JsonFormatter from 'react-json-formatter'

export const LinkComponent: React.FC<any> = ({value, configuration}) => {
    const linkDetailValueEntity = useEntity(linkDetailValue)
    const [open, setOpen] = React.useState<boolean>(false);

    const goTo = () => {
        setLinkDetailChange();
        setLinkDetailValue({
            ...linkDetailValueEntity,
            linkConfiguration: configuration.link,
            linkValue: value,
            linkJson: null
        })
        setOpen(true)
    }
    return (<>
        <Box>
            <Modal
                aria-labelledby="modal-title"
                aria-describedby="modal-desc"
                open={open}
                onClose={() => setOpen(false)}
                sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}
            >
                <Sheet
                    variant="outlined"
                    sx={{
                        maxWidth: 500,
                        width: '100%',
                        borderRadius: 'md',
                        p: 5,
                        boxShadow: 'lg',
                    }}
                >
                    <ModalClose onClick={() => setOpen(false)} variant="plain" sx={{m: 1}}/>
                    {!linkDetailValueEntity.linkJson ?
                        <Box sx={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                            <CircularProgress variant='soft' color="neutral"/>
                        </Box> :
                        <Box>
                            <Typography
                                component="h1"
                                id="modal-title"
                                level="h4"
                                textColor="inherit"
                                fontWeight="lg"
                                mb={1}
                                mt={2}
                            >
                                Rvn detail
                            </Typography>
                            <JsonFormatter json={linkDetailValueEntity.linkJson}/>
                        </Box>
                    }
                </Sheet>
            </Modal>
        </Box>
        <Link onClick={goTo}>{value}</Link>
    </>)
}
