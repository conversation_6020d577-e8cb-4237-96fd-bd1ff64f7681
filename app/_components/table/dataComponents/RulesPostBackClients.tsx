'use client'
import React, {useEffect, useState} from "react";
import {<PERSON>, <PERSON><PERSON>, Tab, <PERSON><PERSON><PERSON><PERSON>, TabPanel, Tabs} from "@mui/joy";
import Modal from "@mui/joy/Modal";
import Sheet from "@mui/joy/Sheet";
import ModalClose from "@mui/joy/ModalClose";
import {buildSteppersData} from "@/app/_components/table/functions/buildSteppersData";
import _ from "lodash";
import Typography from "@mui/joy/Typography";
import {RulesStructureComponent} from "@/app/_components/rules/RulesStructureComponent";

export const RulesPostBackClients: React.FC<any> = ({
                                                        row,
                                                        tableColumns,
                                                        dbTableName
                                                    }) => {
    const [titleRules, setTitleRules] = useState(null);
    const [stepperData, setStepperData] = useState(null);
    const [showModal, setShowModal] = useState(false);

    useEffect(() => {
        const getAvailableRules = row?.original?.rules ?? []
        if (!_.isEmpty(getAvailableRules)) {
            const rulesBuilt = getAvailableRules.map((infRules: any) => buildSteppersData(infRules))
            setStepperData(rulesBuilt)
            setTitleRules(row?.original?.client)
        }
    }, []);

    const openModal = () => {
        setShowModal(true)
    }

    return (<>
            <Button variant={"outlined"} color={"neutral"} onClick={openModal}>View</Button>
            <Modal
                aria-labelledby="modal-title"
                aria-describedby="modal-desc"
                open={showModal}
                onClose={() => setShowModal(false)}
                sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
                <Sheet
                    variant="outlined"
                    sx={{
                        maxWidth: 900,
                        width: '100%',
                        borderRadius: 'md',
                        p: 5,
                        boxShadow: 'lg'
                    }}>
                    <ModalClose onClick={() => setShowModal(false)} variant="plain" sx={{m: 1}}/>
                    <Box sx={{maxHeight: '400px', overflowY: 'auto'}}>
                        {titleRules && <div className="flex justify-center mb-5">
                            <Typography level="title-lg">
                                {titleRules} Rules
                            </Typography>
                        </div>}
                        {!_.isEmpty(stepperData) ? <>
                                <Tabs defaultValue={0} sx={{'& .MuiTabs-flexContainer': {justifyContent: 'center'}}}>
                                    <TabList sticky='top' sx={{
                                        display: 'inline-flex',
                                        justifyContent: 'center',
                                        '& .MuiTab-root': {
                                            minWidth: 5,
                                            '@media (max-width: 600px)': {
                                                fontSize: '0.75rem',
                                                minWidth: 0,
                                                padding: '6px 12px',
                                            }
                                        },
                                        zIndex: 8
                                    }}>
                                        {stepperData.map((stepInf: any, index: number) => (
                                            <Tab key={`tab_list_${index}`} value={index}
                                                 sx={{minWidth: 20}}>{stepInf?.title}</Tab>
                                        ))}
                                    </TabList>
                                    {stepperData.map((stepInf: any, index: number) => (
                                        <TabPanel key={`tab_body_${index}`} value={index}>
                                            <RulesStructureComponent stepperData={stepInf?.data ?? []}/>
                                        </TabPanel>
                                    ))}
                                </Tabs>
                            </> :
                            <div className="flex justify-center">
                                <Typography level="title-lg">
                                    No rules available
                                </Typography>
                            </div>}
                    </Box>
                </Sheet>
            </Modal>
        </>
    )
}