import {Button} from "@mui/joy";
import Box from "@mui/joy/Box";
import ModalClose from "@mui/joy/ModalClose";
import Typography from "@mui/joy/Typography";
import Sheet from "@mui/joy/Sheet";
import Modal from "@mui/joy/Modal";
import {useState} from "react";
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {useMutation} from "@tanstack/react-query";

export const DeleteRowComponent = ({dbTableName, row, fullTableConfiguration, onDeleteRow}) => {
    const [open, setOpen] = useState(false);
    const [deleting, setDeleting] = useState(false);

    const deleteRow = () => {
        deleteMutation.mutate();
    }

    const deleteMutation = useMutation({
        mutationFn: async () => {
            setDeleting(true);
            const {lock_on_modify} = fullTableConfiguration;
            const payload = {
                tasksAfterSubmit: ['deleteInvoiceTask'],
                collection: dbTableName,
                lock_on_modify,
                row: row.original
            }
            await fetch("/api/mongo/delete", {
                method: 'POST',
                body: JSON.stringify(payload),
            })
        },
        onSuccess: () => {
            if (onDeleteRow) {
                onDeleteRow(row)
            }
            setDeleting(false);
            setOpen(false);
            showSuccess(`Item deleted`);

        },
        onError: () => {
            setDeleting(false);
            setOpen(false);
            showError(`Error to delete item`)

        },
    })

    return <>
        <Button size="md" variant='outlined' color="danger" sx={{marginLeft: '5px'}} onClick={() => setOpen(!open)}>
            Delete
        </Button>

        <Box className={"delete-row-modal-container"}>
            <Modal
                aria-labelledby="modal-title"
                aria-describedby="modal-desc"
                open={open}
                onClose={() => setOpen(false)}
                sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}
            >
                <Sheet
                    variant="outlined"
                    sx={{maxWidth: 700, borderRadius: 'md', p: 3, boxShadow: 'lg'}}
                >
                    {!deleting &&
                        <ModalClose variant="plain" sx={{m: 1}}/>
                    }
                    <Box sx={{textAlign: 'center'}}>
                        <HighlightOffIcon sx={{color: 'red', fontSize: '50px', textAlign: 'center'}}/>
                    </Box>
                    <Typography
                        component="h2"
                        id="modal-title"
                        level="h4"
                        textColor="inherit"
                        sx={{fontWeight: 'xl', mb: 2, mt: 1, textAlign: 'center'}}>
                        Confirm delete
                    </Typography>
                    <Typography id="modal-desc" textColor="text.tertiary">
                        Are you sure you want to delete this record?
                    </Typography>
                    <Box sx={{mt: 3, textAlign: 'end'}}>
                        <Button size="md" variant='outlined' color="neutral" sx={{marginLeft: '5px'}}
                                onClick={() => setOpen(!open)} disabled={deleting}>
                            Cancel
                        </Button>
                        <Button size="md" variant='outlined' color="danger" sx={{marginLeft: '10px'}}
                                onClick={deleteRow} loading={deleting}>
                            Delete
                        </Button>
                    </Box>
                </Sheet>
            </Modal>
        </Box>
    </>
}
