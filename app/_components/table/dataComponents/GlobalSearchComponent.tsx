import {Input, Tooltip} from "@mui/joy";
import {globalSearchHelpText} from "@/app/_components/table/others/globalSearchHelpText";
import SearchIcon from "@mui/icons-material/Search";
import HelpIcon from '@mui/icons-material/Help';
import React, {useState} from "react";
import _ from "lodash";
import IconButton from "@mui/joy/IconButton";
import {useEntity} from "simpler-state";
import {
    setPhraseGloSea,
    setValueInpPhrGloSea,
    valueInpPhrGloSea
} from "@/app/_components/table/states/globalSearchStates";

export const GlobalSearchComponent: React.FC<any> = ({loading, fieldsSearch}) => {
    const [open, setOpen] = useState(false);
    const valueInp = useEntity(valueInpPhrGloSea)

    const onKeyUp = (event: any) => {
        if (event.key === 'Enter') {
            setPhraseGloSea(event.target.value)
        }
    }

    const onBlur = (event: any) => {
        if (valueInp && !_.isEmpty(valueInp)) {
            setPhraseGloSea(valueInp)
        }
    }

    const handleTooltip = () => {
        setOpen(!open);
    };

    return (
        <Input
            value={valueInp}
            disabled={loading}
            endDecorator={
                <Tooltip
                    onClose={handleTooltip}
                    open={open}
                    disableFocusListener
                    disableTouchListener
                    title={globalSearchHelpText(fieldsSearch)}>
                    <IconButton onClick={handleTooltip} variant="plain">
                        <HelpIcon/>
                    </IconButton>
                </Tooltip>}
            sx={{width: '90%'}}
            placeholder="Global Search"
            onKeyUp={onKeyUp}
            onBlur={onBlur}
            onChange={(event) => setValueInpPhrGloSea(event.target.value)}
            startDecorator={<SearchIcon/>}/>
    );
}
