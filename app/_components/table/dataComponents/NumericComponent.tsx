'use client'
import React, {useEffect, useState} from "react";
import {formatNumber} from "@/app/_lib/utils/formatNumber";
import {formatCurrency} from "@/app/_lib/utils/formatCurrency";
import Decimal from "decimal.js";


export const NumericComponent: React.FC<any> = ({
                                                    initialValue,
                                                    onUpdate,
                                                    configuration,
                                                    columnName
                                                }) => {
    const [value, setValue] = useState(initialValue || 0);
    const editable = configuration?.editable
    useEffect(() => {
        setValue(initialValue);
    }, [initialValue]);

    const onBlur = async () => {
        if (initialValue != value) {
            const payload = {[columnName]: value};
            onUpdate(payload)
        }
    };

    const displayValue = (value: any): any => {
        if (configuration.currency) {
            const decimals = configuration.currency.decimals ? configuration.currency.decimals: 2;
            const decimalAmount = value ? new Decimal(value) : new Decimal(0);
            const usdValue = decimalAmount.div(100).toDP(decimals).toNumber()
            return `$${formatCurrency(usdValue)}`
        } else {
            return formatNumber(value)
        }
    }
    return (
        <>
            {editable && value != undefined ?
                <input
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                    onBlur={onBlur}
                />
                : <span>{displayValue(value)}</span>
            }

        </>
    );
};
