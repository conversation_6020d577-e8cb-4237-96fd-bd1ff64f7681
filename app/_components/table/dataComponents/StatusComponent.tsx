'use client'
import React, {useEffect, useState} from "react";

export const StatusComponent: React.FC<any> = ({
                                                   initialValue,
                                                   onUpdate,
                                                   configuration,
                                                   columnName
                                               }) => {
    const [value, setValue] = useState(initialValue || '');
    const statusConfigurations = configuration.status || {};
    useEffect(() => {
        setValue(initialValue);
    }, [initialValue]);

    const getColor = (value: any) => {
        return statusConfigurations && statusConfigurations[value] && statusConfigurations[value].color ? statusConfigurations[value].color : 'white'
    }
    const buildStyles = (value: any) => {
        return {
            border: `solid 1px ${getColor(value)}`,
            borderRadius: '20px',
            color: getColor(value),
            justifyContent: 'center',
            alignItems: 'center',
            fontWeight: 500,
            padding: '3px',
            paddingLeft: '10px',
            paddingRight: '10px',
            margin: 'auto',
        }
    }
    const displayValue = (value: any): any => {
        return statusConfigurations && statusConfigurations[value] && statusConfigurations[value].label ? statusConfigurations[value].label : value
    }

    return (
        <>
            <span style={buildStyles(value)}>{displayValue(value)}</span>
        </>
    );
};
