'use client'
import React, {useEffect, useState} from "react";
import {Box, Button} from "@mui/joy";
import Sheet from "@mui/joy/Sheet";
import ModalClose from "@mui/joy/ModalClose";
import Modal from "@mui/joy/Modal";
import JsonView from "react18-json-view";
import 'react18-json-view/src/style.css'

export const JsonDataComponent: React.FC<any> = ({
                                                     initialValue,
                                                     onUpdate,
                                                     configuration,
                                                     columnName
                                                 }) => {
    const [value, setValue] = useState(null);
    const [showModal, setShowModal] = useState(false);

    useEffect(() => {
        setValue(initialValue || {data: 'No available'});
    }, [initialValue]);


    function openModal() {
        setShowModal(true)
    }

    return (
        <>
            <Button variant={"outlined"} color={"neutral"} onClick={openModal}>{configuration?.linkLabel}</Button>
            <Modal
                aria-labelledby="modal-title"
                aria-describedby="modal-desc"
                open={showModal}
                onClose={() => setShowModal(false)}
                sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}
            >
                <Sheet
                    variant="outlined"
                    sx={{
                        maxWidth: 900,
                        width: '100%',
                        borderRadius: 'md',
                        p: 5,
                        boxShadow: 'lg',
                    }}
                >
                    <ModalClose onClick={() => setShowModal(false)} variant="plain" sx={{m: 1}}/>
                    <Box sx={{maxHeight: '400px', overflowY: 'auto'}}>
                        <JsonView src={value} theme="github" enableClipboard={false} collapseStringsAfterLength={150}/>
                    </Box>
                </Sheet>
            </Modal>
        </>
    );
};
