import React from "react";
import {LinkComponent} from "@/app/_components/table/dataComponents/LinkComponent";
import {TextComponent} from "@/app/_components/table/dataComponents/TextComponent";
import {NumericComponent} from "@/app/_components/table/dataComponents/NumericComponent";
import {JsonDataComponent} from "@/app/_components/table/dataComponents/JsonDataComponent";
import {StatusComponent} from "@/app/_components/table/dataComponents/StatusComponent";
import {ArrayDataComponent} from "@/app/_components/table/dataComponents/ArrayDataComponent";
import {ObjectDataComponent} from "@/app/_components/table/dataComponents/ObjectDataComponent";
import {TagsComponent} from "@/app/_components/table/dataComponents/TagsComponent";
import {useQuery} from "@tanstack/react-query";
import {Tags} from "@/app/_components/queries/Tags";

export const DataComponentFactory: React.FC<any> = ({
                                                        initialValue,
                                                        onUpdate,
                                                        configuration,
                                                        columnName,
                                                        dbTableName
                                                    }) => {
    const {data: tagsData} = useQuery(Tags(dbTableName, (configuration?.component === 'ObjectDataComponent'), false));

    const getDataComponentFactory: any = () => {
        switch (configuration?.component) {
            case 'TextComponent':
                return <TextComponent initialValue={initialValue} onUpdate={onUpdate} configuration={configuration}
                                      columnName={columnName}/>;
            case 'StatusComponent':
                return <StatusComponent initialValue={initialValue} onUpdate={onUpdate} configuration={configuration}
                                        columnName={columnName}/>;
            case 'LinkComponent':
                return <LinkComponent columnName={columnName} value={initialValue} configuration={configuration}/>

            case 'NumericComponent':
                return <NumericComponent columnName={columnName} initialValue={initialValue}
                                         configuration={configuration}/>

            case 'JsonDataComponent':
                return <JsonDataComponent columnName={columnName} initialValue={initialValue}
                                          configuration={configuration}/>
            case 'ArrayDataComponent':
                return <ArrayDataComponent columnName={columnName} initialValue={initialValue}
                                           configuration={configuration}/>
            case 'ObjectDataComponent':
                return <ObjectDataComponent columnName={columnName} initialValue={initialValue} tagsData={tagsData}
                                            configuration={configuration} dbTableName={dbTableName}/>
            case 'TagsComponent':
                return <TagsComponent columnName={columnName} initialValue={initialValue}
                                      configuration={configuration}/>
            default:
                return <TextComponent initialValue={initialValue} onUpdate={onUpdate} configuration={configuration}
                                      columnName={columnName}/>;
        }
    }
    return (getDataComponentFactory())
}
