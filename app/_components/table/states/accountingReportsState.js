import {entity} from "simpler-state";
import {getISOWeeksInMonth} from "@/app/_components/utils/getISOWeeksInMonth.js";
import {getDefaultIsoWeek} from "@/app/_components/utils/getDefaultIsoWeek.js";

const currentDate = getDefaultIsoWeek({});
let isoWeeks = getISOWeeksInMonth(currentDate.year, currentDate.month - 1);
const allWeeks = isoWeeks.map(([weekNum]) => weekNum)

export const accountingReportFiltersEntity = entity({
    accountingFilters: {
        type: {$in: ['REVENUE', 'EXPENSE']},
        source_table: {$in: ['leads', 'transfers', 'postbacks']},
        week: {$in: allWeeks},
        year: currentDate.year,
    },
    month: 1,
    accountingType: 'REVENUE',
    collection: 'leads',
});


export const setAccountingReportFiltersEntity = (filters) => {
    accountingReportFiltersEntity.set(filters);
}
