import {entity, persistence} from 'simpler-state';

const setPersistence = (key) => {
    if (typeof window !== 'undefined') {
        return [persistence(`db_${key}`)];
    }
    return [];
}

export const createTableEntities = (tableName) => {
    const defaultData = [];
    const defaultPaginateConfig = {
        count: 0,
        page: 0,
        size: 5,
        hasNextPage: true,
        totalPages: 0,
        cursor: 0,
        minCursor: 0,
        action: ''
    };
    fixUndefinedStorage()
    const dataTableState = entity(defaultData, setPersistence(tableName));
    const paginateConfigEntity = entity(defaultPaginateConfig, setPersistence(`${tableName}-pagination-config`));
    const filtersEntity = entity({}, setPersistence(`${tableName}-filters`));
    const dataChangeCount = entity(0, setPersistence(`${tableName}-change-count`));
    const columnsVisibilityEntityConfiguration = entity({}, setPersistence(`${tableName}-columns-visibility`))
    const defaultFiltersEntity = entity({}, setPersistence(`${tableName}-default-filters`));

    const setDataTableState = (value) => {
        dataTableState.set(value);
    }

    const setPaginateConfigEntity = (value) => {
        paginateConfigEntity.set(value);
    }

    const setFiltersEntity = (value) => {
        filtersEntity.set(value);
    }

    const setDataChangeCount = () => {
        dataChangeCount.set(dataChangeCount.get() + 1);
    }

    const setColumnsVisibilityEntityConfiguration = (value) => {
        columnsVisibilityEntityConfiguration.set(value);
    }
    const setDefaultFilters = (value) => {
        defaultFiltersEntity.set(value);
    }

    return {
        dataTableState,
        paginateConfigEntity,
        filtersEntity,
        dataChangeCount,
        setDataTableState,
        setPaginateConfigEntity,
        setFiltersEntity,
        setDataChangeCount,
        columnsVisibilityEntityConfiguration,
        setColumnsVisibilityEntityConfiguration,
        defaultFiltersEntity,
        setDefaultFilters
    };
};


const fixUndefinedStorage = () => {
    if (typeof window !== 'undefined') {
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            const value = localStorage.getItem(key);
            if (!value || value === 'undefined' || value === 'null') {
                localStorage.setItem(key, "{}")
            }
        }
    }
}
