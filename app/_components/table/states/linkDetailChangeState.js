'use client'
import {entity} from 'simpler-state';


export const linkDetailChange = entity(0);

export const setLinkDetailChange = () => {
    linkDetailChange.set(linkDetailChange.get() + 1);
}


export const linkDetailValue = entity({
    showModal: false,
    linkConfiguration: {linkTable: '', linkKey: ''} || undefined,
    linkJson: {} || undefined,
    linkValue: ''
});

export const setLinkDetailValue = (value) => {
    linkDetailValue.set(value);
}


