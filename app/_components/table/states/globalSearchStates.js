'use client'
import {entity} from "simpler-state";

export const phraseGlobalSearch = entity("");

export const valueInpPhrGloSea = entity("");

export const showFiltersGS = entity(true);

export const showOnlyPagBut = entity(false);


export const setPhraseGloSea = (newPhrase) => {
    let textToSearch = newPhrase ? newPhrase.trim() : '';
    if (isEmail(textToSearch)) {
        textToSearch = processEmail(newPhrase);
    }
    phraseGlobalSearch.set(textToSearch);
}

export const setValueInpPhrGloSea = (valueInp) => {
    valueInpPhrGloSea.set(valueInp);
}

export const setShowFiltersGS = (show) => {
    showFiltersGS.set(show);
}

export const setShowOnlyPagBut = (show) => {
    showOnlyPagBut.set(show);
}

const isEmail = (email) => {
    const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return regex.test(email);
}

const processEmail = (email) => {
    const arrayEmail = email.split("@");
    return arrayEmail[0];
}
