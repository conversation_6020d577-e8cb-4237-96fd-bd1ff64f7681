'use client'
import {entity} from 'simpler-state';

export const tabChangeOnSummary = entity(0);

export const isReportConsult = entity(false);

export const detailValueSelected = entity({
    label: "None",
    key: null,
    value: "none"
});

export const thirdLevelSelected = entity({
    label: "None",
    key: null,
    value: "none"
});

export const changeDefFiltersOnSelSum = entity({});

export const vendorLeadsSelected = entity({});

export const setTabChangeOnSummary = (tabToChange) => {
    tabChangeOnSummary.set(tabToChange);
}

export const setDetailValueSelected = (newDetailSelect) => {
    detailValueSelected.set(newDetailSelect);
}

export const setThirdLevelSelected = (new3rdLevel) => {
    thirdLevelSelected.set(new3rdLevel);
}

export const setChangeDefFiltersOnSelSum = (filters) => {
    changeDefFiltersOnSelSum.set(filters);
}

export const setIsReportConsult = (consult) => {
    isReportConsult.set(consult);
}

export const setVendorLeadsSelected = (idsVendor) => {
    vendorLeadsSelected.set(idsVendor);
}




