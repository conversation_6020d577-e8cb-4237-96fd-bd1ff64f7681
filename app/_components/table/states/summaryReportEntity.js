
export const createSummaryDashboardReportEntities = (tableName, valueSel) => {
        if (valueSel === undefined) {
        const selectedKey = localStorage.getItem(tableName + 'summarySelect')
        let filters = localStorage.getItem(tableName + 'summaryDate');
        if (selectedKey !== null) {
            const jsonObject = JSON.parse(selectedKey);
            filters = localStorage.getItem(tableName + 'summaryDate' + jsonObject.show);
        }
        return {
            filtersEntity: JSON.parse(filters),
            filterSelected: JSON.parse(selectedKey)
        };}
        else {
            const filters = localStorage.getItem(tableName + 'summaryDate' + valueSel.show);
            return {
                filtersEntity: JSON.parse(filters),
                filterSelected: valueSel
            };
        }
};

export const createSummaryReportEntities = (tableName) => {
    if (typeof window !== 'undefined') {
        const filters = localStorage.getItem(tableName + 'summaryDate');
        const selectedKey = localStorage.getItem(tableName + 'summarySelect')
        return {
            filtersEntity: JSON.parse(filters),
            filterSelected: JSON.parse(selectedKey)
        };
    }
    return {
        filtersEntity: null
    };
};
