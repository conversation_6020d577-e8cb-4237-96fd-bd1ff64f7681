'use client'
import {entity} from "simpler-state";


export const tableNameForEdit = entity(null);

export const formPermissions = entity(null);

export const originalTemplate = entity(null);

export const userIdForEdit = entity(null);

export const activeTabUserMetadata = entity('roles');

export const rolSelected = entity(null);

export const timezoneSelected = entity(null);

export const tabsAdminData = entity([]);

export const loadingGetData = entity(false);

export const setTableNameForEdit = (tableId) => {
    tableNameForEdit.set(tableId);
}

export const setFormPermissions = (formData) => {
    formPermissions.set(formData);
}

export const setOriginalTemplate = (formData) => {
    originalTemplate.set(formData);
}

export const setUserIdForEdit = (userId) => {
    userIdForEdit.set(userId);
}

export const setLoadingGetData = (isLoading) => {
    loadingGetData.set(isLoading);
}

export const setActiveTabUserMetadata = (activeTab) => {
    activeTabUserMetadata.set(activeTab);
}

export const setRolSelected = (rolInfo) => {
    rolSelected.set({name: rolInfo?.role, object: rolInfo?.permissions});
}

export const setTimezoneSelected = (timezoneSel) => {
    timezoneSelected.set(timezoneSel);
}

export const setTabsAdminData = (tabsInfo) => {
    tabsAdminData.set(tabsInfo);
}
