'use client'
import {entity} from 'simpler-state';

export const dataChangeNodeScriptsCount = entity(0);

export const idNodeAddedPanel = entity(null);

export const activeTabNodeScripts = entity(0);

export const sessionDataChatScript = entity({});

export const messagesChatScript = entity([]);

export const promptChatScript = entity(null);

export const dataQuestChatScript = entity({});

export const dataExtractedChatScript = entity({});

export const viewExtractedChatScript = entity(false);

export const enableExtractionChatScript = entity(false);

export const chatScriptStarted = entity(false);

export const setDataChangeNodeScriptsCount = () => {
    dataChangeNodeScriptsCount.set(dataChangeNodeScriptsCount.get() + 1);
}

export const setActiveNodeScriptTab = (newTab) => {
    activeTabNodeScripts.set(newTab);
}

export const setIdNodeAddedPanel = (nodeInfo) => {
    idNodeAddedPanel.set(nodeInfo);
}

export const setSessionDataChatScript = (sessionData) => {
    sessionDataChatScript.set(sessionData);
}

export const setMessagesChatScripts = (newMessages) => {
    messagesChatScript.set(newMessages);
}

export const setPromptChatScript = (prompt) => {
    promptChatScript.set(prompt);
}

export const setDataQuestChatScript = (newData) => {
    dataQuestChatScript.set(newData);
}

export const setDataExtractedChatScript = (newDataExtractedChatScript) => {
    dataExtractedChatScript.set(newDataExtractedChatScript);
}

export const setViewExtractedChatScript = (newData) => {
    viewExtractedChatScript.set(newData);
}

export const setEnableExtractionChatScript = (newData) => {
    enableExtractionChatScript.set(newData);
}

export const setChatScriptStarted = (stateChat) => {
    chatScriptStarted.set(stateChat);
}
