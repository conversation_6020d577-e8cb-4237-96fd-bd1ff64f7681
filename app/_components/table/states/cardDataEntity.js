import {entity, persistence} from 'simpler-state';

const setPersistence = (key) => {
    if (typeof window !== 'undefined') {
        return [persistence(`db_${key}`)];
    }
    return [];
}

export const createCardEntities = (tableName) => {
    const defaultPaginateConfig = {
        count: 0,
        page: 0,
        size: 5,
        hasNextPage: true,
        totalPages: 0,
        cursor: 0,
        minCursor: 0,
        action: ''
    };
    fixUndefinedStorage()
    const paginateConfigEntity = entity(defaultPaginateConfig, setPersistence(`${tableName}-pagination-config`));


    const setPaginateConfigEntity = (value) => {
        paginateConfigEntity.set(value);
    }


    return {
        paginateConfigEntity,
        setPaginateConfigEntity,
    };
};

const fixUndefinedStorage = () => {
    if (typeof window !== 'undefined') {
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            const value = localStorage.getItem(key);
            if (!value || value === 'undefined' || value === 'null') {
                localStorage.setItem(key, "{}")
            }
        }
    }
}
