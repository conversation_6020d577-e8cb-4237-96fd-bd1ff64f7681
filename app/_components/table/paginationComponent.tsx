"use client"
import React from "react";
import {useEntity} from "simpler-state";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity";
import {reFetchDataCount} from "@/app/_components/table/states/changeDataState";
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import {Button, Select, Tooltip} from "@mui/joy";
import Option from '@mui/joy/Option';
import _ from "lodash";
import {loadingCounting} from "@/app/_components/table/states/changeCountState";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import Loading from "react-loading";
import {
    setPhraseGloSea,
    setShowFiltersGS,
    setShowOnlyPagBut,
    setValueInpPhrGloSea,
    showOnlyPagBut
} from "@/app/_components/table/states/globalSearchStates";
import {setClearFiltersChange} from "@/app/_components/table/states/clearFiltersState";
import {styleNumber} from "@/app/_components/utils/styleNumber";
import PubSub from 'pubsub-js'
import {pubSubEvents} from "@/app/_lib/utils/pubsub/pubSubEvents";
import {Pagination} from "@/app/_components/queries/Pagination";
import {setDebugFilters} from "@/app/_components/table/states/debugFiltersState";


const PaginationComponent: React.FC<any> = ({
                                                dbTableName,
                                                dbName,
                                                tableColumns,
                                                tableDetails,
                                                loading,
                                                dataTable,
                                                shouldFetchCount,
                                                onClearFilters
                                            }) => {
    const {
        paginateConfigEntity,
        setPaginateConfigEntity,
        setFiltersEntity,
        filtersEntity,
        defaultFiltersEntity
    } = createTableEntities(dbTableName);

    const paginateConfig = useEntity(paginateConfigEntity)
    const showOnlyPagButtons = useEntity(showOnlyPagBut);
    const reFetchData = useEntity(reFetchDataCount);
    const loadingCount = useEntity(loadingCounting);
    const filters = useEntity(filtersEntity);
    const queryClient = useQueryClient();

    const {
        data: dataCount,
        isFetching
    } = useQuery(Pagination(dbTableName, dbName, filters, paginateConfig, reFetchData, showOnlyPagButtons,tableDetails));

    const nextPage = async () => {
        if (paginateConfig?.page != dataCount?.totalPages && dataCount?.totalPages != 1 && paginateConfig?.page + 1 < dataCount?.totalPages) {
            setPaginateConfigEntity({...paginateConfig, page: paginateConfig?.page + 1, action: 'NEXT'})
            invalidateQuery()
        }
    }

    const previousPage = async () => {
        if (paginateConfig?.page != 0) {
            setPaginateConfigEntity({...paginateConfig, page: paginateConfig?.page - 1, action: 'PREVIOUS'})
            invalidateQuery()
        }
    }

    const initialPage = async () => {
        if (paginateConfig?.page != 0) {
            setPaginateConfigEntity({...paginateConfig, page: 0, action: 'START'})
            invalidateQuery()
        }
    }

    const endPage = async () => {
        if (paginateConfig?.page != dataCount?.totalPages && dataCount?.totalPages != 1) {
            setPaginateConfigEntity({...paginateConfig, page: dataCount?.totalPages - 1, action: 'END'})
            invalidateQuery()
        }
    }

    async function changePaginationSize(event: any, newValue: any) {
        setPaginateConfigEntity({...paginateConfig, page: 0, size: newValue, action: 'START'})
        invalidateQuery()
    }

    const hasFilter = () => {
        return _.some(filtersEntity.get(), (filter: any) => {
            let value = filter?.value;
            if (typeof value === 'object') {
                value = JSON.stringify(value);
            }
            if (typeof value === 'number') {
                return true;
            }
            if (typeof value === 'boolean') {
                return true;
            }
            return !value?.includes("NA_FILTER");
        });
    }

    const clearFilters = () => {
        setPaginateConfigEntity({...paginateConfig, page: 0, sort: tableDetails?.sort})
        setFiltersEntity(defaultFiltersEntity.get());
        setShowFiltersGS(true)
        setShowOnlyPagBut(false)
        setPhraseGloSea()
        setValueInpPhrGloSea('')
        setClearFiltersChange();
        setDebugFilters({})
        if (onClearFilters) onClearFilters();
        PubSub.publish(pubSubEvents.CLEAR_FILTERS)
        invalidateQuery()
    }

    const invalidateQuery = async () => {
        await queryClient.invalidateQueries({
            queryKey: [`table_data_${dbTableName}`],
            refetchType: "active"
        });
    };

    return <>
        <div className="mt-5 w-full p-1">
            {(showOnlyPagButtons) ?
                <div className="flex justify-center mx-auto items-center w-100 gap-2">
                    <Tooltip title="Init page" variant="soft" placement="top">
                        <Button size="md" variant='soft' onClick={() => initialPage()} color="neutral">
                            <KeyboardDoubleArrowLeftIcon/>
                        </Button>
                    </Tooltip>
                    <Tooltip title="Previous page" variant="soft" placement="top">
                        <Button size="md" variant='soft' onClick={previousPage} color="neutral">
                            <KeyboardArrowLeftIcon/>
                        </Button>
                    </Tooltip>
                    <Tooltip title="Next page" variant="soft" placement="top">
                        <Button size="md" variant='soft' onClick={nextPage} color="neutral">
                            <KeyboardArrowRightIcon/>
                        </Button>
                    </Tooltip>
                    <Tooltip title="Size page" variant="soft" placement="top">
                        <Select value={paginateConfig.size} defaultValue={paginateConfig.size} variant={'soft'}
                                onChange={(event, newValue) => changePaginationSize(event, newValue)}>
                            <Option value={5}>5</Option>
                            <Option value={10}>10</Option>
                            <Option value={20}>20</Option>
                            <Option value={50}>50</Option>
                        </Select>
                    </Tooltip>
                </div> : (!isFetching && !loadingCount) ? <>
                        <div className="flex justify-center mx-auto items-center w-100 gap-2">
                            <Tooltip title="Init page" variant="soft" placement="top">
                                <Button size="md" variant='soft' onClick={() => initialPage()} color="neutral">
                                    <KeyboardDoubleArrowLeftIcon/>
                                </Button>
                            </Tooltip>
                            <Tooltip title="Previous page" variant="soft" placement="top">
                                <Button size="md" variant='soft' onClick={previousPage} color="neutral">
                                    <KeyboardArrowLeftIcon/>
                                </Button>
                            </Tooltip>
                            <Tooltip title="Next page" variant="soft" placement="top">
                                <Button size="md" variant='soft' onClick={nextPage} color="neutral">
                                    <KeyboardArrowRightIcon/>
                                </Button>
                            </Tooltip>
                            <Tooltip title="End page" variant="soft" placement="top">
                                <Button size="md" variant='soft' onClick={endPage} color="neutral">
                                    <KeyboardDoubleArrowRightIcon/>
                                </Button>
                            </Tooltip>
                            <Tooltip title="Size page" variant="soft" placement="top">
                                <Select value={paginateConfig.size} defaultValue={paginateConfig.size} variant={'soft'}
                                        onChange={(event, newValue) => changePaginationSize(event, newValue)}>
                                    <Option value={5}>5</Option>
                                    <Option value={10}>10</Option>
                                    <Option value={20}>20</Option>
                                    <Option value={50}>50</Option>
                                </Select>
                            </Tooltip>
                        </div>
                        {!loading ?
                            <div
                                className=" w-full flex content-center items-center justify-center gap-1 total-items-paging mt-3 mb-2">
                                <div>Page</div>
                                <p><span className='font-bold'> {paginateConfig.page + 1} </span> of{' '}
                                    <span className='font-bold'>
                            {styleNumber(dataCount?.totalPages === 1 ? dataCount.totalPages : dataCount?.totalPages)}
                                    </span>
                                </p>
                                <span>
                                    , out of{' '}
                                    <span className='font-bold'>
                                        {styleNumber(dataCount?.count)}
                                    </span>
                                    {' '}items
                                </span>
                            </div> :
                            <div
                                className=" w-full flex content-center items-center justify-center gap-1 total-items-paging mt-3 mb-2">
                                Counting <Loading color={'black'} width={'20px'} height={'20px'} type={'bubbles'}/>
                            </div>
                        }
                    </> :
                    <div
                        className=" w-full flex content-center items-center justify-center gap-1 total-items-paging mt-3 mb-2">
                        <Loading color={'black'} width={'20px'} height={'20px'} type={'bubbles'}/>
                    </div>
            }
            <div className=" w-full flex content-center items-center justify-center gap-1 total-items-paging mt-3 mb-2">
                {(dbTableName !== "campaigns") &&
                    <Button size="md" variant='soft' onClick={() => clearFilters()} color="neutral">
                        Clear filters
                    </Button>}
            </div>
        </div>
    </>
}

export default PaginationComponent
