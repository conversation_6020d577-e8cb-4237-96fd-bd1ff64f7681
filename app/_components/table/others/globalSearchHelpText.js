import _ from "lodash";

export const globalSearchHelpText = (fieldsSearch) => {
    return (
        <div className="m-3">
            <div className="flex justify-center"><b>Search:</b></div>
            <div className="flex justify-center">This search can find records with the following fields:</div>
            <div className="text-center" dangerouslySetInnerHTML={{__html: buildSearchTextHelp(fieldsSearch)}}/>
            <br/>
            <div className="flex justify-center"><b>Instructions:</b></div>
            <div className="flex justify-center">You can also perform more complex searches with</div>
            <div className="flex justify-center">the help of the following items:</div>
            <div className="flex justify-center">1. Searching for multiple words writing:</div>
            <div className="flex justify-center">word1 word2</div>
            <div className="flex justify-center">2. Using logical operators (AND, OR):</div>
            <div className="flex justify-center">word1 OR word2</div>
            <div className="flex justify-center">3. Excluding words with a minus sign (-):</div>
            <div className="flex justify-center">word1 -word2</div>
            <br/>
            <div className="flex justify-center"><b>Note:</b></div>
            <div className="flex justify-center">If you use the global search, you can&apos;t use</div>
            <div className="flex justify-center">other filters</div>
            <div className="flex justify-center">Please do the complete search here</div>
        </div>
    )
}


const buildSearchTextHelp = (fieldsSearch) => {
    const newArray = chunkIntoN(fieldsSearch, 4);
    let helpText = ''
    if (newArray.length > 1) {
        for (let i = 0; i < newArray.length; i++) {
            const subStr = newArray[i].join(', ')
            helpText = helpText + `<p>${_.trim(subStr)}</p>`
        }
    } else {
        const subStr = newArray[0].join(', ')
        helpText = `<p>${_.trim(subStr)}</p>`
    }
    return helpText
}

const chunkIntoN = (arr, size) => {
    const arrays = []
    for (let i = 0; i < arr.length; i += size){
        arrays.push(arr.slice(i, i + size));
    }
    return arrays
}