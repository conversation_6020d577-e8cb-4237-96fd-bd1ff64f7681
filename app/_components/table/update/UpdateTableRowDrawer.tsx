import React from 'react';
import Drawer from "@mui/joy/Drawer";
import Sheet from "@mui/joy/Sheet";
import DialogTitle from "@mui/joy/DialogTitle";
import ModalClose from "@mui/joy/ModalClose";
import Divider from "@mui/joy/Divider";
import DialogContent from "@mui/joy/DialogContent";

const UpdateTableRowDrawer: React.FC<any> = ({ open, onClose, title, children }) => {
    return (
        <Drawer
            size="lg"
            variant="plain"
            open={open}
            anchor={"right"}
            onClose={onClose}
            slotProps={{
                content: {
                    sx: {
                        bgcolor: 'transparent',
                        p: {md: 0, sm: 0},
                        boxShadow: 'none',
                    },
                },
            }}>
            <Sheet
                sx={{
                    p: 2,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2,
                    height: '100%',
                    overflow: 'auto',
                    marginTop: '40px',
                }}>
                <DialogTitle>{title}</DialogTitle>
                <ModalClose/>
                <Divider sx={{mt: 'auto'}}/>
                <DialogContent sx={{gap: 1}}>
                    {children}
                </DialogContent>
            </Sheet>
        </Drawer>
    );
};

export default UpdateTableRowDrawer;
