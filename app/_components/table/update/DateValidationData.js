import {formatISO} from 'date-fns';

export const formatDateToISO = (dateString) => {
    const date = new Date(dateString);
    return formatISO(date);
};

export const formatDatesInObject = (obj, schema) => {
    const formattedObj = {...obj};
    for (const key in formattedObj) {
        if (schema && schema[key] && schema[key].format === 'date') {
            formattedObj[key] = formatDateToISO(formattedObj[key]);
        }
    }
    return formattedObj;
};
