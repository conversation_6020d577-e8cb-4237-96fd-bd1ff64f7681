import React from 'react';
import {withTheme} from "@rjsf/core";
import {Theme as AntDTheme} from '@rjsf/antd';
import Box from "@mui/joy/Box";
import Card from "@mui/joy/Card";
import Button from "@mui/joy/Button";
import IconButton from "@mui/joy/IconButton";
import Typography from "@mui/joy/Typography";
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import {ArrayFieldTemplateProps} from "@rjsf/utils";
import MultiSelectWidget from "@/app/_components/table/update/MultipleSelectChipWidget";
import {useQuery} from "@tanstack/react-query";
import Loading from "react-loading";
import {UpdateConfiguration} from "@/app/_components/queries/UpdateConfiguration";
import { customizeValidator } from "@rjsf/validator-ajv8";
import ajvErrors from "ajv-errors";
const ThemedForm = withTheme(AntDTheme);
const validator = customizeValidator();
ajvErrors(validator.ajv);

const UpdateClientForm: React.FC<any> = ({
                                             schemaData,
                                             formData,
                                             handleSubmit,
                                             editableRow,
                                             jsonKeys,
                                             tagsData,
                                             fullTableConfiguration,
                                             dbTableName
                                         }) => {

    const {data: updateConfiguration, isLoading} = useQuery(UpdateConfiguration(dbTableName, jsonKeys, formData));

    const ArrayFieldTemplate = ({items, canAdd, onAddClick, title}: ArrayFieldTemplateProps) => (
        <Box>
            <Card
                key={title}
                variant="outlined"
                sx={{
                    marginBottom: '5px',
                    padding: '16px',
                    backgroundColor: 'rgb(249 250 251)'
                }}>
                <Typography sx={{marginBottom: '16px'}}>
                    {title}
                </Typography>
                {items.length > 0 ? (
                    items.map((element, index) => (
                        <Card key={index} variant="outlined" sx={{marginBottom: '16px'}}>
                            <Box sx={{padding: '16px', display: 'flex', flexDirection: 'column'}}>
                                {element.children}
                                <Box sx={{display: 'flex', justifyContent: 'flex-end', marginTop: '8px'}}>
                                    <IconButton onClick={element.onDropIndexClick(element.index)}>
                                        <DeleteIcon/>
                                    </IconButton>
                                </Box>
                            </Box>
                        </Card>
                    ))
                ) : (
                    <Typography>
                        No results found
                    </Typography>
                )}
                {canAdd && (
                    <Box sx={{display: 'flex', justifyContent: 'flex-end', marginTop: '16px'}}>
                        <Button variant="outlined" onClick={onAddClick}>
                            <AddIcon sx={{marginRight: '8px'}}/>
                            Add new
                        </Button>
                    </Box>
                )}
            </Card>
        </Box>
    );

    const widgets = {
        MultiSelectWidget
    };

    const templates = {
        ArrayFieldTemplate
    };
    return (
        <Box sx={{overflowX: 'hidden'}}>
            {isLoading
                ? <Box style={{display: 'flex', justifyContent: 'center'}}>
                    <Loading color={'black'} width={'30px'} height={'30px'} type={'spin'}/>
                </Box>
                : <ThemedForm
                    schema={updateConfiguration?.rjsSchema}
                    formData={formData}
                    uiSchema={updateConfiguration?.uiSchema}
                    widgets={widgets}
                    templates={templates}
                    onSubmit={handleSubmit}
                    validator={validator}
                    disabled={!editableRow}
                />
            }
        </Box>
    );
};

export default UpdateClientForm;
