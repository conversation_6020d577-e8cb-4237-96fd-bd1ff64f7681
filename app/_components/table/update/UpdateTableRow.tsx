import * as React from 'react';
import {useEffect, useState} from 'react';
import {buildUpdateSchema} from "@/app/_components/table/update/BuildUpdateSchema";
import UpdateTableRowDrawer from './UpdateTableRowDrawer';
import UpdateForm from './UpdateFormRow';
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {formatDatesInObject} from "@/app/_components/table/update/DateValidationData";
import {buildFormData} from "@/app/_components/table/update/BuildFormData";
import {buildUpdateSchemaRow} from "@/app/_components/table/update/buildUpdateSchemaRow";
import UpdateVendorRow from "@/app/_components/table/update/UpdateVendorRow";
import {setLoading} from "@/app/_components/Loading/loadingState";
import {useQueryClient} from "@tanstack/react-query";

export const UpdateTableRow: React.FC<any> = ({row, tagsData, showModal, onClose, dbOptionsColumns, dbTableName}) => {
    const [editableRow, setEditableRow] = useState(false);
    const [open, setOpen] = useState(showModal);
    const [schemaData, setSchemaData] = useState<any>({});
    const buildForm = buildFormData(dbOptionsColumns, row.original);
    const formData = buildForm.properties
    const jsonKeys = buildForm.jsonAccessorKeys
    const queryClient = useQueryClient();

    useEffect(() => {
        const isEditable = dbOptionsColumns.some((column: any) => column.config.editable);
        setEditableRow(isEditable);
        if (dbTableName === 'leads' || dbTableName === 'postbacks' || dbTableName === 'transfers') {
            setSchemaData(buildUpdateSchemaRow(dbOptionsColumns));
        } else {
            setSchemaData(buildUpdateSchema(dbOptionsColumns, isEditable));
        }
    }, []);

    useEffect(() => {
        const checkEditableRow = () => {
            const isEditable = dbOptionsColumns.some((column: any) => column.config.editable);
            setEditableRow(isEditable);
        };
        checkEditableRow();
    }, [dbOptionsColumns]);

    useEffect(() => {
        setOpen(showModal);
    }, [showModal]);

    const getPayload = (value: any) => {
        const formattedValue = formatDatesInObject(value, schemaData.properties);
        return {
            collection: dbTableName,
            data: formattedValue,
            id: formattedValue._id,
        };
    };

    const updateData = async (value: any) => {
        try {
            setLoading(true);
            let payload = (dbTableName === 'leads' || dbTableName === 'postbacks' || dbTableName === 'transfers')
                ? getPayload(value)
                : getPayload(value.formData);

            const result = await fetch("/api/mongo/updateAllDataById", {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(payload)
            });

            if (result.ok) {
                showSuccess('Updated successfully');
                queryClient.invalidateQueries({queryKey: [`tags_query_${dbTableName}`]});
                queryClient.invalidateQueries({queryKey: [`table_data_${dbTableName}`]});
            } else {
                showError('We had a problem updating');
            }
        } catch (error) {
            showError(error.message || 'We had a problem updating');
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        setOpen(false);
        onClose();
    };

    const handleSubmit = async (formData: any) => {
        handleClose();
        await updateData(formData);
    };

    return (
        <UpdateTableRowDrawer open={open} onClose={handleClose} title="Update row">
            {dbTableName === 'vendors' || dbTableName === 'tags' ? (
                <UpdateVendorRow
                    schemaData={schemaData}
                    formData={formData}
                    handleSubmit={handleSubmit}
                    tagsData={tagsData}
                    editableFields={schemaData.editableFields}
                    dbTableName={dbTableName}
                />
            ) : (
                <UpdateForm
                    schemaData={schemaData}
                    formData={formData}
                    handleSubmit={handleSubmit}
                    editableRow={editableRow}
                    jsonKeys={jsonKeys}
                    dbTableName={dbTableName}
                    tagsData={tagsData}
                    editableFields={schemaData.editableFields}
                />
            )}
        </UpdateTableRowDrawer>
    );
};
