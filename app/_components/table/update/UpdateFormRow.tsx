import React, { useEffect, useState } from 'react';
import validator from '@rjsf/validator-ajv8';
import { withTheme } from "@rjsf/core";
import { Theme as AntDTheme } from '@rjsf/antd';
import Box from "@mui/joy/Box";
import MultiSelectWidget from "@/app/_components/table/update/MultipleSelectChipWidget";

const ThemedForm = withTheme(AntDTheme);

const UpdateForm: React.FC<any> = ({ schemaData, formData, handleSubmit, jsonKeys, tagsData, editableFields }) => {
    const [adjustedFormData, setAdjustedFormData] = useState(formData);

    const mapTagNamesToIds = (tagNames: string[], tagsData: any[]) => {
        if (!Array.isArray(tagsData)) {
            return [];
        }

        return tagNames.map(tagName => {
            const tag = tagsData.find(tag => tag.name === tagName.trim());
            return tag ? tag._id : null;
        }).filter(Boolean);
    };

    const adjustFormData = (data: any) => {
        const adjustedData = { ...data };

        if (typeof adjustedData.tags_names === 'string') {
            const tagNamesArray = adjustedData.tags_names.split(', ');
            adjustedData.tags_names = mapTagNamesToIds(tagNamesArray, tagsData);
        } else if (Array.isArray(adjustedData.tags_names)) {
            adjustedData.tags_names = mapTagNamesToIds(adjustedData.tags_names, tagsData);
        }

        return adjustedData;
    };

    useEffect(() => {
        const updatedData = adjustFormData(formData);
        Object.keys(updatedData).forEach(key => {
            if (updatedData[key] && typeof updatedData[key] === 'object' && updatedData[key].$date) {
                updatedData[key] = new Date(updatedData[key].$date);
            } else if (typeof updatedData[key] === 'string' && !isNaN(Date.parse(updatedData[key]))) {
                updatedData[key] = new Date(updatedData[key]);
            }
        });

        setAdjustedFormData(updatedData);
    }, [formData, tagsData]);

    const generateUiSchema = (fields, editableFields) => {
        const uiSchema = {
            "ui:submitButtonOptions": {
                "submitText": "Submit",
                "norender": false,
                "props": {
                    "type": "primary",
                },
            },
            tags_names: {
                "ui:widget": "MultiSelectWidget",
                "ui:options": {
                    tagsData: Array.isArray(tagsData) ? tagsData : []
                },
                "ui:disabled": !editableFields?.includes("tags_names")
            }
        };
        fields.forEach(field => {
            uiSchema[field] = {
                ...uiSchema[field],
                "ui:disabled": !editableFields?.includes(field)
            };
        });

        return uiSchema;
    };

    const widgets = {
        MultiSelectWidget
    };

    const uiSchema = generateUiSchema(Object.keys(schemaData.properties), editableFields);

    return (
        <Box sx={{ overflowX: 'hidden' }}>
            <ThemedForm
                schema={schemaData}
                formData={adjustedFormData}
                widgets={widgets}
                validator={validator}
                onSubmit={handleSubmit}
                uiSchema={uiSchema}
            />
        </Box>
    );
};

export default UpdateForm;
