export const buildUpdateSchemaRow = (dbOptionsColumns) => {
    const properties = {};
    const editableFields = [];

    dbOptionsColumns.forEach(column => {
        const { accessorKey, header, config, filterComponent } = column;
        if (!config?.show || accessorKey === 'actions') {
            return;
        }

        let type;
        let format;
        let title;
        let enumOpt;
        if (config?.editable) {
            editableFields.push(accessorKey);
        }
        if (accessorKey === 'retained') {
            type = 'boolean';
        } else if (accessorKey === 'tags_names') {
            type = 'array';
            title = 'Tags';
        } else if (config?.component) {
            switch (config.component) {
                case 'TextFilterComponent':
                    type = 'string';
                    break;
                case 'ArrayDataComponent':
                    type = 'array';
                    break;
                case 'NumericComponent':
                    type = 'number';
                    break;
                default:
                    type = 'string';
            }
        } else if (filterComponent) {
            switch (filterComponent) {
                case 'TextFilterComponent':
                    type = 'string';
                    break;
                case 'MultiSelectFilterComponent':
                    type = 'string';
                    title = 'Select';
                    enumOpt = config.selectOptions.map(option => option.label);
                    break;
                case 'SelectFilterComponent':
                    type = 'string';
                    title = 'Select';
                    enumOpt = config.selectOptions.map(option => option.value);
                    break;
                case 'RangeNumberFilterComponent':
                    type = 'number';
                    break;
                case 'ArrayDataComponent':
                    type = 'array';
                    break;
                case 'DateFilterComponent':
                    type = 'string';
                    format = 'date-time';
                    break;
                case 'BooleanFilterComponent':
                    type = 'boolean';
                    break;
                default:
                    type = 'string';
            }
        } else {
            type = 'string';
        }
        properties[accessorKey] = { title: header };

        if (type === 'array') {
            properties[accessorKey].type = 'array';
            properties[accessorKey].items = { type: 'string', default: '' };
        } else if (type === 'string' && format === 'date-time') {
            properties[accessorKey].type = type;
            properties[accessorKey].format = format;
        } else if (type === 'string' && title === 'Select') {
            properties[accessorKey].type = type;
            properties[accessorKey].enum = enumOpt;
        } else {
            properties[accessorKey].type = type;
        }
        if (accessorKey === 'tags_names') {
            properties[accessorKey].title = 'Tags';
        }
    });

    return {
        type: 'object',
        properties: properties,
        editableFields: editableFields
    };
};
