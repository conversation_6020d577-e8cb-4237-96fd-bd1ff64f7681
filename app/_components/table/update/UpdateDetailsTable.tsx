import React from 'react';
import validator from '@rjsf/validator-ajv8';
import { withTheme } from "@rjsf/core";
import { Theme as AntDTheme } from '@rjsf/antd';

const ThemedForm = withTheme(AntDTheme);

const UpdateForm: React.FC<any> = ({ schemaData, formData, handleSubmit }) => {
    return (
        <ThemedForm
            schema={schemaData}
            formData={formData}
            validator={validator}
            onSubmit={handleSubmit}
        />
    );
};

export default UpdateForm;
