export const buildFormData = (dbOptionsColumns, rowValues) => {
    const properties = {};
    const jsonAccessorKeys = [];

    dbOptionsColumns.forEach(column => {
        const { accessorKey, config } = column;
        if (!config?.editable && accessorKey === 'actions') {
            return;
        }
        let value = rowValues[accessorKey];
        if (config?.component === 'JsonDataComponent' && typeof value === 'object') {
            try {
                value = JSON.stringify(value);
                jsonAccessorKeys.push(accessorKey);
            } catch (e) {
                console.error(`Error stringifying JSON for accessorKey ${accessorKey}:`, e);
            }
        }
        properties[accessorKey] = value;
    });

    return { properties, jsonAccessorKeys };
};
