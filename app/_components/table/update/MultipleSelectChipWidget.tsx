import React from 'react';
import { Autocomplete, Chip } from '@mui/joy';
import { WidgetProps } from "@rjsf/utils";

interface OptionType {
    value: string;
    label: string;
}

const MultiSelectWidget: React.FC<WidgetProps> = (props) => {
    const { schema, value = [], onChange, uiSchema } = props;

    const options = Array.isArray(uiSchema["ui:options"]?.selectItemsData) ? uiSchema["ui:options"].selectItemsData : [];
    const transformedOptions: OptionType[] = options.map(item => ({
        value: item.value,
        label: item.label
    }));

    const handleChange = (event: React.ChangeEvent<{}>, newValue: OptionType[]) => {
        const selectedValues = newValue.map(option => option.value);
        onChange(selectedValues);
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
        if (event.key === 'Enter') {
            event.preventDefault();
        }
    };

    const getOptionLabel = (option: string | OptionType): string => {
        if (typeof option === 'string') {
            const matchedOption = transformedOptions.find(opt => opt.value === option);
            return matchedOption ? matchedOption.label : option;
        }
        return option.label;
    };

    return (
        <Autocomplete
            multiple
            options={transformedOptions}
            value={transformedOptions.filter(option => value.includes(option.value))}
            onChange={handleChange}
            getOptionLabel={getOptionLabel}
            onKeyDown={handleKeyDown}
            renderTags={(value, getTagProps) =>
                value.map((option, index) => {
                    const { key, ...tagProps } = getTagProps({ index });
                    const optionLabel = typeof option === 'string' ? option : option.label;
                    const optionValue = typeof option === 'string' ? option : option.value;
                    return (
                        <Chip
                            key={optionValue}
                            {...tagProps}
                        >
                            {optionLabel}
                        </Chip>
                    );
                })
            }
            placeholder={value.length === 0 ? "Select" : ""}
        />
    );
};

export default MultiSelectWidget;
