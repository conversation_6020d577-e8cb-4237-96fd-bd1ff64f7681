import React, {useEffect, useState} from 'react';
import validator from '@rjsf/validator-ajv8';
import {withTheme} from "@rjsf/core";
import {Theme as AntDTheme} from '@rjsf/antd';
import Box from "@mui/joy/Box";
import MultiSelectWidget from "@/app/_components/table/update/MultipleSelectChipWidget";

const ThemedForm = withTheme(AntDTheme);

const UpdateForm: React.FC<any> = ({ schemaData, formData, handleSubmit, editableRow, jsonKeys, tagsData }) => {
    const [adjustedFormData, setAdjustedFormData] = useState(formData);

    const mapTagNamesToIds = (tagNames: string[], tagsData: any[]) => {
        if (!Array.isArray(tagsData)) return [];
        return tagNames.map(tagName => {
            const tag = tagsData.find(tag => tag.name === tagName.trim());
            return tag ? tag._id : null;
        }).filter(Boolean);
    };

    const adjustFormData = (data: any) => {
        const adjustedData = { ...data };
        if (typeof adjustedData.tags_names === 'string') {
            const tagNamesArray = adjustedData.tags_names.split(', ');
            adjustedData.tags_names = mapTagNamesToIds(tagNamesArray, tagsData);
        } else if (Array.isArray(adjustedData.tags_names)) {
            adjustedData.tags_names = mapTagNamesToIds(adjustedData.tags_names, tagsData);
        }
        if ('pub_ids' in adjustedData && adjustedData.pub_ids === undefined) {
            adjustedData.pub_ids = [];
        } else if (Array.isArray(adjustedData.pub_ids)) {
            adjustedData.pub_ids = adjustedData.pub_ids.map(pub => ({
                id: pub.id || '',
                leadStrategy: pub.leadStrategy || 'match',
            }));
        }
        return adjustedData;
    };

    useEffect(() => {
        const updatedData = adjustFormData(formData);
        Object.keys(updatedData).forEach(key => {
            if (updatedData[key] && typeof updatedData[key] === 'object' && updatedData[key].$date) {
                updatedData[key] = new Date(updatedData[key].$date);
            } else if (typeof updatedData[key] === 'string' && !isNaN(Date.parse(updatedData[key]))) {
                updatedData[key] = new Date(updatedData[key]);
            }
        });

        setAdjustedFormData(updatedData);
    }, [formData, tagsData]);

    const generateUiSchema = () => {
        return {
            "ui:submitButtonOptions": {
                "submitText": "Submit",
                "norender": false,
                "props": {
                    "type": "primary",
                },
            },
            tags_names: {
                "ui:widget": "MultiSelectWidget",
                "ui:options": {
                    tagsData: Array.isArray(tagsData) ? tagsData : []
                }
            },
            ...(adjustedFormData.pub_ids !== undefined ? {
                pub_ids: {
                    "items": {
                        id: {
                            "ui:widget": "text",
                            "ui:placeholder": "Enter ID"
                        },
                        leadStrategy: {
                            "ui:widget": "select",
                            "ui:options": {
                                enumOptions: [
                                    {label: "Match", value: "match"},
                                    {label: "Create", value: "create"}
                                ]
                            }
                        }
                    }
                }
            } : {})
        };
    };

    const schema = {
        ...schemaData,
        properties: {
            ...schemaData.properties,
            ...(adjustedFormData.pub_ids !== undefined ? {
                pub_ids: {
                    type: "array",
                    title: "Pub IDs",
                    items: {
                        type: "object",
                        properties: {
                            id: { type: "string", title: "ID" },
                            leadStrategy: {
                                type: "string",
                                title: "Lead Strategy",
                                enum: ["match", "create"],
                                default: "match"
                            }
                        }
                    }
                }
            } : {})
        }
    };

    const widgets = {
        MultiSelectWidget
    };

    const uiSchema = generateUiSchema();

    return (
        <Box sx={{ overflowX: 'hidden' }}>
            <ThemedForm
                disabled={editableRow}
                schema={schema}
                formData={adjustedFormData}
                widgets={widgets}
                validator={validator}
                onSubmit={handleSubmit}
                uiSchema={uiSchema}
            />
        </Box>
    );
};

export default UpdateForm;
