import _ from "lodash";
import {styledName} from "@/app/_components/utils/styledName.js";
import {sortByNameSelectElements} from "@/app/_components/table/functions/sortByNameSelectElements";
import {DateTime} from "luxon";

export const buildUpdateSchema = (dbOptionsColumns, editableRow) => {
    const properties = {};
    const uiSchema = {};
    const definitions = {};
    const accKeysReplace = []
    dbOptionsColumns.forEach(column => {
        const {accessorKey, header, config, filterComponent} = column;
        if (!config?.editable && editableRow || accessorKey === 'actions') {
            return;
        }
        let type;
        let format;
        let title;
        let enumOpt;
        let builtSel;
        if (!_.isEmpty(config?.reference)) {
            builtSel = `${config?.reference}Selector`
            accKeysReplace.push({keyFind: accessorKey, keyRep: builtSel});
        }
        const switchComponent = config?.component || filterComponent
        switch (switchComponent) {
            case 'TextFilterComponent':
                type = 'string';
                break;
            case 'MultiSelectFilterComponent':
            case 'SelectFilterComponent':
                type = 'string';
                title = 'Select';
                enumOpt = config.selectOptions.map(option => option.value);
                if (config.selectOptions && config.selectOptions.length > 0 &&
                    !_.isEmpty(builtSel) && !_.has(builtSel, builtSel)) {
                    const sortedElem = sortByNameSelectElements(config.selectOptions)
                    const enumObj = []
                    const enumNam = []
                    sortedElem.forEach(opt => {
                        enumObj.push(opt)
                        enumNam.push(opt.label)
                    })
                    definitions[builtSel] = {
                        enumNames: enumNam,
                        enum: enumObj
                    };
                    properties[builtSel] = {
                        "title": `${styledName(config?.reference)} Selector`,
                        "$ref": `#/definitions/${builtSel}`
                    }
                }
                break;
            case 'RangeNumberFilterComponent':
                type = 'number';
                break;
            case 'ArrayDataComponent':
                type = 'array';
                break;
            case 'DateFilterComponent':
                type = 'string';
                format = 'date-time';
                break;
            case 'NumericComponent':
                type = 'number';
                break;
            case 'BooleanFilterComponent':
                type = 'boolean';
                break;
            default:
                type = 'string';
        }

        properties[accessorKey] = {title: header};

        if (type === 'array') {
            properties[accessorKey].type = 'array';
            properties[accessorKey].items = {type: 'string'};
        } else if (type === 'string' && format === 'date-time') {
            properties[accessorKey].type = type;
            properties[accessorKey].format = format;
            if (!editableRow && config?.dateNow) {
                const myTimeZone = 'America/Chicago';
                properties[accessorKey].default = DateTime.now().setZone(myTimeZone).toFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZZ")
                uiSchema[accessorKey] = {"ui:widget": "hidden"}
            }
        } else if (type === 'string' && title === 'Select') {
            properties[accessorKey].type = type;
            if (!editableRow && config?.canAdd) {
                properties[accessorKey].enum = enumOpt;
                uiSchema[accessorKey] = {
                    "ui:widget": "customSelectWidget"
                }
            } else {
                properties[accessorKey].enum = enumOpt;
            }
        } else {
            properties[accessorKey].type = type;
        }

        if (_.includes(accessorKey, "email")) {
            properties[accessorKey].format = "email";
            uiSchema[accessorKey] = {"ui:widget": "email"}
        } else if (_.includes(accessorKey, "phone")) {
            properties[accessorKey].pattern = "^[0-9]+$";
            properties[accessorKey].maxLength = 10;
            properties[accessorKey].minLength = 10;
            uiSchema[accessorKey] = {"ui:widget": "text"}
        }

        if (!editableRow) {
            if (!config?.newReg) {
                // properties[accessorKey].default = null;
                properties[accessorKey] = {default: null};
                if (properties[accessorKey].enum) properties[accessorKey] = _.omit(properties[accessorKey], "enum")
                uiSchema[accessorKey] = {"ui:widget": "hidden"}
            }
            if (config?.reference && !_.isEmpty(config?.reference)) {
                uiSchema[accessorKey] = {"ui:widget": "hidden"}
            }
        }
    });

    const schema = {
        definitions,
        type: 'object',
        properties: properties
    };

    return (editableRow) ? schema : {
        schema,
        uiSchema,
        accKeysReplace
    };
}
