'use client'
import {flexRender} from "@tanstack/react-table";
import React from "react";
import dynamic from "next/dynamic";

const Filter: any = dynamic(() => import('@/app/_components/table/Filter'), {ssr: false})

const TableHead: React.FC<any> = ({table, dbTableName}) => {
    return (
        <>
            {table.getHeaderGroups().map((headerGroup: any) => (
                <tr key={headerGroup.id}>
                    {headerGroup.headers.map((header: any) => (
                        <th key={header.id}
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div style={{minWidth: '150px'}}>
                                {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                        header.column.columnDef.header,
                                        header.getContext()
                                    )}
                            </div>
                            <div>
                                <Filter column={header.column} table={table} dbTableName={dbTableName}
                                        filterComponent={header.column.columnDef.meta?.filterComponent}/>
                            </div>
                        </th>
                    ))}
                </tr>
            ))}
        </>
    )
}

export default TableHead;

