import {GeneratePubidComponent} from "@/app/_components/table/actions/GeneratePubidComponent";

export const RowActionComponentsFactory = ({component, componentProps}) => {

    const actionComponents: any = {
        'GeneratePubidComponent': GeneratePubidComponent
    }

    const SelectedComponent = actionComponents[component];

    return (<>
        {SelectedComponent ? <SelectedComponent {...componentProps} /> : <></>}
    </>)
}
