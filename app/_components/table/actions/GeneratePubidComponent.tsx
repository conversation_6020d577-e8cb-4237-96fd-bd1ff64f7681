import {Button, Input, Modal, Tooltip} from "@mui/joy";
import React, {useState} from "react";
import ModalDialog from "@mui/joy/ModalDialog";
import ModalClose from "@mui/joy/ModalClose";
import Typography from "@mui/joy/Typography";
import {generateId} from "@/app/_lib/utils/generateId";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {CampaignKeysUseQuery} from "@/app/_components/queries/CampaignKeysUseQuery";
import Box from "@mui/joy/Box";
import Loading from "react-loading";
import Select from "@mui/joy/Select";
import Option from "@mui/joy/Option";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import copy from "copy-to-clipboard";

export const GeneratePubidComponent = (componentProps) => {
    const [openModal, setOpenModal] = useState(false)
    const [generatingPubid, setGeneratingPubid] = useState(false)
    const [campaignKey, setCampaignKey] = useState('')
    const [leadStrategy, setLeadStrategy] = useState('match')
    const [pubId] = useState(generateId())
    const {data: campaignKeys, isFetching} = useQuery(CampaignKeysUseQuery(true));
    const queryClient = useQueryClient();

    const onOpenModal = () => {
        setOpenModal(true);
    }

    const closeModal = () => {
        setOpenModal(false)
    }

    const copyPubid = () => {
        copy(pubId)
    }

    const onCloseModal=()=>{
      setCampaignKey('')
    }

    const generatePubid = async () => {
        const {_id: vendorId} = componentProps;
        setGeneratingPubid(true)
        const getRespTemplate = await fetch("/api/mongo/vendors/generatePubId", {
            method: 'POST',
            body: JSON.stringify({vendorId, pubId, campaignKey, leadStrategy}),
        });
        if (getRespTemplate.ok) {
            showSuccess("The pubid was generated successfully");
            await queryClient.invalidateQueries({queryKey: ["table_data_vendors"]});
            setOpenModal(false);

        } else {
            const {message} = await getRespTemplate.json();
            showError(message || "Something wrong happened")
        }
        setGeneratingPubid(false)
    }

    return (
        <>
            <Button variant="outlined" color="neutral" onClick={onOpenModal}>Generate pubid</Button>
            {openModal &&
                <Modal
                    aria-labelledby="modal-title"
                    aria-describedby="modal-desc"
                    onClose={onCloseModal}
                    open={openModal}
                    sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
                    <ModalDialog size="lg" variant="outlined" sx={{padding: '30px', width: '400px'}}>
                        <ModalClose onClick={() => closeModal()}/>
                        {isFetching &&
                            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
                                <Loading color={'black'} width={'30px'} height={'30px'} type={'spin'}/>
                                <Typography level="title-md" fontWeight="bold"
                                            sx={{textAlign: 'center', marginLeft: '10px'}}>Loading...</Typography>
                            </Box>
                        }
                        {!isFetching && <Box>
                            <Box>
                                <Typography level="title-md" fontWeight="bold" sx={{mt: 1, textAlign: 'center'}}>
                                    Generating pubid
                                </Typography>
                            </Box>
                            <Box>
                                <Typography level="title-sm" fontWeight="bold" sx={{mt: 1}}>
                                    Pubid
                                    <Tooltip title="Copy pub id" variant="soft" placement="right">
                                        <ContentCopyIcon sx={{marginLeft: '5px', cursor: 'pointer'}}
                                                         onClick={copyPubid}/>
                                    </Tooltip>
                                </Typography>
                                <Input
                                    name={'pubid'}
                                    sx={{mt: 1}}
                                    type={'text'}
                                    disabled={true}
                                    value={pubId}
                                />
                            </Box>
                            <Box>
                                <Typography level="title-sm" fontWeight="bold" sx={{mt: 2}}>
                                    Lead strategy
                                </Typography>
                                <Select
                                    name="leadStrategy"
                                    required
                                    value={leadStrategy}
                                    onChange={(e, value) => setLeadStrategy(value)}
                                    sx={{minWidth: 200}}>
                                    <Option key={`lead_strategy_match`} value={'match'}>Match</Option>
                                    <Option key={`lead_strategy_create`} value={'create'}>Create</Option>
                                </Select>

                                <Typography level="title-sm" fontWeight="bold" sx={{mt: 2}}>
                                    Campaign key
                                </Typography>
                                <Select placeholder="Select a campaign key"
                                        name="campaignKey"
                                        required
                                        value={campaignKey}
                                        onChange={(e, value) => setCampaignKey(value)}
                                        sx={{minWidth: 200}}>
                                    {campaignKeys.map((campaign, index) => (
                                        <Option key={`campaign_select_${index}`} value={campaign._id}>
                                            {campaign.name}
                                        </Option>
                                    ))}
                                </Select>

                                <Box sx={{mt: 3, textAlign: 'right'}}>
                                    <Button disabled={!campaignKey}
                                            variant='outlined'
                                            loading={generatingPubid}
                                            color='neutral' sx={{height: '30px'}}
                                            onClick={generatePubid}>Generate</Button>
                                </Box>
                            </Box>
                        </Box>}
                    </ModalDialog>
                </Modal>
            }
        </>)
}
