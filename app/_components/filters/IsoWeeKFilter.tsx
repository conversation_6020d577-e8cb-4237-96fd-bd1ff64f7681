'use client'

import React, {useEffect, useState} from 'react'
import {But<PERSON>, Option, Select} from "@mui/joy";
import {getISOWeeksInMonth} from "@/app/_components/utils/getISOWeeksInMonth";
import {getDefaultIsoWeek} from "@/app/_components/utils/getDefaultIsoWeek";
import PubSub from 'pubsub-js'
import {pubSubEvents} from "@/app/_lib/utils/pubsub/pubSubEvents";


export default function ISOWeekFilter({
                                          onFilter,
                                          showApplyFiltersButton,
                                          defaultIsoWeek,
                                          showAllWeeks,
                                          destroyComponent,
                                          showAllOnChange = false
                                      }: {
    onFilter: (filter: { year: number, month: number, week: number[] }) => void,
    showApplyFiltersButton: boolean,
    defaultIsoWeek: any,
    showAllWeeks: boolean,
    destroyComponent: any,
    showAllOnChange: boolean
}) {
    const currentDate = getDefaultIsoWeek(defaultIsoWeek);
    const [year, setYear] = useState(defaultIsoWeek === 'NA' ? -1 : currentDate.year)
    const [month, setMonth] = useState(defaultIsoWeek === 'NA' ? -1 : currentDate.month - 1)
    const [week, setWeek] = useState(defaultIsoWeek === 'NA' ? -1 : defaultIsoWeek === 'all' ? 'all' : currentDate.weekNumber.toString())
    const [weeks, setWeeks] = useState<[number, string][]>([])
    const years = Array.from({length: 10}, (_, i) => currentDate.year - 5 + i);
    const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ]
    useEffect(() => {
        clearFiltersSubscribe();
    }, []);
    useEffect(() => {
        if (year !== -1 && month !== -1) {
            let isoWeeks = getISOWeeksInMonth(year, month);
            const isoWeekYear = getYearFromWeekSelected(isoWeeks);
            if (isoWeekYear != 0 && year != isoWeekYear && destroyComponent) {
                destroyComponent({year: isoWeekYear, month, week});
                return
            }
            setWeeks(isoWeeks);
            if (week !== -1) {
                const isValidWeek = isoWeeks.some(([weekNum]) => weekNum.toString() === week);
                if (!isValidWeek) {
                    const newWeek = showAllWeeks ? 'all' : isoWeeks[0][0];
                    setWeek(newWeek);
                }
                if (defaultIsoWeek?.all) {
                    setWeek('all');
                }
                if (!showApplyFiltersButton) {
                    handleFilter(isoWeeks);
                }
            }
        }
    }, [year, month, week])

    const clearFiltersSubscribe = () => {
        PubSub.subscribe(pubSubEvents.CLEAR_FILTERS, () => {
            setYear(-1)
            setMonth(-1)
            setWeek(-1)
        });
    }

    const handleFilter = (isoWeeks) => {
        let splitedYear = getYearFromWeekSelected(isoWeeks)
        onFilter({
            year: splitedYear,
            month,
            week: week === 'all' ? isoWeeks.map(([weekNum]) => weekNum) : [Number(week)]
        })
    }

    const getYearFromWeekSelected = (isoWeeks) => {
        const selectedWeek = isoWeeks.find(item => item[0] == week);
        const splitYear = week === 'all' ? year : selectedWeek && selectedWeek.length > 1 ? selectedWeek[1].split('-')[0] : null;
        return Number(splitYear);
    }
    const handleChangeYear = (year) => {
        setMonth(0)
        setYear(year)
        const week = showAllOnChange ? 'all': -1
        setWeek(week)
    }

    const handleChangeMonth = (month) => {
        const week = showAllOnChange ? 'all': -1
        setWeek(week)
        setMonth(month)
    }

    return (
        <div className="flex flex-wrap items-center gap-4">
            <Select
                value={year.toString()}
                style={{maxWidth: '300px'}}
                onChange={(event, value) => handleChangeYear(parseInt(value))}>
                <Option key={'na-filter-year'} value={'-1'}>Select Year</Option>
                {years && years.map((year: any, index: number) => (
                    <Option key={index} value={year.toString()}>{year}</Option>))}
            </Select>
            <Select value={month.toString()} onChange={(event, value) => handleChangeMonth(parseInt(value))}>
                <Option key={'na-filter-month'} value={'-1'}>Select Month</Option>
                {months.map((m, index) => (
                    <Option key={index} value={index.toString()}>{m}</Option>
                ))}
            </Select>

            <Select value={week?.toString()} onChange={(event, value) => setWeek(value)}>
                <Option key={'na-filter-week'} value={'-1'}>Select Week</Option>
                {showAllWeeks && <Option value={'all'}>All Weeks</Option>}
                {weeks.map(([weekNum, weekLabel]) => (
                    <Option key={weekNum} value={weekNum.toString()}>{weekLabel}</Option>
                ))}
            </Select>
            {showApplyFiltersButton && year != -1 && month != -1 && week != -1 &&
                <Button color={'neutral'} variant="outlined" onClick={handleFilter}>Apply Filter</Button>
            }
        </div>
    )
}
