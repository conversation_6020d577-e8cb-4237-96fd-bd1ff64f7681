import React from "react";
import ChipDelete from '@mui/joy/ChipDelete';
import {Chip} from "@mui/joy";
import {accountingInvoiceIds, accountingInvoiceLabels} from "@/app/_lib/_states/accounting/accountingReportInvoices.js";
import {createTableEntities} from "@/app/_components/table/states/tableDataEntity.js";
import {cleanAccountingInvoiceFilter} from "@/app/_lib/utils/filters/index.js";


export const InvoiceAccountingFilter = () => {
    const {
        setFiltersEntity,
    } = createTableEntities('invoices');
    const onDeleteFilterAccounting = () => {
        cleanAccountingInvoiceFilter()
        setFiltersEntity({})
    }
    return <>
        {accountingInvoiceIds.get().length > 0 &&
            <Chip
                size="sm"
                variant="outlined"
                color="primary"
                endDecorator={<ChipDelete onDelete={() => onDeleteFilterAccounting()}/>}>
                {accountingInvoiceLabels.get()}
            </Chip>
        }
    </>
}
