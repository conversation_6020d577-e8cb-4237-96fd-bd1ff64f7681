import {InvoiceIsoWeekFilter} from "@/app/_components/filters/custom/InvoiceIsoWeekFilter.jsx";
import {InvoiceAccountingFilter} from "@/app/_components/filters/custom/InvoiceAccountingFilter.jsx";

export const CustomFilters = ({component, defaultFilter, onFilter, hasSearchParams}) => {
    switch (component) {
        case 'InvoiceIsoWeekFilter':
            return <InvoiceIsoWeekFilter defaultFilter={defaultFilter}
                                         showApplyFiltersButton={false}
                                         hasSearchParams={hasSearchParams}
                                         onFilter={onFilter}/>;
        case "InvoiceAccountingFilter":
            return <InvoiceAccountingFilter/>;
    }
}
