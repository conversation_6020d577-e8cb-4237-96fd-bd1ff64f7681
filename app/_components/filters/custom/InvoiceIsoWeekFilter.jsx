import ISOWeekFilter from "@/app/_components/filters/IsoWeeKFilter";
import React, {useState} from "react";

export const InvoiceIsoWeekFilter = ({onFilter, showApplyFiltersButton}) => {

    const [showIsoWeekFilter, setShowIsoWeekFilter] = useState(true);
    const [defaultIsoWeek, setDefaultIsoWeek] = useState('NA');

    const destroyComponent = (event) => {
        setShowIsoWeekFilter(false);
        setDefaultIsoWeek({weekYear: event.year, weekNumber: 1, weekday: 5})
        setTimeout(() => {
            setShowIsoWeekFilter(true)
        }, 500)
    }
    return <>
        {showIsoWeekFilter &&
            <ISOWeekFilter defaultIsoWeek={defaultIsoWeek} onFilter={onFilter}
                           showApplyFiltersButton={showApplyFiltersButton} showAllWeeks={true}
                           destroyComponent={destroyComponent} showAllOnChange={false}/>
        }
    </>
}
