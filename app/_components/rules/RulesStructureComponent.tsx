import React from "react";
import {<PERSON>rid, <PERSON>, Step<PERSON>ndicator, <PERSON><PERSON>} from "@mui/joy";
import Typography from "@mui/joy/Typography";
import _ from "lodash";
import Sheet from "@mui/joy/Sheet";

export const RulesStructureComponent: React.FC<any> = ({stepperData}) => {

    return(
        <Stepper sx={{width: '100%'}} orientation="vertical">
            {stepperData.map((stepInf: any, index: number) => (
                <Step
                    key={`step_${index}`}
                    indicator={
                        <StepIndicator
                            variant={'soft'}
                            color={'neutral'}>
                            {index + 1}
                        </StepIndicator>
                    }>
                    <Typography level="h4">
                        {stepInf?.title}
                    </Typography>
                    <Typography level="title-md">
                        {stepInf?.subTitle}
                    </Typography>
                    <Grid container spacing={2}>
                        {(!_.isEmpty(stepInf?.data)) && stepInf?.data.map((dataInf: any, index: number) => (
                            <Grid key={`stack_step_${index}`}
                                  xs={12}
                                  md={(stepInf?.data.length === 1) ? 12 : (stepInf?.data.length >= 3) ? 4 : 6}
                                  lg={(stepInf?.data.length === 1) ? 12 :(stepInf?.data.length >= 3) ? 4 : 6}>
                                <Sheet
                                    variant="outlined"
                                    sx={{
                                        borderRadius: 'md',
                                        p: "10px",
                                    }}>
                                    <Typography level="body-sm">
                                        {dataInf?.title && <>{dataInf?.title} <br/></>}
                                        {dataInf?.description && <>{dataInf?.description}
                                            <br/></>}
                                        {dataInf?.description2 && <>{dataInf?.description2}
                                            <br/></>}
                                        {dataInf?.description3 && <>{dataInf?.description3}
                                            <br/></>}
                                    </Typography>
                                </Sheet>
                            </Grid>
                        ))}
                    </Grid>
                </Step>
            ))}
        </Stepper>
    )
}
