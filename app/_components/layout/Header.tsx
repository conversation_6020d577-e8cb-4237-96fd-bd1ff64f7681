import * as React from 'react';
import IconButton from '@mui/joy/IconButton';
import MenuIcon from '@mui/icons-material/Menu';
import {toggleSidebar} from '../../utils';
import {Box} from '@mui/joy';
import {HeaderUserButton} from "@/app/_components/layout/HeaderUserButton";
import {OrganizationsSwitcher} from "@/app/_components/layout/OrganizationsSwitcher";

export default function Header({user}) {
    return (<>
        <IconButton
            onClick={() => toggleSidebar()}
            variant="outlined"
            color="neutral"
            size="sm"
            sx={{
                position: 'fixed',
                top: '10px',
                left: '10px',
                zIndex: 95,
                bgcolor: 'white'
            }}>
            <MenuIcon/>
        </IconButton>

        <Box sx={{
            position: 'fixed',
            top: '10px',
            right: '10px',
            zIndex: 95,
            display: 'flex',
            flexDirection: 'row',
            gap: 1
        }}>
            <HeaderUserButton user={user}/>
            {(user?.role === 'admin' && user?.dev) &&
                <OrganizationsSwitcher user={user}/>
            }
        </Box>
    </>);
}
