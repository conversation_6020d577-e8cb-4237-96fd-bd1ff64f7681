import {DateTime} from "luxon";

export const getCurrentWeekRange = () => {
    const currentDate = DateTime.now();
    const currentWeekNumber = currentDate.weekNumber;
    const currentWeekYear = currentDate.weekYear;
    const startOfWeek = DateTime.fromObject({weekYear: currentWeekYear, weekNumber: currentWeekNumber, weekday: 1});
    const endOfWeek = startOfWeek.plus({days: 6});
    return {
        start: startOfWeek.toISODate(),
        end: endOfWeek.toISODate()
    };
}
