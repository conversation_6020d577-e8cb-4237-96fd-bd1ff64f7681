import _ from "lodash";

export function getMenusWithToolsAvailable(metadataMenus) {
    const getMenusMetadata = metadataMenus ?? {}
    if (!_.isEmpty(getMenusMetadata)) {
        const allMenus = []
        for (const key in getMenusMetadata) {
            if (!_.isEmpty(getMenusMetadata[key])) {
                allMenus.push(key)
            }
        }
        return allMenus
    } else {
        return []
    }
}