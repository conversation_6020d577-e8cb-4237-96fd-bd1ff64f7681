const fileTypeMap = {
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    csv: 'text/csv',
    txt: 'text/plain',
    html: 'text/html',
    htm: 'text/html',
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
};


export const buildAcceptObject = (fileTypes) => {
    if (fileTypes && fileTypes.length === 1 && fileTypes[0] === '*') {
        return {};
    }

    return fileTypes?.reduce((acc, ext) => {
        const mimeType = fileTypeMap[ext];
        if (mimeType) {
            if (!acc[mimeType]) {
                acc[mimeType] = [];
            }
            acc[mimeType].push(`.${ext}`);
        }
        return acc;
    }, {});
};
