'use client'
import Box from "@mui/material/Box";
import {<PERSON><PERSON>} from "@mui/joy";
import {useDropzone} from "react-dropzone";
import React, {useCallback, useState} from "react";
import {buildAcceptObject} from "@/app/_components/utils/dragAndDropFiles/fileTypeMap.js";
import {CheckCircleOutline, Close, CloudUpload, ErrorOutline} from '@mui/icons-material';
import Typography from "@mui/joy/Typography";
import axios from "axios";
import LinearProgressCountUp from "@/app/_components/utils/LinearProgressCountUp.js";
import LinearProgress from "@mui/joy/LinearProgress";

const STATUS = {
    GENERATE_URL: "GENERATE_URL",
    UPLOAD_FILE: "UPLOAD_FILE",
    UPLOADING_FILE: "UPLOADING_FILE",
    SUCCESS_UPLOAD: "SUCCESS_UPLOAD",
    ERROR: "ERROR",
    ERROR_GENERATE_URL: "ERROR_GENERATE_URL",
    POST_VALIDATION: "POST_VALIDATION"
};

export const UploadFileComponent = ({
                                        fileTypes,
                                        urlConfiguration,
                                        onSuccess,
                                        onError,
                                        onGenerateUrl,
                                        onUploading,
                                        postValidations
                                    }) => {
    const [errors, setErrors] = useState([]);
    const [urlError, setUrlError] = useState("");
    const [status, setStatus] = useState(STATUS.GENERATE_URL);
    const [loading, setLoading] = useState(false);
    const [uploadLoading, setUploadLoading] = useState(false);
    const [file, setFile] = useState(null);
    const [urlUpload, setUrlUpload] = useState("");
    const [uploadProgress, setUploadProgress] = useState(0);
    const [errorMessage, setErrorMesssage] = useState('')
    const accept = buildAcceptObject(fileTypes);
    const onDrop = useCallback((acceptedFiles, fileRejections) => {
        setErrors([]);
        if (fileRejections.length > 0) {
            const rejectionErrors = fileRejections.flatMap(({errors}) => errors.map(err => err.message));
            setErrors(rejectionErrors);
        } else {
            setFile(acceptedFiles[0]);
        }
    }, []);

    const {getRootProps, getInputProps, isDragActive} = useDropzone({onDrop, accept});

    const generateUrl = async () => {
        setLoading(true);
        try {
            const response = await fetch("/api/file/generateUrl", {
                method: 'POST',
                body: JSON.stringify(urlConfiguration),
                headers: {'Content-Type': 'application/json'},
            });

            if (response.ok) {
                const {urlFile} = await response.json();
                setUrlUpload(urlFile);
                setStatus(STATUS.UPLOAD_FILE);
                onGenerateUrl?.();
            } else {
                const {error} = await response.json();
                setUrlError(error || 'Error on generate Url.');
                setStatus(STATUS.ERROR_GENERATE_URL);
            }
        } catch (e) {
            console.error("Error on generate url file", e);
            setUrlError('Error on generate Url.');
            setStatus(STATUS.ERROR_GENERATE_URL);
        } finally {
            setLoading(false);
        }
    };

    const uploadFile = async () => {
        setUploadLoading(true);
        if (onUploading) onUploading()
        setStatus(STATUS.UPLOADING_FILE)
        axios.put(urlUpload, file, {
            onUploadProgress: (progressEvent) => {
                const percentage = Math.round(
                    (progressEvent.loaded * 100) / progressEvent.total
                );
                setUploadProgress(percentage);
            }
        })
            .then((response) => {
                if (response.status === 200) {
                    executePostValidation()
                } else {
                    setStatus(STATUS.ERROR);
                    setErrorMesssage('Error uploading file.')
                    onError?.();
                }
            })
            .catch((error) => {
                console.error("Error uploading file", e);
                setStatus(STATUS.ERROR);
                setErrorMesssage('Error uploading file.')
            }).finally(() => {
            setUploadLoading(false);
        });
    };

    const cleanFile = () => setFile(null);
    const reset = () => {
        setFile(null);
        setStatus(STATUS.UPLOAD_FILE);
    };

    const executePostValidation = async () => {
        if (postValidations && postValidations.length > 0) {
            setStatus(STATUS.POST_VALIDATION);

            const {storage, fileName, client, type} = urlConfiguration;
            const validations = postValidations;
            const response = await fetch("/api/file/validateFile", {
                method: 'POST',
                body: JSON.stringify({
                    path: storage,
                    fileName,
                    validations,
                    originalFileName: file?.name,
                    client,
                    type
                }),
                headers: {'Content-Type': 'application/json'},
            });
            if (!response.ok) {
                const error = await response.json();
                console.error(error)
                setStatus(STATUS.ERROR);
                setErrorMesssage(error?.error)
            } else {
                setStatus(STATUS.SUCCESS_UPLOAD);
                onSuccess?.();
            }
        } else {
            setStatus(STATUS.SUCCESS_UPLOAD);
            onSuccess?.();
        }
    }

    return (
        <Box>
            {status === STATUS.GENERATE_URL && (
                <Box sx={{textAlign: 'left', marginBottom: '20px'}}>
                    <Button loading={loading} variant="plain" sx={{color: 'gray', border: '1px solid #cdd7e1'}}
                            onClick={generateUrl}>
                        Upload file
                    </Button>
                </Box>
            )}

            {status === STATUS.UPLOAD_FILE && (
                <Box>
                    <Box
                        sx={{
                            border: '2px dashed',
                            borderColor: errors.length > 0 ? '#ff0000a3' : '#d1d1d1',
                            borderRadius: '4px',
                            padding: '16px',
                            textAlign: 'center',
                            cursor: 'pointer',
                            '&:hover': {backgroundColor: '#f5f5f5'},
                        }}
                        {...getRootProps()}
                    >
                        <input {...getInputProps()} />
                        <p style={{color: 'gray'}}>
                            {isDragActive ? "Drop the file here .." : "Drag and drop your file here or click to select file"}
                        </p>
                        <CloudUpload sx={{marginTop: '20px', fontSize: '30px'}}/>
                        {errors.length > 0 && errors.map((error, index) => (
                            <p key={index} style={{
                                color: 'red',
                                fontSize: '11px',
                                fontWeight: 'bold',
                                marginTop: '10px',
                                opacity: '.8'
                            }}>
                                {error}
                            </p>
                        ))}
                    </Box>

                    {file && (
                        <>
                            <Box sx={{
                                marginTop: '10px',
                                minHeight: '45px',
                                background: '#e2f2ff',
                                padding: '10px',
                                display: 'flex',
                                justifyContent: 'space-between'
                            }}>
                                <p>{file.name}</p>
                                <Close sx={{cursor: 'pointer'}} onClick={cleanFile}/>
                            </Box>
                            <Box sx={{marginTop: '20px', display: 'flex', justifyContent: 'left'}}>
                                <Button loading={uploadLoading} onClick={uploadFile} variant={'soft'}>Upload
                                    file</Button>
                            </Box>
                        </>
                    )}
                </Box>
            )}

            {status === STATUS.UPLOADING_FILE && (
                <Box>
                    <LinearProgressCountUp progress={uploadProgress}/>
                </Box>
            )}
            {status === STATUS.SUCCESS_UPLOAD && (
                <Box sx={{
                    height: '45px',
                    background: 'rgb(205 244 216)',
                    padding: '10px',
                    display: 'flex',
                    justifyContent: 'left',
                    alignItems: 'center'
                }}>
                    <CheckCircleOutline sx={{fontSize: '30px', marginRight: '10px'}}/>
                    <Typography level="body-md">
                        Your file was uploaded successfully.
                    </Typography>
                </Box>
            )}

            {status === STATUS.ERROR && (
                <Box>
                    <Box sx={{
                        height: '45px',
                        background: 'rgba(253,0,0,0.51)',
                        padding: '10px',
                        display: 'flex',
                        justifyContent: 'left',
                        alignItems: 'center'
                    }}>
                        <ErrorOutline sx={{fontSize: '30px', marginRight: '10px'}}/>
                        <Typography level="body-md">
                            {errorMessage}
                        </Typography>
                    </Box>
                    <Box sx={{marginTop: '20px', display: 'flex', justifyContent: 'left'}}>
                        <Button loading={uploadLoading} onClick={reset} variant="soft">Try Again</Button>
                    </Box>
                </Box>
            )}

            {status === STATUS.ERROR_GENERATE_URL && (
                <Box sx={{
                    height: '45px',
                    background: 'rgba(253,0,0,0.51)',
                    padding: '10px',
                    display: 'flex',
                    justifyContent: 'left',
                    alignItems: 'center'
                }}>
                    <ErrorOutline sx={{fontSize: '30px', marginRight: '10px'}}/>
                    <Typography level="body-md">
                        {urlError}
                    </Typography>
                </Box>
            )}

            {status === STATUS.POST_VALIDATION && (
                <Box sx={{
                    height: '45px',
                    justifyContent: 'left',
                    alignItems: 'center'
                }}>
                    <Typography level="body-md" sx={{marginBottom: '5px'}}>
                        Validating file
                    </Typography>
                    <LinearProgress variant="soft"/>
                </Box>
            )}
        </Box>
    );
};
