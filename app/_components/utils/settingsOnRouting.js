"use client"
import {
    setPhraseGloSea,
    setShowFiltersGS,
    setShowOnlyPagBut,
    setValueInpPhrGloSea
} from "@/app/_components/table/states/globalSearchStates.js";
import {loading, setLoading} from "@/app/_components/Loading/loadingState.js";
import {setUserIdForEdit} from "@/app/_components/table/states/adminEditStates.js";
import _ from "lodash";

export const settingsOnRouting = (tableName = null, defaultFilters = null, setFiltersEntity = null, setDefaultFilters = null) => {
    if (defaultFilters) {
        const currentFilters = localStorage.getItem(`db_${tableName}-filters`);
        const getDefaultFilters = JSON.parse(currentFilters)
        if (setDefaultFilters) setDefaultFilters(defaultFilters)
        if (_.isEmpty(getDefaultFilters) && !_.isEmpty(defaultFilters)) {
            localStorage.setItem(`db_${tableName}-filters`, JSON.stringify(defaultFilters));
            if (setFiltersEntity) setFiltersEntity(defaultFilters);
        } else {
            if (setFiltersEntity) setFiltersEntity(getDefaultFilters);
        }
    }
    setShowFiltersGS(true)
    setShowOnlyPagBut(false)
    setUserIdForEdit(null)
    setValueInpPhrGloSea("")
    setPhraseGloSea("")
    if (loading.get()) {
        setLoading(false)
    }
}
