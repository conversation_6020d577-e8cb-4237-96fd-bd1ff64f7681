import {DateTime} from "luxon";

const getISOWeeks = (year, month, addYear = false, showAllIsoWeeks = false) => {
    const weeks = new Map();
    let date;
    if (showAllIsoWeeks) {
        date = DateTime.local(year, 1, 1); // Comenzar desde el 1 de enero
    } else {
        date = DateTime.local(year, month + 1, 1); // Comenzar desde el mes especificado
    }
    const endOfRange = showAllIsoWeeks
        ? DateTime.local(year, 12, 31) // Finalizar en el último día del año
        : DateTime.local(year, month + 1, 1).endOf('month');
    while (date <= endOfRange) {
        const weekNumber = date.weekNumber;
        const weekYear = date.weekYear;
        const weekStart = date.startOf('week');
        const weekEnd = date.endOf('week');
        weeks.set(
            weekNumber,
            `${weekYear}-Week ${weekNumber} (${formatDate(weekStart, addYear)} - ${formatDate(weekEnd, addYear)})`
        );
        date = date.plus({days: 1});
    }
    return Array.from(weeks);
};


export const getISOWeeksInMonth = (year, month, addYear = false) => {
    return getISOWeeks(year, month, addYear, false);
};

export const getISOWeeksInMonthWithPrevious = (year, month, addYear = false) => {
    return getISOWeeks(year, month, addYear, true);
};

const formatDate = (date, addYear) => {
    return addYear ? date.toFormat('LLL d yyyy') : date.toFormat('LLL d');
};
