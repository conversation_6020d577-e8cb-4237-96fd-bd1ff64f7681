import * as React from 'react';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
import Radio from '@mui/joy/Radio';
import RadioGroup from '@mui/joy/RadioGroup';

export default function ExpenseRvnAmountType({onChangeTypeAmount}) {
    return (
        <RadioGroup  name="accounting type" defaultValue="perItem">
            <List
                sx={{
                    minWidth: 240,
                    '--List-gap': '0.5rem',
                    '--ListItem-paddingY': '1rem',
                    '--ListItem-radius': '8px',
                    '--ListItemDecorator-size': '32px',
                }}
            >
                {[{label: 'Per item', value: 'perItem'}, {label: 'All', value: 'all'}].map((item, index) => (
                    <ListItem variant="outlined" key={item.value} sx={{boxShadow: 'sm'}}>
                        <Radio
                            overlay
                            value={item.value}
                            label={item.label}
                            sx={{flexGrow: 1, flexDirection: 'row-reverse'}}
                            onChange={onChangeTypeAmount}
                            slotProps={{
                                action: ({checked}) => ({
                                    sx: (theme) => ({
                                        ...(checked && {
                                            inset: -1,
                                            border: '2px solid',
                                            borderColor: theme.vars.palette.primary[500],
                                        }),
                                    }),
                                }),
                            }}
                        />
                    </ListItem>
                ))}
            </List>
        </RadioGroup>
    );
}
