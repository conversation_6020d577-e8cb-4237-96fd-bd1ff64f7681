import {DateTime} from "luxon";
import _ from "lodash";

export const formatFilterIsoWeek = (isoWeekFilter) => {
    let {year, week} = isoWeekFilter;
    week = _.isArray(week) ? week : [week];
    if (week.length === 1 || week > 0) {
        const startOfWeek = DateTime.fromObject({weekYear: year, weekNumber: week[0], weekday: 1});
        const endOfWeek = startOfWeek.plus({days: 6});
        return {
            start: startOfWeek.toISODate(),
            end: endOfWeek.toISODate()
        };
    } else {
        const startOfFirstWeek = DateTime.fromObject({weekYear: year, weekNumber: week[0], weekday: 1});
        const endOfLastWeek = DateTime.fromObject({weekYear: year, weekNumber: week[week.length - 1], weekday: 7});
        return {
            start: startOfFirstWeek.toISODate(),
            end: endOfLastWeek.toISODate()
        };
    }
}
