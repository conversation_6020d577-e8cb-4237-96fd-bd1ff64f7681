import _ from "lodash";
import {DateTime} from "luxon";

export const getDefaultIsoWeek = (defaultIsoWeek) => {
    if (defaultIsoWeek && !_.isEmpty(defaultIsoWeek) && !defaultIsoWeek.all && defaultIsoWeek !== 'NA') {
        if (defaultIsoWeek.weekNumber.length > 1) {
            return {
                weekYear: defaultIsoWeek.year,
                weekNumber: 'all',
                weekday: 3,
                month: defaultIsoWeek.month,
                year: defaultIsoWeek.year
            }
        }
        return DateTime.fromObject({
            weekYear: defaultIsoWeek.year,
            weekNumber: defaultIsoWeek.weekNumber,
            weekday: defaultIsoWeek.weekday ? defaultIsoWeek.weekday : 3
        })
    } else {
        return DateTime.local();
    }
}
