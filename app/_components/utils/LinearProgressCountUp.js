import * as React from 'react';
import LinearProgress from '@mui/joy/LinearProgress';
import Typography from '@mui/joy/Typography';

export default function LinearProgressCountUp({progress=0}) {

    return (
        <LinearProgress
            determinate
            variant="soft"
            color="neutral"
            size="sm"
            thickness={24}
            value={Number(progress)}
            sx={{
                '--LinearProgress-radius': '20px',
                '--LinearProgress-thickness': '24px',
                color:'#e3effb',
                background: '#EAE5E5'
            }}
        >
            <Typography
                level="body-xs"
                textColor="white"
                sx={{ mixBlendMode: 'difference' }}
            >
                Uploading… {`${Math.round(Number(progress))}%`}
            </Typography>
        </LinearProgress>
    );
}
