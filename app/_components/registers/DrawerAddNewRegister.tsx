'use client'
import React, {useEffect, useState} from "react";
import Box from "@mui/joy/Box";
import ModalDialog from "@mui/joy/ModalDialog";
import ModalClose from "@mui/joy/ModalClose";
import Typography from "@mui/joy/Typography";
import Modal from "@mui/joy/Modal";
import {AddRegisterIcon} from "@/app/_components/registers/AddRegisterIcon";
import {buildUpdateSchema} from "@/app/_components/table/update/BuildUpdateSchema";
import validator from "@rjsf/validator-ajv8";
import {Theme as AntDTheme} from '@rjsf/antd';
import {withTheme} from "@rjsf/core";
import {generateId} from "@/app/_lib/utils/generateId";
import {useQueryClient} from "@tanstack/react-query";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {Tooltip} from "@mui/joy";
import {CustomSelectWidget} from "@/app/_components/customForms/widgets/CustomSelectWidget";
import {ObjectFormUpdAddTemplate} from "@/app/_components/customForms/templates/ObjectFormUpdAddTemplate";
import Loading from "react-loading";
import _ from "lodash";
import {styledName} from "@/app/_components/utils/styledName";

const ThemedForm = withTheme(AntDTheme);

export const DrawerAddNewRegister: React.FC<any> = ({tableColumns, tableName}) => {
    const [showModal, setShowModal] = useState(false);
    const [loadingSub, setLoadingSub] = useState(false);
    const [registerSchema, setRegisterSchema] = useState({});
    const [uiSchema, setUiSchema] = useState({});
    const [keysToReplace, setKeysToReplace] = useState([]);
    const queryClient = useQueryClient();

    useEffect(() => {
        buildSchema();
    }, [tableColumns]);

    const buildSchema = () => {
        if (tableName === "campaigns") {
            const customSchema = {
                type: "object",
                properties: {
                    name: {type: "string", title: "Name", minLength: 1},
                },
                required: ["name"],
            };
            setRegisterSchema(customSchema);
            setUiSchema({});
        } else {
            let {schema: newSchema, uiSchema, accKeysReplace} = buildUpdateSchema(tableColumns, false);
            newSchema = removeIdProperty(newSchema);
            newSchema.required = Object.keys(newSchema.properties).filter(key => {
                const property = newSchema.properties[key];
                return property && property.type !== "array" && !_.has(property, "default");
            });
            setRegisterSchema(newSchema);
            setUiSchema(uiSchema);
            setKeysToReplace(accKeysReplace);
        }
    };

    const removeIdProperty = (schema: any) => {
        if (schema.properties && schema.properties._id) {
            const {_id, ...remainingProperties} = schema.properties;
            return {
                ...schema,
                properties: remainingProperties
            };
        }
        return schema;
    };

    const addNewRegister = () => {
        setShowModal(true);
    };

    const submit = async (newData: any) => {
        await createNewRegister(newData.formData);
    };

    const createNewRegister = async (formData: any) => {
        try {
            setLoadingSub(true);
            const registerId = generateId();
            const newRegisterData = {
                ...formData,
                _id: registerId,
                ...(tableName === 'tags' && { show: true })
            };

            const endpoint = tableName === "campaigns"
                ? "/api/mongo/call-center/create-new-call-campaign"
                : "/api/mongo/createRegister";

            const payload = {
                collection: tableName,
                keysToReplace,
                data: newRegisterData
            };

            const result = await fetch(endpoint, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload)
            });

            if (result.ok) {
                showSuccess("Create successfully");
                queryClient.invalidateQueries({queryKey: [`table_data_${tableName}`]});
                queryClient.invalidateQueries({queryKey: [`${tableName}-count`]});
            } else {
                showError("We had a problem to create register");
            }
        } catch (error) {
            showError(error.message || "We had a problem creating register");
        } finally {
            setShowModal(false);
            setLoadingSub(false);
        }
    };

    const widgets = {
        customSelectWidget: CustomSelectWidget
    };

    const transformErrors = (errors: any) => {
        return errors.map((error: any) => {
            if (error.name === "pattern") {
                error.message = "The field must contain only numbers";
            }
            return error;
        });
    };

    const removeTrailingS = (word: string): string => {
        if (_.endsWith(word, 's') || _.endsWith(word, 'S')) {
            return _.trimEnd(word, 'sS');
        }
        return word;
    }
    const titleAdd = (tableName) ? `New ${styledName(removeTrailingS(tableName))}` : 'New Register';

    return (
        <>
            <Tooltip title="New Register" variant="soft" placement="left">
                <Box>
                    <AddRegisterIcon addRegister={() => addNewRegister()}/>
                </Box>
            </Tooltip>
            <Modal open={showModal}>
                <ModalDialog size="sm" style={{maxWidth: "550px", maxHeight: "805px"}} variant="outlined"
                             sx={{padding: "20px"}}>
                    {!loadingSub && <ModalClose onClick={() => setShowModal(false)}/>}
                    <Typography color="neutral" level="title-lg" noWrap={false} variant="plain">
                        Add {titleAdd}
                    </Typography>
                    <Box sx={{overflowY: "auto", maxHeight: "600px", overflowX: "hidden"}}>
                        {!loadingSub ? (
                            <ThemedForm
                                schema={registerSchema}
                                transformErrors={transformErrors}
                                validator={validator}
                                onSubmit={submit}
                                widgets={widgets}
                                uiSchema={{
                                    "ui:submitButtonOptions": {
                                        "submitText": "Submit",
                                        "norender": false,
                                        "props": {
                                            "type": "primary"
                                        }
                                    },
                                    ...uiSchema,
                                    "ui:ObjectFieldTemplate": ObjectFormUpdAddTemplate,
                                }}
                            />
                        ) : (
                            <div className="flex justify-center items-center" style={{height: "350px", width: "350px"}}>
                                <Loading color={"black"} width={"50px"} height={"50px"} type={"spin"}/>
                            </div>
                        )}
                    </Box>
                </ModalDialog>
            </Modal>
        </>
    );
};
