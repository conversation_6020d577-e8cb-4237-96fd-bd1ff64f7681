import React, {useEffect, useRef, useState} from "react";
import {Box, Button, CircularProgress, Modal, ModalDialog, Tab, <PERSON><PERSON><PERSON>ist, Tab<PERSON>anel, Ta<PERSON>, Typography,} from "@mui/joy";
import {useEntity} from "simpler-state";
import {activeTabNodeScripts, setActiveNodeScriptTab,} from "@/app/_components/table/states/nodeSriptsStates";
import BackToScriptsButton from "@/app/_components/callScriptsConf/BackToScriptsButton";
import {useRouter} from "next/navigation";
import {setLoading} from "@/app/_components/Loading/loadingState";
import {ResetCampaign} from "@/app/_components/callScriptsConf/ResetCampaign";
import {ChatScriptProvider} from "@/app/_components/callScriptsConf/tabs/ChatScript";
import {ViewDataToSendProvider} from "@/app/_components/callScriptsConf/tabs/ViewDataToSendProvider";
import {VarConfComponent} from "@/app/_components/callScriptsConf/tabs/VarConfComponent";
import {GreetingsConfiguration} from "@/app/_components/callScriptsConf/tabs/GreetingsConfiguration";
import {RequestConfiguration} from "@/app/_components/callScriptsConf/tabs/RequestConfiguration";
import {TasksConfiguration} from "@/app/_components/callScriptsConf/tabs/TasksConfiguration";
import {TerminateConfiguration} from "@/app/_components/callScriptsConf/tabs/TerminateConfiguration";
import {CallConfigurationProvider} from "@/app/_components/callScriptsConf/tabs/CallConfigurationProvider";
import {KnowledgeConfiguration} from "@/app/_components/callScriptsConf/tabs/KnowledgeConfiguration";

export const TabsViewCallScriptsComponent: React.FC<any> = ({
                                                                greetingsData,
                                                                userMetadata,
                                                                dispositionData,
                                                                tasksData,
                                                                campaignData,
                                                                tableName,
                                                                terminateData,
                                                                callCampaignId,
                                                                userId,
                                                                confCallLeads,
                                                                vectraConfig,
                                                            }) => {
    const isDev = userMetadata?.dev;
    const [openConfirm, setOpenConfirm] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [showResetCampaign, setShowResetCampaign] = useState(false);
    const principalAudioRef = useRef<HTMLAudioElement | null>(null);

    const getTab = useEntity(activeTabNodeScripts);
    const router = useRouter();

    useEffect(() => {
        if (!isDev) setActiveNodeScriptTab(1)
    }, []);

    const handleTabChange = (newValue: any) => {
        resetCurrentAudio()
        setLoading(true);
        setActiveNodeScriptTab(newValue);
        router.replace(`/call-center/script/${callCampaignId}/${newValue}`);
    };

    const handleReset = () => {
        setOpenConfirm(false);
        setShowResetCampaign(true);
        setIsLoading(true);
    };

    const handleResetComplete = () => {
        setShowResetCampaign(false);
        setIsLoading(false);
    };

    const resetCurrentAudio = () => {
        if (principalAudioRef.current) {
            principalAudioRef.current.pause();
            principalAudioRef.current.currentTime = 0;
            principalAudioRef.current = null;
        }
    }

    return (
        <>
            <BackToScriptsButton/>
            <div style={{marginTop: "16px"}}>
                {callCampaignId === "994bb173e9d51554b5c8fae2" && (
                    <>
                        <Button
                            variant="outlined"
                            color="neutral"
                            sx={{fontWeight: "bold", mb: 3}}
                            onClick={() => setOpenConfirm(true)}>
                            Reset Component
                        </Button>
                        <Modal open={openConfirm} onClose={() => setOpenConfirm(false)}>
                            <ModalDialog>
                                <Typography component="h2" fontSize="lg" fontWeight="bold">
                                    Confirm Reset
                                </Typography>
                                <Typography fontSize="md" sx={{mt: 2, mb: 3}}>
                                    Are you sure you want to reset? This action cannot be undone.
                                </Typography>
                                <Box sx={{display: "flex", justifyContent: "flex-end", gap: 1}}>
                                    <Button
                                        variant="outlined"
                                        color="neutral"
                                        onClick={() => setOpenConfirm(false)}
                                    >
                                        Cancel
                                    </Button>
                                    <Button color="danger" variant="solid" onClick={handleReset}>
                                        Yes, Reset
                                    </Button>
                                </Box>
                            </ModalDialog>
                        </Modal>
                    </>
                )}
            </div>
            {showResetCampaign && (
                <ResetCampaign
                    callCampaignId={callCampaignId}
                    onComplete={handleResetComplete}
                />
            )}
            <Modal open={isLoading}>
                <ModalDialog>
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            flexDirection: "column",
                            gap: 2,
                        }}>
                        <CircularProgress/>
                        <Typography fontSize="md" fontWeight="bold">
                            Processing reset, please wait...
                        </Typography>
                    </Box>
                </ModalDialog>
            </Modal>

            <Tabs
                value={getTab}
                onChange={(_, newValue) => handleTabChange(newValue)}
                sx={{"& .MuiTabs-flexContainer": {justifyContent: "center"}}}>
                <TabList
                    sticky="top"
                    sx={{
                        display: "inline-flex",
                        justifyContent: "center",
                        "& .MuiTab-root": {
                            minWidth: 5,
                            fontSize: "clamp(0.65rem, 2vw, 1rem)",
                            whiteSpace: "nowrap",
                            padding: "6px 12px",
                        },
                    }}>
                    {isDev && (
                        <>
                            <Tab value={0} sx={{minWidth: 20}}>
                                Variables
                            </Tab>
                            <Tab value={1} sx={{minWidth: 20}}>
                                Events
                            </Tab>
                            <Tab value={2} sx={{minWidth: 20}}>
                                Knowledge base
                            </Tab>
                        </>
                    )}
                    <Tab value={3} sx={{minWidth: 20}}>
                        Greetings
                    </Tab>
                    <Tab value={4} sx={{minWidth: 20}}>
                        Requests
                    </Tab>
                    <Tab value={5} sx={{minWidth: 20}}>
                        Tasks
                    </Tab>
                    <Tab value={6} sx={{minWidth: 20}}>
                        Terminates
                    </Tab>
                    <Tab value={7} sx={{minWidth: 20}}>
                        Complete
                    </Tab>
                    {isDev && (
                        <Tab value={8} sx={{minWidth: 20}}>
                            Chat
                        </Tab>
                    )}
                </TabList>

                {isDev && (
                    <>
                        <TabPanel value={0}>
                            <VarConfComponent callCampaignId={callCampaignId}/>
                        </TabPanel>
                        <TabPanel value={1}>
                            <ViewDataToSendProvider
                                callCampaignId={callCampaignId}
                            />
                        </TabPanel>
                        <TabPanel value={2}>
                            <KnowledgeConfiguration callCampaignId={callCampaignId} config={vectraConfig}/>
                        </TabPanel>
                    </>
                )}
                <TabPanel value={3}>
                    <GreetingsConfiguration
                        data={greetingsData}
                        isDev={isDev}
                        terminateData={terminateData}
                        tableName={tableName}
                        callCampaignId={callCampaignId}
                    />
                </TabPanel>
                <TabPanel value={4}>
                    <RequestConfiguration
                        data={dispositionData}
                        isDev={isDev}
                        tableName={tableName}
                        callCampaignId={callCampaignId}
                    />
                </TabPanel>
                <TabPanel value={5}>
                    <TasksConfiguration
                        data={tasksData}
                        isDev={isDev}
                        allData={campaignData}
                        terminateData={terminateData}
                        tableName={tableName}
                        callCampaignId={callCampaignId}
                    />
                </TabPanel>
                <TabPanel value={6}>
                    <TerminateConfiguration
                        data={terminateData}
                        isDev={isDev}
                        tableName={tableName}
                        callCampaignId={callCampaignId}
                    />
                </TabPanel>
                <TabPanel value={7}>
                    <CallConfigurationProvider
                        campaignData={campaignData}
                        tableName={tableName}
                        callCampaignId={callCampaignId}
                        isDev={isDev}
                    />
                </TabPanel>
                {isDev && (
                    <TabPanel value={8}>
                        <ChatScriptProvider
                            chatData={userMetadata}
                            callCampaignId={callCampaignId}
                            userId={userId}
                            confCallLeads={confCallLeads}
                            principalAudioRef={principalAudioRef}
                            vectraConfig={vectraConfig}
                        />
                    </TabPanel>
                )}
            </Tabs>
        </>
    );
};
