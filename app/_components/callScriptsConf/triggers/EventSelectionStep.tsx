import {<PERSON>, <PERSON><PERSON>, <PERSON>, Grid, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Too<PERSON><PERSON>, Typography} from "@mui/joy"
import React, {useState} from "react";
import {styledName} from "@/app/_components/utils/styledName";
import Modal from "@mui/joy/Modal";
import ModalDialog from "@mui/joy/ModalDialog";
import ModalClose from "@mui/joy/ModalClose";
import SendOutlinedIcon from '@mui/icons-material/SendOutlined';
import FactCheckOutlinedIcon from '@mui/icons-material/FactCheckOutlined';
import DeleteIcon from '@mui/icons-material/Delete';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import AssignmentOutlinedIcon from '@mui/icons-material/AssignmentOutlined';
import IosShareOutlinedIcon from '@mui/icons-material/IosShareOutlined';
import JsonView from "react18-json-view";
import 'react18-json-view/src/style.css'
import {showError, showSuc<PERSON>} from "@/app/_components/alerts/toast/ToastMessages";
import {useQueryClient} from "@tanstack/react-query";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";

export const EventSelectionStep = ({
                                       activeSubs,
                                       campaignId,
                                       logsData,
                                       setShowSteps,
                                       setSubsSelected,
                                       setEventShowing
                                   }) => {
    const queryClient = useQueryClient();
    const [openModal, setOpenModal] = useState(false);
    const [dataEventSelected, setDataEventSelected] = useState(null);
    const [eventModal, setEventModal] = useState(null);
    const [viewJsonLogs, setViewJsonLogs] = useState(null);
    const [loadingEvent, setLoadingEvent] = useState(false);

    const handleUnsubscribe = async () => {
        setOpenModal(false);
        setLoadingEvent(true);
        try {
            const deletePayload = {
                collection: 'subscriptions',
                filter: {_id: dataEventSelected._id},
                deleteMany: false
            };

            const res = await fetch("/api/mongo/call-center/delete-data", {
                method: "POST",
                headers: {"Content-Type": "application/json"},
                body: JSON.stringify(deletePayload),
            });

            if (!res.ok) {
                const errorText = await res.text();
                showError("Failed to unsubscribe: " + errorText);
            } else {
                showSuccess("Unsubscribed successfully");
            }
        } catch (err) {
            console.error("Error on unsubscribe", err);
        } finally {
            handleClose();
            setLoadingEvent(false);
            queryClient.invalidateQueries({queryKey: [`data_triggers_${campaignId}`]});
        }
    };

    const handleGetLogs = async (eventType: string) => {
        setLoadingEvent(true);
        try {
            const payload = {
                collection: "triggers",
                filter: {
                    campaign_id: campaignId,
                    event: eventType
                },
                limit: 10
            }

            const getItemsResult = await fetch("/api/mongo/call-center/get-all-data", {
                method: 'POST',
                body: JSON.stringify(payload),
                headers: {'Content-Type': 'application/json'}
            });

            if (getItemsResult.ok) {
                const {response} = await getItemsResult.json();
                const getLogs = response.length > 0 ? response : {logs: 'No logs found'};
                setViewJsonLogs(getLogs);
            }
        } catch (err) {
            console.error("Error on unsubscribe", err);
        } finally {
            setLoadingEvent(false);
            setOpenModal(true);
            setEventModal("logs");
        }
    };

    const handleTestEvent = async (eventType: string) => {
        console.log('test event: ', eventType);
    }

    const handleClose = () => {
        setOpenModal(false);
        setDataEventSelected(null);
        setEventModal(null);
        setViewJsonLogs(null);
    };

    const dealerSubs = activeSubs?.filter(sub => sub?.callShaper);
    const normalSubs = activeSubs?.filter(sub => !sub?.callShaper);

    const renderCards = (subs) => (
        <Grid container spacing={2}>
            {subs.map((sub, index) => (
                <Grid xs={12} sm={6} md={4} key={`sub_${index}`}>
                    <Card variant="outlined" sx={{p: 2}}>
                        <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Typography level="title-md"><strong>Event:</strong></Typography>
                            <Typography level="body-md">{styledName(sub?.event)}</Typography>
                        </Stack>
                        <Typography level="body-sm" sx={{my: 1}}>
                            {sub?.config?.description || "No description"}
                        </Typography>
                        <Stack direction="row" spacing={1}>
                            {!sub?.callShaper && <>
                                <Tooltip title="Send Service">
                                    <IconButton onClick={() => {
                                        setSubsSelected(sub);
                                        setEventShowing("data_sent")
                                    }}>
                                        <SendOutlinedIcon/>
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Map Data">
                                    <IconButton onClick={() => {
                                        setSubsSelected(sub);
                                        setEventShowing("data_map")
                                    }}>
                                        <FactCheckOutlinedIcon/>
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Unsubscribe">
                                    <IconButton color="danger" onClick={() => {
                                        setDataEventSelected(sub);
                                        setEventModal("unsubscribe");
                                        setOpenModal(true);
                                    }}>
                                        <DeleteIcon/>
                                    </IconButton>
                                </Tooltip>
                            </>}
                            <Tooltip title="Logs">
                                <IconButton
                                    color="primary"
                                    onClick={() => handleGetLogs(sub?.event)}>
                                    <AssignmentOutlinedIcon/>
                                </IconButton>
                            </Tooltip>
                            <Tooltip title="Test event">
                                <IconButton
                                    color="success"
                                    onClick={() => handleTestEvent(sub?.event)}>
                                    <IosShareOutlinedIcon/>
                                </IconButton>
                            </Tooltip>
                        </Stack>
                    </Card>
                </Grid>
            ))}
        </Grid>
    );

    return (<>
            {loadingEvent ? <LoadingMessage message="Loading..."/> : <>
                <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{mb: 2}}>
                    <Typography level="h4">Subscribed Events</Typography>
                    <Tooltip title="Add Subscription">
                        <IconButton
                            variant="solid"
                            color="neutral"
                            onClick={() => setShowSteps(null)}
                        >
                            <AddCircleOutlineIcon/>
                        </IconButton>
                    </Tooltip>
                </Stack>
                {normalSubs?.length > 0 && renderCards(normalSubs)}
                {dealerSubs?.length > 0 && (<>
                    <Typography level="h4" sx={{mt: 4, mb: 2}}>Dealer Events</Typography>
                    {renderCards(dealerSubs)}
                </>)}
            </>}

            <Modal open={openModal}>
                <ModalDialog size="lg" sx={{overflowY: 'auto'}}>
                    <ModalClose onClick={handleClose}/>
                    {eventModal === "logs" ? (
                        <JsonView src={viewJsonLogs} theme="vscode"/>
                    ) : (<>
                        <Typography>Confirm unsubscribe?</Typography>
                        <Box>
                            <Typography level="body-md">Are you sure you want to unsubscribe from this
                                event?</Typography>
                            <Box sx={{mt: 2, display: "flex", justifyContent: "flex-end"}}>
                                <Button onClick={handleClose} sx={{mr: 2}}>
                                    Cancel
                                </Button>
                                <Button
                                    variant="solid"
                                    color="danger"
                                    onClick={handleUnsubscribe}
                                >
                                    Unsubscribe
                                </Button>
                            </Box>
                        </Box>
                    </>)}
                </ModalDialog>
            </Modal>
        </>
    );
};
