import {<PERSON>, <PERSON>, Grid, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typography} from "@mui/joy";
import {styledName} from "@/app/_components/utils/styledName";
import InfoIcon from "@mui/icons-material/Info";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import DataEventView from "@/app/_components/callScriptsConf/triggers/DataEventView";
import React, {useState} from "react";
import {generateId} from "@/app/_lib/utils/generateId";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";

export const EventListView: React.FC<any> = ({
                                                 eventsData,
                                                 campaignId,
                                                 mapVariables,
                                                 setActiveStep,
                                                 setSubsSelected
                                             }) => {
    const [fieldsData, setFieldsData] = useState(null);
    const [loadingSub, setLoadingSub] = useState(false);

    const handleSelectNewEvent = async (eventData: any) => {
        setFieldsData(null);
        setLoadingSub(true);
        const newSub = {
            _id: generateId(),
            event: eventData?.type,
            campaignId,
            config: {},
            dataMap: {
                maps: mapVariables,
                customs: []
            }
        };
        try {
            const payload = {
                collection: 'subscriptions',
                keysToReplace: [],
                data: newSub
            };

            const res = await fetch("/api/mongo/call-center/create-data", {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(payload)
            });

            if (!res.ok) {
                const errorText = await res.text();
                showError("Failed to subscribe: " + errorText);
            } else {
                showSuccess("Subscribed successfully");
            }
        } catch (err) {
            console.error("Error on subscribe", err);
        } finally {
            setSubsSelected(newSub);
            setActiveStep(1);
            setLoadingSub(false);
        }
    };

    return (<>
        {loadingSub ?
            <LoadingMessage message="Loading..."/> :
            <Box sx={{p: 2}}>
                <Typography level="h4" sx={{mb: 2}}>Available Events</Typography>
                <Grid container spacing={2}>
                    {eventsData.map((event: any, index: number) => (
                        <Grid xs={12} sm={6} md={6} key={`available_event_${index}`}>
                            <Card variant="outlined" sx={{p: 2}}>
                                <Stack direction="row" justifyContent="space-between" spacing={1}>
                                    <Typography level="body-md">
                                        {styledName(event?.type)}
                                    </Typography>
                                    <Stack direction="row" justifyContent="flex-end" spacing={1} sx={{mt: 1}}>
                                        <Tooltip title="View Data">
                                            <IconButton
                                                variant="outlined"
                                                onClick={() => setFieldsData(event.fields)}
                                            >
                                                <InfoIcon/>
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Subscribe">
                                            <IconButton
                                                variant="outlined"
                                                color="neutral"
                                                onClick={() => handleSelectNewEvent(event)}
                                            >
                                                <AddIcon/>
                                            </IconButton>
                                        </Tooltip>
                                    </Stack>
                                </Stack>
                            </Card>
                        </Grid>
                    ))}
                </Grid>

                {fieldsData && (
                    <Box
                        sx={{
                            mt: 3,
                            p: 2,
                            border: '1px solid',
                            borderRadius: 'sm',
                            borderColor: 'neutral.outlinedBorder',
                            bgcolor: '#fafafa',
                        }}
                    >
                        <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Typography level="title-md" sx={{mb: 1}}>
                                Event Data Preview
                            </Typography>
                            <Tooltip title="Close preview">
                                <IconButton
                                    variant="outlined"
                                    color="neutral"
                                    onClick={() => setFieldsData(null)}
                                >
                                    <CloseIcon/>
                                </IconButton>
                            </Tooltip>
                        </Stack>
                        <DataEventView fieldsData={fieldsData}/>
                    </Box>
                )}
            </Box>}
    </>);
}
