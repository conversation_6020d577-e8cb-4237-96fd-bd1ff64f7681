import React, {useEffect, useState} from "react";
import validator from "@rjsf/validator-ajv8";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";
import {Box, Typography} from "@mui/joy";
import {buildFormSendService} from "@/app/_components/callScriptsConf/funcs/buildFormSendService";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {withTheme} from "@rjsf/core";
import {Theme as AntDTheme} from "@rjsf/antd";

const ThemedForm = withTheme(AntDTheme);

export const ServiceSendDataConf: React.FC<any> = ({
                                                       subsSelected,
                                                       setActiveStep,
                                                       handleFinish,
                                                       isNew,
                                                   }) => {
    const [loadingSubmit, setLoadingSubmit] = useState(false);
    const [sendServiceForm, setSendServiceForm] = useState(null);

    useEffect(() => {
        const getSchema = buildFormSendService(subsSelected?.config, isNew)
        setSendServiceForm(getSchema)
    }, []);

    const onSubmit = async (data: any) => {
        try {
            setLoadingSubmit(true);
            const newConfig = data?.formData ?? {}
            const {_id} = subsSelected;
            const payload = {
                collection: 'subscriptions',
                filter: {_id},
                update: {$set: {config: newConfig}},
                options: {upsert: false}
            };

            const updateResult = await fetch("/api/mongo/call-center/update-data", {
                method: 'PUT',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(payload)
            });

            if (updateResult.status !== 200) {
                const errorResponse = await updateResult.text();
                console.error('Failed to saving data:', errorResponse);
                showError('Problem saving data: ' + errorResponse);
            } else {
                showSuccess('Configuration saved successfully');
            }
        } catch (error) {
            console.error('Error fetching data:', error);
        } finally {
            setLoadingSubmit(false);
            resetValues()
            if (isNew) {
                setActiveStep(2);
            } else {
                handleFinish();
            }
        }
    }

    const resetValues = () => {
        setLoadingSubmit(false);
        setSendServiceForm(null);
    }

    return (<>
        <Typography level="h4" sx={{mb: 2}}>Data Sending Service</Typography>
        {loadingSubmit ?
            <div className="flex justify-center mt-3">
                <LoadingMessage message="Loading..."/>
            </div> :
            <Box sx={{
                padding: '20px'
            }}>
                {sendServiceForm &&
                    <ThemedForm
                        schema={sendServiceForm?.schema}
                        formData={sendServiceForm?.formData}
                        validator={validator}
                        widgets={sendServiceForm?.widgets}
                        onSubmit={onSubmit}
                        uiSchema={sendServiceForm?.uiSchema}
                    />}
            </Box>
        }
    </>)
}
