import {Box, CircularProgress, IconButton, Sheet, Table, Typography} from '@mui/joy';
import React, {useState} from "react";
import CheckIcon from "@mui/icons-material/Check";
import RemoveCircleIcon from "@mui/icons-material/RemoveCircle";
import {showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {styledName} from "@/app/_components/utils/styledName";

export const EventSubscription = ({
                                      campaignId,
                                      carrierId,
                                      subscriptions
                                  }) => {
    const [selectedEvent, setSelectedEvent] = useState<string | null>(null);
    const [subscribedEvents, setSubscribedEvents] = useState<string[]>(subscriptions?.events || []);
    const [loadingEvents, setLoadingEvents] = useState<string[]>([]);

    const eventDescriptions = {
        answer: JSON.stringify(JSON.parse('{\n' +
            '  "_id": "684b2b816cb8f2ded907e1a2",\n' +
            '  "session_id": "684b2b806cb8f2ded907e19f",\n' +
            '  "last_step": "callAnswered",\n' +
            '  "step_id": "becedc1d0e4144c38c9401a46be7d8c2",\n' +
            '  "campaign_id": "994bb173e9d51554b5c8fae2",\n' +
            '  "campaign_name": "test-with-reset",\n' +
            '  "status": "greeting",\n' +
            '  "event": "answer",\n' +
            '  "elapsed_time": 0,\n' +
            '  "metadata": {\n' +
            '    "first_name": "Alexis",\n' +
            '    "last_name": "Leyva",\n' +
            '    "gender": "m",\n' +
            '    "dob": "01/01/1990",\n' +
            '    "email": "<EMAIL>",\n' +
            '    "street_address": "15 Berneri Drive",\n' +
            '    "city": "Las Vegas",\n' +
            '    "state": "NV",\n' +
            '    "zip": "89138",\n' +
            '    "ipaddress": "*******",\n' +
            '    "exact_care_customer": null,\n' +
            '    "insurance_provider": null,\n' +
            '    "prescription_medications_count": null,\n' +
            '    "treated_conditions_three_or_more": null,\n' +
            '    "trouble_with_routine_activities": null,\n' +
            '    "local_time_call_start": "2025-06-12T19:33:20.738Z",\n' +
            '    "reschedule_time": "2025-06-19T19:33:20.738Z",\n' +
            '    "timezone": "PST",\n' +
            '    "name": "Alexis Leyva",\n' +
            '    "full_address": "15 Berneri Drive Las Vegas NV 89138"\n' +
            '  }\n' +
            '}'), null, 2),
        no_answer: '{}',
        reschedule: JSON.stringify(JSON.parse('{\n' +
            '  "_id": "684c7a305e52fbca05f0eceb",\n' +
            '  "session_id": "60e7f123-c37c-42fa-8daf-4eb7353593cc",\n' +
            '  "last_step": "askingQualificationQuestion",\n' +
            '  "step_id": "variable_becedc1d0e4144c38c9401a46be7d8c2",\n' +
            '  "campaign_id": "994bb173e9d51554b5c8fae2",\n' +
            '  "campaign_name": "test-with-reset",\n' +
            '  "status": "askingToRescheduleCall",\n' +
            '  "event": "reschedule",\n' +
            '  "elapsed_time": 12,\n' +
            '  "reschedule_time": "You can call tomorrow at 2pm"\n' +
            '}'), null, 2),
        dnc: JSON.stringify(JSON.parse('{\n' +
            '  "_id": "684b2b9c6cb8f2ded907e1a3",\n' +
            '  "session_id": "684b2b806cb8f2ded907e19f",\n' +
            '  "last_step": "endingCall",\n' +
            '  "step_id": "996755a32e14d95d7ffc71Da",\n' +
            '  "campaign_id": "994bb173e9d51554b5c8fae2",\n' +
            '  "campaign_name": "test-with-reset",\n' +
            '  "status": "endingCall",\n' +
            '  "event": "dnq",\n' +
            '  "elapsed_time": null,\n' +
            '  "client_phone": "unknown"\n' +
            '}'), null, 2),
        dnq: JSON.stringify(JSON.parse('{\n' +
            '  "_id": "684b2b9c6cb8f2ded907e1a3",\n' +
            '  "session_id": "684b2b806cb8f2ded907e19f",\n' +
            '  "last_step": "endingCall",\n' +
            '  "step_id": "996755a32e14d95d7ffc71Da",\n' +
            '  "campaign_id": "994bb173e9d51554b5c8fae2",\n' +
            '  "campaign_name": "test-with-reset",\n' +
            '  "status": "endingCall",\n' +
            '  "event": "dnq",\n' +
            '  "elapsed_time": null,\n' +
            '  "dnq_reason": "unknown"\n' +
            '}'), null, 2),
        qualified: 'Client is qualified. Requires: metadata.',
        transferred: JSON.stringify(JSON.parse('{\n' +
            '  "_id": "684a0268cad4a2eb7cfb6bb6",\n' +
            '  "session_id": "71676ff4-81df-4127-bac2-5b732030dd4b",\n' +
            '  "last_step": "transferringCall",\n' +
            '  "step_id": "996755a32e14d95d7ffc94D3",\n' +
            '  "campaign_id": "994bb173e9d51554b5c8fae2",\n' +
            '  "campaign_name": "test-with-reset",\n' +
            '  "status": "transferringCall",\n' +
            '  "event": "transfered",\n' +
            '  "elapsed_time": 82,\n' +
            '  "transfer": "+19726532859",\n' +
            '  "metadata": {\n' +
            '    "first_name": "Alexis",\n' +
            '    "last_name": "Leyva",\n' +
            '    "gender": "m",\n' +
            '    "dob": "1990-01-01",\n' +
            '    "email": "<EMAIL>",\n' +
            '    "street_address": "15 Berneri Drive",\n' +
            '    "city": "Las Vegas",\n' +
            '    "state": "NV",\n' +
            '    "zip": "89138",\n' +
            '    "ipaddress": "*******",\n' +
            '    "exact_care_customer": "N/A",\n' +
            '    "insurance_provider": "test company",\n' +
            '    "prescription_medications_count": "6",\n' +
            '    "treated_conditions_three_or_more": "N/A",\n' +
            '    "trouble_with_routine_activities": "N/A",\n' +
            '    "local_time_call_start": "2025-06-11T22:24:19Z",\n' +
            '    "reschedule_time": "2025-06-18T22:24:19Z",\n' +
            '    "timezone": "PST",\n' +
            '    "name": "Alexis Leyva",\n' +
            '    "full_address": "15 Berneri Drive Las Vegas NV 89138"\n' +
            '  }\n' +
            '}'), null, 2),
        transfer_busy: JSON.stringify(JSON.parse('{\n' +
            '  "_id": "684a0268cad4a2eb7cfb6bb6",\n' +
            '  "session_id": "71676ff4-81df-4127-bac2-5b732030dd4b",\n' +
            '  "last_step": "transferringCall",\n' +
            '  "step_id": "996755a32e14d95d7ffc94D3",\n' +
            '  "campaign_id": "994bb173e9d51554b5c8fae2",\n' +
            '  "campaign_name": "test-with-reset",\n' +
            '  "status": "transferringCall",\n' +
            '  "event": "transfered",\n' +
            '  "elapsed_time": 82,\n' +
            '  "transfer": "+19726532859",\n' +
            '  "metadata": {\n' +
            '    "first_name": "Alexis",\n' +
            '    "last_name": "Leyva",\n' +
            '    "gender": "m",\n' +
            '    "dob": "1990-01-01",\n' +
            '    "email": "<EMAIL>",\n' +
            '    "street_address": "15 Berneri Drive",\n' +
            '    "city": "Las Vegas",\n' +
            '    "state": "NV",\n' +
            '    "zip": "89138",\n' +
            '    "ipaddress": "*******",\n' +
            '    "exact_care_customer": "N/A",\n' +
            '    "insurance_provider": "test company",\n' +
            '    "prescription_medications_count": "6",\n' +
            '    "treated_conditions_three_or_more": "N/A",\n' +
            '    "trouble_with_routine_activities": "N/A",\n' +
            '    "local_time_call_start": "2025-06-11T22:24:19Z",\n' +
            '    "reschedule_time": "2025-06-18T22:24:19Z",\n' +
            '    "timezone": "PST",\n' +
            '    "name": "Alexis Leyva",\n' +
            '    "full_address": "15 Berneri Drive Las Vegas NV 89138"\n' +
            '  }\n' +
            '}'), null, 2),
    };

    const updateSubscription = async (updatedEvents: string[]) => {
        const payload = {
            collection: 'subscriptions',
            filter: {
                _id: subscriptions._id
            },
            update: {
                $set: {
                    events: updatedEvents,
                    campaignId,
                    carrierId
                }
            },
            options: {
                upsert: true
            }
        };

        const response = await fetch("/api/mongo/call-center/update-data", {
            method: "PUT",
            headers: {"Content-Type": "application/json"},
            body: JSON.stringify(payload)
        });

        return response.ok;
    };

    const handleSubscribeToggle = async (eventKey: string, isCurrentlySubscribed: boolean) => {
        setLoadingEvents(prev => [...prev, eventKey]);

        const newEvents = isCurrentlySubscribed
            ? subscribedEvents.filter(e => e !== eventKey)
            : [...subscribedEvents, eventKey];

        const success = await updateSubscription(newEvents);

        if (success) {
            setSubscribedEvents(newEvents);
            showSuccess('Your subscription was updated successfully');
        }

        setLoadingEvents(prev => prev.filter(e => e !== eventKey));
    };

    return (
        <Box sx={{p: 2}}>
            <Typography level="h4" sx={{mb: 2}}>
                Events
            </Typography>
            <Sheet variant="outlined" sx={{borderRadius: 'md', overflow: 'hidden'}}>
                <Table borderAxis="bothBetween" sx={{minWidth: 600}}>
                    <thead>
                    <tr>
                        <th style={{width: "13%"}}>Event</th>
                        <th>Description</th>
                        <th style={{width: "10%"}}>Subscribed</th>
                    </tr>
                    </thead>
                    <tbody>
                    {Object.entries(eventDescriptions).map(([eventKey, description]) => {
                        const isSubscribed = subscribedEvents.includes(eventKey);
                        const isLoading = loadingEvents.includes(eventKey);
                        return (
                            <tr
                                key={eventKey}
                                style={{
                                    backgroundColor: selectedEvent === eventKey ? "#f0f4ff" : "inherit"
                                }}
                                onClick={() => setSelectedEvent(eventKey)}
                            >
                                <td style={{width: "13%", whiteSpace: "nowrap"}}>{styledName(eventKey)}</td>
                                <td>
                                    <pre style={{whiteSpace: 'pre-wrap', wordWrap: 'break-word'}}>
                                        {description}
                                    </pre>
                                </td>
                                <td style={{textAlign: "center"}}>
                                    {isLoading ? (
                                        <CircularProgress size="sm"/>
                                    ) : (
                                        <IconButton
                                            color={isSubscribed ? "danger" : "primary"}
                                            onClick={() => handleSubscribeToggle(eventKey, isSubscribed)}
                                            title={isSubscribed ? "Unsubscribe" : "Subscribe"}
                                        >
                                            {isSubscribed ? <RemoveCircleIcon/> : <CheckIcon/>}
                                        </IconButton>
                                    )}
                                </td>
                            </tr>
                        );
                    })}
                    </tbody>
                </Table>
            </Sheet>
        </Box>
    );
};
