import React, {useEffect, useState} from "react";
import {Button, IconButton, Input, Sheet, Table, Typography} from "@mui/joy";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";

export const ViewMappingProvider: React.FC<any> = ({subsSelected}) => {
    const [maps, setMaps] = useState<any[]>([]);
    const [customs, setCustoms] = useState<any[]>([]);

    useEffect(() => {
        const {dataMap} = subsSelected || {};
        setMaps(dataMap?.maps ?? [])
        setCustoms(dataMap?.customs ?? [])
    }, [subsSelected]);

    const updateMapKey = async (index: number) => {
        const updated = [...maps];

        await saveMapping({
            ...subsSelected?.dataMap,
            maps: updated
        });
    };

    const updateCustom = async (index: number) => {
        await saveMapping({
            ...subsSelected?.dataMap,
            customs
        });
    };

    const addCustom = async () => {
        const updated = [...customs, {key: "", value: ""}];
        setCustoms(updated);

        await saveMapping({
            ...subsSelected?.dataMap,
            customs: updated
        });
    };

    const removeCustom = async (index: number) => {
        const updated = customs.filter((_, i) => i !== index);
        setCustoms(updated);

        await saveMapping({
            ...subsSelected?.dataMap,
            customs: updated
        });
    };

    const saveMapping = async (newData: any) => {
        const {_id} = subsSelected;
        const payload = {
            collection: "subscriptions",
            filter: {_id},
            update: {$set: {dataMap: newData}},
            options: {upsert: false}
        };

        await fetch("/api/mongo/call-center/update-data", {
            method: "PUT",
            headers: {"Content-Type": "application/json"},
            body: JSON.stringify(payload)
        });
    };

    return (
        <div>
            <Typography level="h4" sx={{mb: 2}}>
                Mappings
            </Typography>
            <Sheet variant="outlined" sx={{p: 2, borderRadius: "md", mb: 4}}>
                <Table borderAxis="bothBetween">
                    <thead>
                    <tr>
                        <th>Field</th>
                        <th>Key</th>
                    </tr>
                    </thead>
                    <tbody>
                    {maps.length > 0 ? (
                        maps.map((item, index) => (
                            <tr key={index}>
                                <td>{item.field}</td>
                                <td>
                                    <Input
                                        value={item.key}
                                        onChange={(e) => {
                                            const updated = [...maps];
                                            updated[index].key = e.target.value;
                                            setMaps(updated);
                                        }}
                                        onBlur={() => updateMapKey(index)}
                                        onKeyDown={(e) => {
                                            if (e.key === "Enter") {
                                                e.currentTarget.blur();
                                            }
                                        }}
                                        placeholder="Enter key"
                                    />
                                </td>
                            </tr>
                        ))
                    ) : (
                        <tr>
                            <td colSpan={2} style={{textAlign: "center", padding: "10px"}}>
                                No mappings added
                            </td>
                        </tr>
                    )}
                    </tbody>
                </Table>
            </Sheet>

            <Typography level="h4" sx={{mb: 2}}>
                Custom Keys
            </Typography>
            <Sheet variant="outlined" sx={{p: 2, borderRadius: "md"}}>
                <Table borderAxis="bothBetween">
                    <thead>
                    <tr>
                        <th>Key</th>
                        <th>Value</th>
                        <th style={{width: "7%"}}></th>
                    </tr>
                    </thead>
                    <tbody>
                    {customs.length > 0 ? (
                        customs.map((item, index) => (
                            <tr key={index}>
                                <td>
                                    <Input
                                        value={item.key}
                                        onChange={(e) => {
                                            const updated = [...customs];
                                            updated[index].key = e.target.value;
                                            setCustoms(updated);
                                        }}
                                        onBlur={() => updateCustom(index)}
                                        onKeyDown={(e) => {
                                            if (e.key === "Enter") e.currentTarget.blur();
                                        }}
                                        placeholder="Custom key"
                                    />
                                </td>
                                <td>
                                    <Input
                                        value={item.value}
                                        onChange={(e) => {
                                            const updated = [...customs];
                                            updated[index].value = e.target.value;
                                            setCustoms(updated);
                                        }}
                                        onBlur={() => updateCustom(index)}
                                        onKeyDown={(e) => {
                                            if (e.key === "Enter") e.currentTarget.blur();
                                        }}
                                        placeholder="Custom value"
                                    />
                                </td>
                                <td style={{width: "7%", whiteSpace: "nowrap"}}>
                                    <IconButton
                                        variant="plain"
                                        color="danger"
                                        onClick={() => removeCustom(index)}
                                    >
                                        <DeleteIcon/>
                                    </IconButton>
                                </td>
                            </tr>
                        ))
                    ) : (
                        <tr>
                            <td colSpan={3} style={{textAlign: "center", padding: "10px"}}>
                                No custom keys added
                            </td>
                        </tr>
                    )}
                    <tr>
                        <td colSpan={3} style={{textAlign: "center"}}>
                            <Button
                                startDecorator={<AddIcon/>}
                                onClick={addCustom}
                                size="sm"
                                variant="soft"
                            >
                                Add Custom Key
                            </Button>
                        </td>
                    </tr>
                    </tbody>
                </Table>
            </Sheet>
        </div>
    );
};
