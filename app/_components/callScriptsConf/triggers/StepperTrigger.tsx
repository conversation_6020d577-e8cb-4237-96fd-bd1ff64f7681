import {
    Stepper,
    Step,
    StepIndicator,
    stepClasses,
    typographyClasses,
    Typography,
} from '@mui/joy';
import EventAvailableRoundedIcon from '@mui/icons-material/EventAvailableRounded';
import SendRoundedIcon from '@mui/icons-material/SendRounded';
import SettingsSuggestRoundedIcon from '@mui/icons-material/SettingsSuggestRounded';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';

export const StepperTrigger = ({ activeStep }: { activeStep: number }) => {
    return (
        <Stepper
            sx={(theme) => ({
                '--Stepper-verticalGap': '2.5rem',
                '--StepIndicator-size': '2.5rem',
                '--Step-gap': '1rem',
                '--Step-connectorInset': '0.5rem',
                '--Step-connectorRadius': '1rem',
                '--Step-connectorThickness': '4px',
                '--joy-palette-success-solidBg': 'var(--joy-palette-success-400)',
                [`& .${stepClasses.completed}`]: {
                    '&::after': { bgcolor: 'success.solidBg' },
                },
                [`& .${stepClasses.active}`]: {
                    [`& .${StepIndicator}`]: {
                        border: '4px solid',
                        borderColor: '#fff',
                        boxShadow: `0 0 0 1px ${theme.vars.palette.primary[500]}`,
                    },
                },
                [`& .${stepClasses.disabled} *`]: {
                    color: 'neutral.softDisabledColor',
                },
                [`& .${typographyClasses['title-sm']}`]: {
                    textTransform: 'uppercase',
                    letterSpacing: '1px',
                    fontSize: '10px',
                },
            })}
        >
            <Step
                completed={activeStep > 0}
                active={activeStep === 0}
                indicator={
                    <StepIndicator variant="solid" color={activeStep >= 0 ? "success" : "neutral"}>
                        {activeStep > 0 ? <CheckRoundedIcon/> : <EventAvailableRoundedIcon/>}
                    </StepIndicator>
                }
            >
                <div>
                    <Typography level="title-sm">Step 1</Typography>
                    Subscribe to Events
                </div>
            </Step>

            <Step
                completed={activeStep > 1}
                active={activeStep === 1}
                indicator={
                    <StepIndicator variant="solid" color={activeStep >= 1 ? "success" : "neutral"}>
                        {activeStep > 1 ? <CheckRoundedIcon/> : <SendRoundedIcon/>}
                    </StepIndicator>
                }
            >
                <div>
                    <Typography level="title-sm">Step 2</Typography>
                    Data Sending Configuration
                </div>
            </Step>

            <Step
                active={activeStep === 2}
                indicator={
                    <StepIndicator variant="solid" color={activeStep === 2 ? "success" : "neutral"}>
                        <SettingsSuggestRoundedIcon />
                    </StepIndicator>
                }
            >
                <div>
                    <Typography level="title-sm">Step 3</Typography>
                    Data Mapping
                </div>
            </Step>
        </Stepper>
    );
};
