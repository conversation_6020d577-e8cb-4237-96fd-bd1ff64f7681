import {Box, Sheet, Typography} from "@mui/joy";
import React, {useState} from "react";
import JsonView from "react18-json-view";
import 'react18-json-view/src/style.css'
import _ from "lodash";

export const ViewDataMapped: React.FC<any> = ({dataExtracted}) => {
    const [extractedData, setExtractedData] = useState(dataExtracted ?? {});

    return (
        <Sheet
            sx={{
                margin: 'auto',
                display: 'flex',
                height: '100%',
                flexGrow: 1,
                flexDirection: 'column',
                borderRadius: 'md',
                boxShadow: 'lg',
                p: 2,
                gap: 2,
                mb: 2,
                bgcolor: 'background.body',
            }}>
            <Typography level="h3">Data extracted</Typography>
            <Box
                sx={{
                    height: 340,
                    flexGrow: 1,
                    overflowY: 'auto',
                    display: 'flex',
                    flexDirection: 'column',
                    padding: '1rem',
                    borderRadius: 'sm',
                    border: '1px solid',
                    borderColor: 'neutral.outlinedBorder',
                    bgcolor: '#fafafa',
                }}>
                {!_.isEmpty(extractedData) ?
                    <JsonView src={extractedData}
                              theme="vscode"
                              enableClipboard={false}
                              collapseStringsAfterLength={150}/> :
                    <Typography level="body-lg">No data to display</Typography>}
            </Box>
        </Sheet>
    )
}
