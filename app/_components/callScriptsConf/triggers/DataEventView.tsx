import {Table, Typography} from "@mui/joy";

export default function DataEventView({fieldsData}) {
    return (<>
        {fieldsData.length > 0 ? (
            <Table borderAxis="bothBetween">
                <thead>
                <tr>
                    <th style={{width: "30%"}}>Key</th>
                    <th>Description</th>
                </tr>
                </thead>
                <tbody>
                {fieldsData.map((field: any, index: number) => (
                    <tr key={index}>
                        <td style={{width: "30%", whiteSpace: "nowrap"}}>{field.key}</td>
                        <td>{field.description}</td>
                    </tr>
                ))}
                </tbody>
            </Table>
        ) : (
            <Typography level="body-sm">
                No data available for this event.
            </Typography>
        )}
    </>);
}
