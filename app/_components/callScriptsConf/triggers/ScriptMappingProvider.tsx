"use client"

import React, {useEffect, useState} from "react";
import Typography from "@mui/joy/Typography";
import validator from "@rjsf/validator-ajv8";
import {Box} from "@mui/joy";
import {buildFormMappingScript} from "@/app/_components/callScriptsConf/funcs/buildFormMappingScript";
import {withTheme} from "@rjsf/core";
import {Theme as AntDTheme} from "@rjsf/antd";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {generateId} from "@/app/_lib/utils/generateId";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";
import {useQueryClient} from "@tanstack/react-query";

const ThemedForm = withTheme(AntDTheme);

export const ScriptMappingProvider: React.FC<any> = ({callCampaignId, dataMapEnums, dataMapping, setHideClose, setOpenModal}) => {
    const queryClient = useQueryClient();
    const [mappingForm, setMappingForm] = useState(null);
    const [loadingSubmit, setLoadingSubmit] = useState(false);

    useEffect(() => {
        const getSchema = buildFormMappingScript(dataMapping?.data, dataMapEnums)
        setMappingForm(getSchema)
    }, []);

    const onSubmit = async (data: any) => {
        setLoadingSubmit(true);
        const objectToSave = {
            _id: dataMapping?._id ?? generateId(),
            callCampaignId,
            data: data?.formData ?? {},
        };
        await upsertData(objectToSave);
    }

    const upsertData = async (data: any) => {
        const {_id, ...dataWithoutId} = data;
        const payload = {
            collection: 'data_map',
            filter: {_id},
            update: {$set: dataWithoutId},
            options: {upsert: true}
        };

        const updateResult = await fetch("/api/mongo/call-center/update-data", {
            method: 'PUT',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(payload)
        });

        if (updateResult.status !== 200) {
            const errorResponse = await updateResult.text();
            console.error('Failed to saving data:', errorResponse);
            showError('Problem saving data: ' + errorResponse);
            resetValues()
        } else {
            showSuccess('Data saved successfully');
            resetValues()
            queryClient.invalidateQueries({queryKey: [`data_mapping_${callCampaignId}`]});
        }
    }

    const resetValues = () => {
        setLoadingSubmit(false);
        setMappingForm(null);
        setHideClose(false);
        setOpenModal(false);
    }

    return (<>
        <div className="flex justify-center mt-3">
            <Typography level="h2" fontSize="xl" sx={{mb: 0.5}}>
                Mapping Data Scripts
            </Typography>
        </div>
        {loadingSubmit ?
            <div className="flex justify-center mt-3">
                <LoadingMessage message="Saving data..."/>
            </div> :
            <Box sx={{
                padding: '20px'
            }}>
                {mappingForm &&
                    <ThemedForm
                        schema={mappingForm?.schema}
                        formData={mappingForm?.formData}
                        validator={validator}
                        widgets={mappingForm?.widgets}
                        onSubmit={onSubmit}
                        uiSchema={mappingForm?.uiSchema}
                    />}
            </Box>
        }
    </>)
}
