import {Box, Icon<PERSON>utton, Modal, ModalClose, Modal<PERSON><PERSON>og, Sheet, Table, Tooltip} from "@mui/joy"
import Typography from "@mui/joy/Typography";
import ControlPointIcon from "@mui/icons-material/ControlPoint";
import BuildIcon from "@mui/icons-material/Build";
import DeleteIcon from "@mui/icons-material/Delete";
import {VariablesFormProvider} from "@/app/_components/callScriptsConf/variables/VariablesFormProvider";
import React, {useState} from "react";
import {styledName} from "@/app/_components/utils/styledName";
import _ from "lodash";

export const TableSection = ({group, data, allVariables, campaignId}) => {
    const [valueColumn, setValueColumn] = useState(['company', 'compound']);
    const [openModal, setOpenModal] = useState(false);
    const [hideClose, setHideClose] = useState(false);
    const [selectedVar, setSelectedVar] = useState(null);
    const [titleModal, setTitleModal] = useState("Add");
    const [updVarName, setUpdVarName] = useState(null);

    const onAddItem = () => {
        setOpenModal(true)
        setTitleModal("Add")
    }

    const editVariable = (variable: any) => {
        setSelectedVar(variable)
        setUpdVarName(variable?.name ?? null)
        setTitleModal("Update")
        setOpenModal(true)
    }

    const deleteVariable = (variable: any) => {
        setSelectedVar(variable)
        setTitleModal("Delete")
        setOpenModal(true)
    }

    return (<>
        <Sheet variant="outlined" sx={{p: 2, borderRadius: "md", mb: 4}}>
            <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1}}>
                <Typography level="title-md" component="h2" fontWeight="bold" textAlign="left">
                    {styledName(group)} Variables
                </Typography>
                {group !== "script" &&
                    <Tooltip title="Create new variable" variant="soft" placement="top">
                        <IconButton
                            color='neutral'
                            variant="soft"
                            onClick={onAddItem}>
                            <ControlPointIcon/>
                        </IconButton>
                    </Tooltip>}
            </Box>
            <Table borderAxis="bothBetween">
                <thead>
                <tr>
                    <th>Name</th>
                    {group !== "compound" && <th>Details</th>}
                    {_.includes(valueColumn, group) && <th>Value</th>}
                    <th>Actions</th>
                </tr>
                </thead>
                <tbody>
                {data && data.length > 0 ? (
                    data.map((item: any, index: number) => (
                        <tr key={index}>
                            <td>{item?.name}</td>
                            {group !== "compound" && <td>{item?.details || "-"}</td>}
                            {_.includes(valueColumn, group) &&
                                <td>{item?.value || "-"}</td>}
                            <td>
                                <Tooltip title="Edit Variable" variant="soft" placement="top">
                                    <IconButton
                                        sx={{marginRight: '20px'}}
                                        color="neutral"
                                        variant="soft"
                                        onClick={() => editVariable(item)}>
                                        <BuildIcon/>
                                    </IconButton>
                                </Tooltip>
                                {(group !== "script" && item?.type !== "static") &&
                                    <Tooltip title="Remove Variable" variant="soft" placement="top">
                                        <IconButton
                                            color="danger"
                                            variant="soft"
                                            onClick={() => deleteVariable(item)}>
                                            <DeleteIcon/>
                                        </IconButton>
                                    </Tooltip>}

                            </td>
                        </tr>
                    ))
                ) : (
                    <tr>
                        <td colSpan={3} style={{textAlign: "center", padding: "10px"}}>
                            No data available
                        </td>
                    </tr>
                )}
                </tbody>
            </Table>
        </Sheet>
        <Modal
            open={openModal}
            onClose={(event, reason) => {
                if (reason === 'backdropClick') return;
                setOpenModal(false)
            }}>
            <ModalDialog>
                {!hideClose && <ModalClose/>}
                <Typography component="h2" fontWeight="bold">
                    {titleModal} {titleModal === "Update" ? updVarName : group} variable
                </Typography>
                <VariablesFormProvider
                    title={titleModal}
                    group={group}
                    allVariables={allVariables}
                    campaignId={campaignId}
                    variableInfo={selectedVar}
                    setOpenModal={setOpenModal}
                    setHideClose={setHideClose}
                />
            </ModalDialog>
        </Modal>
    </>)
}
