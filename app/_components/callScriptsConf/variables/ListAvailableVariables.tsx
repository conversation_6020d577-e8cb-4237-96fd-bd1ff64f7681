import React from "react";
import {Box, Typography} from "@mui/joy";
import {showSuccess} from "@/app/_components/alerts/toast/ToastMessages";

export const ListAvailableVariables: React.FC<any> = ({data}) => {
    return (
        <Box sx={{
            mb: 3,
            p: 2,
            bgcolor: 'background.level1',
            borderRadius: 'sm',
            border: '1px dashed',
            borderColor: 'divider'
        }}>
            <Typography level="body-md">
                Available variables for use:
            </Typography>
            <Typography level="body-xs" sx={{m: '10px 0', fontStyle: 'italic'}}>
                Click on a variable to copy it to clipboard. Use these variables in the description field.
            </Typography>
            <Box sx={{display: 'flex', flexWrap: 'wrap', gap: 1}}>
                {data && data.filter((v: any) => v.group === "client" || v.group === "company").map((variable: any, index: number) => (
                    <Box
                        key={index}
                        component="code"
                        sx={{
                            p: 0.5,
                            bgcolor: 'background.level2',
                            borderRadius: 'xs',
                            fontSize: 'sm',
                            cursor: 'pointer',
                            '&:hover': {bgcolor: 'primary.softHover'}
                        }}
                        onClick={() => {
                            navigator.clipboard.writeText(`\${${variable.name}}`);
                            showSuccess(`Variable ${variable.name} copied to clipboard`);
                        }}>
                        ${`{${variable.name}}`}
                    </Box>
                ))}
            </Box>
        </Box>
    )
}
