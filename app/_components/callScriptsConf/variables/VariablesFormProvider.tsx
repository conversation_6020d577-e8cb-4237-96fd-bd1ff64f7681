"use client"

import React, {useEffect, useState} from "react";
import validator from "@rjsf/validator-ajv8";
import {Box, Button} from "@mui/joy";
import {withTheme} from "@rjsf/core";
import {Theme as AntDTheme} from "@rjsf/antd";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";
import {useQueryClient} from "@tanstack/react-query";
import {buildFormVariablesScript} from "@/app/_components/callScriptsConf/funcs/buildFormVariablesScript";
import {generateId} from "@/app/_lib/utils/generateId";
import Typography from "@mui/joy/Typography";
import _ from "lodash";
import {ListAvailableVariables} from "@/app/_components/callScriptsConf/variables/ListAvailableVariables";

const ThemedForm = withTheme(AntDTheme);

export const VariablesFormProvider: React.FC<any> = ({
                                                         title,
                                                         group,
                                                         allVariables,
                                                         campaignId,
                                                         variableInfo,
                                                         setHideClose,
                                                         setOpenModal
                                                     }) => {
    const queryClient = useQueryClient();
    const [mappingForm, setMappingForm] = useState(null);
    const [loadingSubmit, setLoadingSubmit] = useState(false);
    const [varClipboard, setVarClipboard] = useState([]);

    useEffect(() => {
        const getSchema = buildFormVariablesScript(variableInfo, title, group)
        setMappingForm(getSchema)
    }, []);

    useEffect(() => {
        setVarClipboard([])
        if (title !== "Delete") {
            switch (group) {
                case "compound":
                case "script":
                    setVarClipboard(allVariables)
                    break;
                // case "compound":
                //     setVarClipboard(allVariables.filter((v: any) => v.group === "client" && v.id !== variableInfo?.id));
                //     break;
                default:
                    setVarClipboard([])
            }
        }
    }, [title]);

    const onSubmit = async (data: any) => {
        setLoadingSubmit(true);
        await upsertData(manageVariables(allVariables, data?.formData));
    }

    const normalizeName = (name: string) => _.replace(_.toLower(name), /\s+/g, "_");

    const manageVariables = (variables: any, data: any) => {
        const normalizedData = {...data, name: normalizeName(data.name), group};

        switch (title) {
            case "Add":
                return [...variables, {id: generateId(), ...normalizedData}];
            case "Update":
                return variables.map((v: any) => (v.id === data.id ? {...v, ...normalizedData} : v));
            case "Delete":
                return variables.filter((v: any) => v.id !== data.id);
            default:
                return variables;
        }
    };

    const upsertData = async (newVariables: any) => {
        setHideClose(true);

        const payload = {
            collection: 'campaigns',
            filter: {_id: campaignId},
            update: {$set: {variables: newVariables}},
            options: {upsert: true}
        };

        const updateResult = await fetch("/api/mongo/call-center/update-data", {
            method: "PUT",
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(payload)
        });

        if (updateResult.status !== 200) {
            const errorResponse = await updateResult.text();
            console.error('Failed to saving data:', errorResponse);
            showError('Problem saving variable: ' + errorResponse);
        } else {
            showSuccess('Variable saved successfully');
            queryClient.invalidateQueries({queryKey: [`data_call_campaign_${campaignId}`]});
        }

        resetValues();
    }

    const resetValues = () => {
        setLoadingSubmit(false);
        setMappingForm(null);
        setHideClose(false);
        setOpenModal(false);
    }

    return (<>
        {loadingSubmit ?
            <div className="flex justify-center mt-3">
                <LoadingMessage message="Saving data..."/>
            </div> :
            <Box sx={{
                padding: '20px'
            }}>
                {(title !== "Delete" && mappingForm) ?
                    <>
                        {(group !== "company" && varClipboard.length > 0) &&
                            (<ListAvailableVariables data={varClipboard}/>)}
                        <ThemedForm
                            schema={mappingForm?.schema}
                            formData={mappingForm?.formData}
                            validator={validator}
                            widgets={mappingForm?.widgets}
                            onSubmit={onSubmit}
                            uiSchema={mappingForm?.uiSchema}
                        />
                    </>
                    : <>
                        <Typography sx={{mt: 1}}>
                            Are you sure you want to delete the variable: &quot;{variableInfo?.name}&quot;?
                        </Typography>
                        <Box sx={{mt: 2, display: "flex", justifyContent: "flex-end"}}>
                            <Button color="neutral" onClick={() => setOpenModal(false)} sx={{mr: 2}}>
                                Cancel
                            </Button>
                            <Button
                                variant="solid"
                                color="danger"
                                onClick={() => onSubmit({formData: variableInfo})}>
                                Delete
                            </Button>
                        </Box>
                    </>}
            </Box>
        }
    </>)
}
