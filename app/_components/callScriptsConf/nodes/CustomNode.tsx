import React from 'react';
import {Handle, Position} from 'react-flow-renderer';
import {Chip, Stack, Typography} from "@mui/joy";

const CustomNode = ({data}) => {
    return (
        <div
            style={{
                borderRadius: '5px',
                width: '180px',
                padding: '10px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
            }}>
            <Handle type="target" position={Position.Top}/>
            <Stack
                spacing={1}
                sx={{
                    fontSize: '10px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    color: 'black'
                }}>
                <Typography level="body-sm">
                    {data?.label || 'No defined'}
                </Typography>
                <Chip variant="outlined" size="sm">
                    {data?.disposition || 'No defined'}
                </Chip>
            </Stack>
            <Handle type="source" position={Position.Bottom}/>
        </div>
    );
};

export default CustomNode;
