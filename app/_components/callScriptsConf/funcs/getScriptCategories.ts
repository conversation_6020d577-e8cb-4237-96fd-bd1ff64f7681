import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getScriptCategories = async (callCampaignId: string) => {
    const payload = {
        callCampaignId
    };

    const getFindSectionsResult = await fetch("/api/mongo/call-center/get-script-categories", {
        method: 'POST',
        body: JSON.stringify(payload),
        headers: {'Content-Type': 'application/json'}
    });

    if (getFindSectionsResult.ok) {
        const {response} = await getFindSectionsResult.json();
        return response || [];
    } else {
        const {error} = await getFindSectionsResult.json();
        console.error('Error to find sections data node scripts', error);
        showError(error || "Something wrong happened")
        return []
    }
}
