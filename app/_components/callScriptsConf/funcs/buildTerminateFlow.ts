import {Edge, Node} from "react-flow-renderer";
import {DataObject} from "@/app/_components/callScriptsConf/types/DataObject";
import dagre from "dagre";

export const buildTerminateFlow = (dataArray: DataObject[]): { nodes: Node[]; edges: Edge[] } => {
    const nodesMap: { [key: string]: Node } = {};
    const dagreGraph = new dagre.graphlib.Graph({compound: true});
    dagreGraph.setDefaultEdgeLabel(() => ({}));
    dagreGraph.setGraph({rankdir: 'TB', ranksep: 100, nodesep: 50});

    dataArray.forEach((nodeData, index) => {
        const {_id, objective, disposition, endType} = nodeData;
        let nodeStyle: any = {backgroundColor: 'white', border: '2px solid black'};

        if (endType === 'green') {
            nodeStyle = {backgroundColor: '#c8e6c9', border: '2px dashed green'};
        } else if (endType === 'red') {
            nodeStyle = {backgroundColor: '#FFCCCB', border: '2px dashed red'};
        }

        nodesMap[_id] = {
            id: _id,
            type: 'doubleNode',
            data: {
                label: objective,
                disposition,
            },
            position: {x: 0, y: 0},
            style: nodeStyle,
        };
        dagreGraph.setNode(_id, {width: 200, height: 150});
    });

    dagre.layout(dagreGraph);

    dagreGraph.nodes().forEach((nodeId: any) => {
        const nodeWithPosition = dagreGraph.node(nodeId);
        if (nodesMap[nodeId]) {
            nodesMap[nodeId].position = {
                x: nodeWithPosition.x - (nodeWithPosition.width || 0) / 2,
                y: nodeWithPosition.y - (nodeWithPosition.height || 0) / 2,
            };
        }
    });

    const objectsNodes = Object.values(nodesMap);

    return {nodes: objectsNodes, edges: []};
};
