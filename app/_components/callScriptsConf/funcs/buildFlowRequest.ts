import {Edge, Node} from "react-flow-renderer";
import dagre from "dagre";
import {DataObject} from "@/app/_components/callScriptsConf/types/DataObject";
import {truncateText} from "@/app/_components/callScriptsConf/funcs/truncateText";

export const buildFlowRequest = (dataArray: DataObject[]): { nodes: Node[]; edges: Edge[] } => {
    const nodesMap: { [key: string]: Node } = {};
    const edges: Edge[] = [];
    const dagreGraph = new dagre.graphlib.Graph({compound: true});
    dagreGraph.setDefaultEdgeLabel(() => ({}));
    dagreGraph.setGraph({rankdir: 'TB', ranksep: 100, nodesep: 50});

    dataArray.forEach((nodeData, index) => {
        const {_id, objective, disposition, endType} = nodeData;
        const objectiveTitle = truncateText(objective, 110);
        let nodeStyle: any = {backgroundColor: 'white', border: '2px solid black'};

        if (endType === 'green') {
            nodeStyle = {backgroundColor: '#c8e6c9', border: '2px dashed green'};
        } else if (endType === 'red') {
            nodeStyle = {backgroundColor: '#FFCCCB', border: '2px dashed red'};
        }

        nodesMap[_id] = {
            id: _id,
            type: 'doubleNode',
            data: {
                label: objectiveTitle,
                disposition,
            },
            position: {x: 0, y: 0},
            style: nodeStyle,
        };
        dagreGraph.setNode(_id, {width: 200, height: 150});
    });

    dataArray.forEach((nodeData) => {
        const {_id, responses} = nodeData;
        const parentNode = nodesMap[_id];


        if (responses) {
            responses.forEach((response: any, index: number) => {
                const {match, disposition, scriptId: targetId} = response;

                if (targetId === "currentScript" || targetId === "previousScript") {
                    const childNodeId = `${_id}-${targetId}-${index}`;

                    nodesMap[childNodeId] = {
                        id: childNodeId,
                        type: 'doubleNode',
                        data: {
                            label: targetId,
                            disposition
                        },
                        position: {
                            x: parentNode.position.x,
                            y: parentNode.position.y + 150 * (index + 1),
                        },
                        style: {backgroundColor: 'white', border: '2px solid black'},
                    };
                    dagreGraph.setNode(childNodeId, {width: 200, height: 150});

                    edges.push({
                        id: `e-${_id}-${childNodeId}`,
                        source: _id,
                        target: childNodeId,
                        data: {
                            label: match,
                            disposition
                        },
                        type: 'customEdge',
                        animated: false,
                        style: {stroke: '#000', strokeWidth: 2},
                        markerEnd: {
                            // @ts-ignore
                            type: 'arrowclosed',
                            width: 20,
                            height: 20,
                            color: '#000',
                        },
                    });
                } else if (nodesMap[targetId]) {
                    const childNode = nodesMap[targetId];
                    childNode.data.disposition = (childNode.data.disposition === 'previous') ?
                        disposition :
                        childNode.data.disposition;
                    childNode.position = {
                        x: parentNode.position.x,
                        y: parentNode.position.y + 150,
                    };
                    edges.push({
                        id: `e-${_id}-${targetId}`,
                        source: _id,
                        target: targetId,
                        data: {
                            label: match,
                            disposition
                        },
                        type: 'customEdge',
                        animated: false,
                        style: {stroke: '#000', strokeWidth: 2},
                        markerEnd: {
                            // @ts-ignore
                            type: 'arrowclosed',
                            width: 20,
                            height: 20,
                            color: '#000',
                        },
                    });
                }
            });
        }
    });

    dagre.layout(dagreGraph);

    dagreGraph.nodes().forEach((nodeId: any) => {
        const nodeWithPosition = dagreGraph.node(nodeId);
        if (nodesMap[nodeId]) {
            nodesMap[nodeId].position = {
                x: nodeWithPosition.x - (nodeWithPosition.width || 0) / 2,
                y: nodeWithPosition.y - (nodeWithPosition.height || 0) / 2,
            };
        }
    });

    const objectsNodes = Object.values(nodesMap);

    return {nodes: objectsNodes, edges};
}
