import {ArrayMappingTemplate} from "@/app/_components/customForms/templates/ArrayMappingTemplate";

export const buildFormMappingScript = (dataMapping: any, dataMapEnums: any) => {
    const {data: mapEnum} = dataMapEnums.find((item: any) => item.configuration === 'enumMaps');

    const schema = {
        type: "object",
        properties: {
            maps: {
                type: "array",
                title: "Data mapping",
                items: {
                    type: "object",
                    properties: {
                        field: {
                            type: "string",
                            title: "Original field",
                            enum: mapEnum || [],
                        },
                        key: {
                            type: "string",
                            title: "Name of the mapped field",
                            default: "",
                        },
                    }
                },
            },
            customs: {
                type: "array",
                title: "Custom fields",
                description: "Add a custom field to the mapped data",
                items: {
                    type: "object",
                    properties: {
                        key: {
                            type: "string",
                            title: "Custom field name",
                        },
                        value: {
                            type: "string",
                            title: "Custom field value",
                        },
                    },
                },
            },
        },
    };

    const uiSchema = {
        "ui:submitButtonOptions": {
            "submitText": "Save mapping",
            "norender": false,
            "props": {"type": "primary"}
        },
        maps: {
            "ui:ArrayFieldTemplate": ArrayMappingTemplate,
            items: {
                field: {
                    "ui:placeholder": "Select a field",
                },
                key: {
                    "ui:placeholder": "Enter the custom field name",
                },
                format: {
                    "ui:placeholder": "Select a format",
                },
            },
        },
        customs: {
            "ui:ArrayFieldTemplate": ArrayMappingTemplate,
            items: {
                key: {
                    "ui:placeholder": "Set the custom field name",
                },
                value: {
                    "ui:placeholder": "Set the value of the custom field name",
                },
            },
        },
    };

    const formData = dataMapping ?? {}
    const widgets = {}

    return {
        formData,
        widgets,
        schema,
        uiSchema
    }
}
