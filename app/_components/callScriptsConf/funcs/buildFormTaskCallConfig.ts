import {ObjectCallConfTabsTemplate} from "@/app/_components/customForms/templates/calls/ObjectCallConfTabsTemplate";
import {CustomSelectWidget} from "@/app/_components/customForms/widgets/CustomSelectWidget";
import {styledName} from "@/app/_components/utils/styledName";

export const buildFormTaskCallConfig = () => {
    const allTasks = ['reschedule_call', 'transferred', 'not_interested', 'transferring_call', 'incomplete_call', 'dnc', 'agents_busy', 'dnq']
    const definitions = {
        "Scripts": {
            "title": "Scripts",
            "type": "string",
            "anyOf": [
                {
                    "type": "string",
                    "enum": [
                        "script 1"
                    ],
                    "title": "Script 1"
                },
                {
                    "type": "string",
                    "enum": [
                        "script 2"
                    ],
                    "title": "Script 2"
                },
                {
                    "type": "string",
                    "enum": [
                        "script 3"
                    ],
                    "title": "Script 3"
                }
            ]
        }
    }
    const formData = {}

    const properties = {
        greetings: {
            "type": "object",
            "properties": {
                unknown: {
                    "type": "string",
                    "title": "Unknown Caller"
                },
                known: {
                    "type": "string",
                    "title": "Known Caller"
                }
            }
        },
        tasks: {
            "type": "object",
            "properties": {}
        }
    }

    const uiSchema = {
        "ui:submitButtonOptions": {
            "submitText": "Save task configuration",
            "norender": false,
            "props": {"type": "primary"}
        },
        "ui:ObjectFieldTemplate": ObjectCallConfTabsTemplate,
        greetings: {
            "unknown": {"ui:widget": "textarea", "ui:placeholder": "Enter greeting for unknown callers"},
            "known": {"ui:widget": "textarea", "ui:placeholder": "Enter greeting for known callers"},
            "ui:options": {label: false}
        },
        tasks: {"ui:options": {label: false}},
    }

    const widgets = {
        customSelectWidget: CustomSelectWidget
    }

    for (const task of allTasks) {
        properties.tasks.properties[task] = {
            "$ref": "#/definitions/Scripts",
            "title": `${styledName(task)}`
        }
        uiSchema.tasks[task] = {
            "ui:widget": "customSelectWidget"
        }
    }

    const schema = {
        definitions,
        type: 'object',
        properties: properties
    };

    return {
        schema,
        uiSchema,
        formData,
        widgets
    };
}