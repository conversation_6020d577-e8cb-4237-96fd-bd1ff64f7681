import {showError} from "@/app/_components/alerts/toast/ToastMessages";

export const getDataNodeScripts = async (tableName: string, callCampaignId: string) => {
    const payload = {
        collection: tableName,
        filter: {callCampaignId: callCampaignId}
    }

    const getItemsResult = await fetch("/api/mongo/call-center/get-all-data", {
        method: 'POST',
        body: JSON.stringify(payload),
        headers: {'Content-Type': 'application/json'}
    });

    if (getItemsResult.ok) {
        const {response} = await getItemsResult.json();
        return response || [];
    } else {
        const {error} = await getItemsResult.json();
        console.error('Error to get data node scripts', error);
        showError(error || "Something wrong happened")
        return []
    }
}
