import {generateId} from "@/app/_lib/utils/generateId";
import {DispositionsNodes} from "@/app/_components/callScriptsConf/types/DispositionsNodes";

export const buildAddUpdDndNodes = (dataNode: any) => {
    const definitions = {
        "Disposition": {
            "title": "Disposition",
            "type": "string",
            "anyOf": DispositionsNodes.map(option => ({
                type: "string",
                enum: [option],
                title: option
            }))
        }
    };

    const formData = {
        id: dataNode?.id || "",
        callCampaignId: dataNode?.callCampaignId || "",
        intent: dataNode?.intent || "",
        origin: dataNode?.origin || "orphan",
        match: dataNode?.match || "",
        objective: dataNode?.objective || "",
        disposition: dataNode?.disposition || "",
        notes: dataNode?.notes || ""
    };

    const properties = {
        id: {
            "type": "string",
            default: generateId()
        },
        callCampaignId: {
            "type": "string",
        },
        origin: {
            "type": "string",
            default: "orphan"
        },
        intent: {
            "type": "string",
            "enum": ["askingQuestion", "askingQualificationQuestion", "askingToDNCCall", "askingToCancelCall", "transferringCall", "endingCall"],
            "enumNames": ["Asking Question", "Asking Qualification Question", "Asking to DNC Call", "Asking to Cancel Call", "Transferring Call", "Ending Call"]
        },
        match: {
            "type": "string",
            "title": "If match this:"
        },
        objective: {
            "type": "string",
            "title": "Do this:",
        },
        disposition: {
            "title": "With this Disposition:",
            "$ref": "#/definitions/Disposition"
        },
        notes: {
            "type": "string",
            "title": "Notes"
        }
    };

    const uiSchema = {
        "ui:submitButtonOptions": {
            "submitText": "Add node to panel",
            "norender": false,
            "props": {
                "type": "primary"
            }
        },
        id: {
            "ui:widget": "hidden"
        },
        callCampaignId: {
            "ui:widget": "hidden"
        },
        origin: {
            "ui:widget": "hidden"
        },
        intent: {
            "ui:widget": "hidden"
        },
        match: {
            "ui:widget": "textarea",
            "ui:placeholder": "Enter a match for this node",
            "ui:options": {
                rows: 1,
                label: true
            }
        },
        objective: {
            "ui:widget": "textarea",
            "ui:placeholder": "Enter a objective for this node",
            "ui:options": {
                rows: 1,
                label: true
            }
        },
        disposition: {
            "ui:placeholder": "Select a disposition for this node"
        },
        notes: {
            "ui:widget": "textarea",
            "ui:placeholder": "Enter a note for this node",
            "ui:options": {
                rows: 2,
                label: true
            }
        }
    };

    const schema = {
        definitions,
        type: 'object',
        properties: properties
    };

    return {
        schema,
        uiSchema,
        formData,
    };
}
