import dagre from 'dagre';
import {Edge, Node} from 'react-flow-renderer';
import _ from "lodash";
import {DataObject} from "@/app/_components/callScriptsConf/types/DataObject";

export const buildFlowTask = (dataArray: DataObject[]): { nodes: Node[]; edges: Edge[] } => {
    const nodesMap: { [key: string]: Node } = {};
    const edges: Edge[] = [];
    const dagreGraph = new dagre.graphlib.Graph({compound: true});
    dagreGraph.setDefaultEdgeLabel(() => ({}));
    dagreGraph.setGraph({rankdir: 'TB', ranksep: 100, nodesep: 50});

    const nodeMap = new Map<string, DataObject>();
    dataArray.forEach(node => {
        nodeMap.set(node._id, node);
    });

    const adjacencyList = new Map<string, string[]>();
    dataArray.forEach(node => {
        if (node.endType) return;
        if (node.responses) {
            node.responses.forEach(response => {
                const targetId = response.scriptId;
                if (!adjacencyList.has(node._id)) {
                    adjacencyList.set(node._id, []);
                }
                adjacencyList.get(node._id)?.push(targetId);
            });
        }
    });

    const levels = new Map<string, number>();
    let maxParentLevel = 0;

    dataArray.forEach(node => {
        if (node.origin === 'parent') {
            levels.set(node._id, 0);
        } else if (node.origin === 'ending') {
            levels.set(node._id, maxParentLevel + 1);
        }
    });

    const queue: string[] = [];
    levels.forEach((_, nodeId) => {
        queue.push(nodeId);
    });

    while (queue.length > 0) {
        const nodeId = queue.shift()!;
        const currentLevel = levels.get(nodeId)!;

        const neighbors = adjacencyList.get(nodeId) || [];
        neighbors.forEach(neighborId => {
            if (!levels.has(neighborId) || levels.get(neighborId)! <= currentLevel) {
                levels.set(neighborId, currentLevel + 1);
                queue.push(neighborId);
            }
        });
    }

    const uniqueLevels = new Set<number>();
    levels.forEach(level => uniqueLevels.add(level));

    uniqueLevels.forEach(level => {
        dagreGraph.setNode(`rank${level}`, {rank: 'same'});
    });

    dataArray.forEach(nodeData => {
        if (nodeData.origin === 'orphan') return;

        const {_id, objective, origin, disposition, endType} = nodeData;
        let nodeStyle: any = {backgroundColor: 'white', border: '2px solid black'};
        if (origin !== 'ending') {
            if (endType === 'green') {
                nodeStyle = {backgroundColor: '#c8e6c9', border: '2px dashed green'};
            } else if (endType === 'red') {
                nodeStyle = {backgroundColor: '#FFCCCB', border: '2px dashed red'};
            }
            nodesMap[_id] = {
                id: _id,
                type: 'doubleNode',
                data: {
                    label: objective,
                    disposition,
                    isEndNode: !!endType
                },
                position: {x: 0, y: 0},
                style: nodeStyle,
            };

            dagreGraph.setNode(_id, {width: 200, height: 150});
            const level = levels.get(_id);
            if (level !== undefined) {
                dagreGraph.setParent(_id, `rank${level}`);
            } else {
                dagreGraph.setParent(_id, `rank_unreachable`);
            }
        }
    });

    dagreGraph.setNode(`rank_unreachable`, {rank: 'max'});

    dataArray.forEach(nodeData => {
        if (nodeData.origin === 'orphan') return;
        if (nodeData.endType) return;

        const {_id, responses, endType} = nodeData;

        if (responses) {
            responses.forEach((response, index) => {
                const {match, disposition, scriptId: targetId} = response;

                const endingCallNode = dataArray.find(
                    item =>
                        targetId === item._id &&
                        // Chequear si tu node debe considerarse “end” por endType, o por intent, etc.
                        (item.endType === 'red' || item.endType === 'green' || (item.intent === 'endingCall' && item.origin === 'ending'))
                );

                if (endingCallNode) {
                    const {
                        _id: specialNodeId,
                        objective,
                        disposition: disEndNode,
                        endType: finalEndType
                    } = endingCallNode;
                    const uniqueTargetId = `${specialNodeId}-${_id}-${index}`;

                    if (!nodesMap[uniqueTargetId]) {
                        let cloneStyle = {backgroundColor: '#FFCCCB', border: '2px dashed red'};
                        if (finalEndType === 'green') {
                            cloneStyle = {backgroundColor: '#c8e6c9', border: '2px dashed green'};
                        }
                        nodesMap[uniqueTargetId] = {
                            id: uniqueTargetId,
                            type: 'doubleNode',
                            data: {
                                label: objective,
                                disposition: (disEndNode === 'previous') ? disposition : disEndNode,
                                match,
                                isEndNode: true,
                                idEndNode: specialNodeId
                            },
                            position: {x: 0, y: 0},
                            style: cloneStyle,
                        };

                        dagreGraph.setNode(uniqueTargetId, {width: 200, height: 50});

                        const level = levels.get(_id)! + 1;
                        levels.set(uniqueTargetId, level);
                        dagreGraph.setParent(uniqueTargetId, `rank${level}`);
                    }

                    const edgeId = `e${_id}-${uniqueTargetId}-${index}`;
                    edges.push({
                        id: edgeId,
                        source: _id,
                        target: uniqueTargetId,
                        data: {
                            label: match,
                            match,
                            disposition,
                            idEndNode: endingCallNode?._id,
                        },
                        type: 'customEdge',
                        animated: false,
                        style: {stroke: '#000', strokeWidth: 2},
                        markerEnd: {
                            // @ts-ignore
                            type: 'arrowclosed',
                            width: 20,
                            height: 20,
                            color: '#000',
                        },
                    });
                } else if (nodesMap[targetId]) {
                    const standardEdgeId = `e${_id}-${targetId}-${index}`;

                    edges.push({
                        id: standardEdgeId,
                        source: _id,
                        target: targetId,
                        data: {
                            label: match,
                            disposition,
                            idEndNode: !_.isEmpty(endType)
                        },
                        type: 'customEdge',
                        animated: false,
                        style: {stroke: '#000', strokeWidth: 2},
                        markerEnd: {
                            // @ts-ignore
                            type: 'arrowclosed',
                            width: 20,
                            height: 20,
                            color: '#000',
                        },
                    });

                    dagreGraph.setEdge(_id, targetId);
                }
            });
        }
    });

    dagre.layout(dagreGraph);

    dagreGraph.nodes().forEach((nodeId: any) => {
        const nodeWithPosition = dagreGraph.node(nodeId);
        if (nodesMap[nodeId]) {
            nodesMap[nodeId].position = {
                x: nodeWithPosition.x - (nodeWithPosition.width || 0) / 2,
                y: nodeWithPosition.y - (nodeWithPosition.height || 0) / 2,
            };
        }
    });

    const connectedNodes = new Set<string>();
    edges.forEach(edge => {
        connectedNodes.add(edge.source);
        connectedNodes.add(edge.target);
    });

    const filteredNodes = Object.values(nodesMap).filter(node => connectedNodes.has(node.id));

    return {nodes: filteredNodes, edges};
};
