import {Edge, Node} from "react-flow-renderer";
import {DataObject} from "@/app/_components/callScriptsConf/types/DataObject";
import dagre from "dagre";
import _ from "lodash";

export const buildFlowGreetings = (dataArray: DataObject[]): { nodes: Node[]; edges: Edge[] } => {
    const nodesMap: { [key: string]: Node } = {};
    const edges: Edge[] = [];
    const dagreGraph = new dagre.graphlib.Graph({compound: true});
    dagreGraph.setDefaultEdgeLabel(() => ({}));
    dagreGraph.setGraph({rankdir: 'TB', ranksep: 100, nodesep: 50});

    const nodeMap = new Map<string, DataObject>();
    const nodeLevels: { [key: string]: number } = {};
    const allNodeIds = new Set(dataArray.map(data => data._id));

    dataArray.forEach(node => {
        nodeMap.set(node._id, node);
    });

    dataArray.forEach((nodeData) => {
        const {_id, objective, match, action, disposition, endType} = nodeData;

        let level: number;

        if (match && _.includes(match, "greeting")) {
            level = 1;
        } else if (action) {
            level = 3;
        } else {
            level = 2;
        }
        nodeLevels[_id] = level;

        nodesMap[_id] = {
            id: _id,
            type: 'doubleNode',
            data: {
                label: objective,
                disposition,
                isEndNode: !!endType
            },
            position: {x: 0, y: 0},
            style: {backgroundColor: 'white', border: '2px solid black'},
        };
        dagreGraph.setNode(_id, {width: 200, height: 150});
    });

    dataArray.forEach((nodeData) => {
        const {_id, responses} = nodeData;

        if (responses) {
            responses.forEach((response: any, index: number) => {
                if (_id !== response.scriptId && allNodeIds.has(response.scriptId)) {
                    const sourceLevel = nodeLevels[_id];
                    const targetLevel = nodeLevels[response.scriptId];
                    const {match, disposition, scriptId: targetId} = response;

                    if (
                        (sourceLevel === 1 && (targetLevel === 2 || targetLevel === 3)) ||
                        (sourceLevel === 2 && targetLevel === 3)
                    ) {
                        const standardEdgeId = `e${_id}-${targetId}-${index}`;

                        edges.push({
                            id: standardEdgeId,
                            source: _id,
                            target: targetId,
                            data: {
                                label: match,
                                disposition
                            },
                            type: 'customEdge',
                            animated: false,
                            style: {stroke: '#000', strokeWidth: 2},
                            markerEnd: {
                                // @ts-ignore
                                type: 'arrowclosed',
                                width: 20,
                                height: 20,
                                color: '#000',
                            },
                        });

                        dagreGraph.setEdge(_id, targetId);
                    }
                }
            });
        }
    });

    dagre.layout(dagreGraph);

    dagreGraph.nodes().forEach((nodeId: any) => {
        const nodeWithPosition = dagreGraph.node(nodeId);
        if (nodesMap[nodeId]) {
            nodesMap[nodeId].position = {
                x: nodeWithPosition.x - (nodeWithPosition.width || 0) / 2,
                y: nodeWithPosition.y - (nodeWithPosition.height || 0) / 2,
            };
        }
    });

    const connectedNodes = new Set<string>();
    const nodesWithParent = new Set<string>();

    edges.forEach((edge) => {
        connectedNodes.add(edge.source);
        connectedNodes.add(edge.target);
        nodesWithParent.add(edge.target);
    });

    const filteredNodes = Object.values(nodesMap).filter((node) => {
        const isConnected = connectedNodes.has(node.id);
        const hasParent = nodesWithParent.has(node.id);
        return (hasParent || isConnected);
    });

    const validNodeIds = new Set(filteredNodes.map((node) => node.id));
    const filteredEdges = edges.filter((edge) => {
        return validNodeIds.has(edge.source) && validNodeIds.has(edge.target);
    });

    return {nodes: filteredNodes, edges: filteredEdges};
};
