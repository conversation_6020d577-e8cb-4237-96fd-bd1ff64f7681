import Elk from 'elkjs/lib/elk.bundled';
import {Edge, Node, Position} from "react-flow-renderer";

const elk = new Elk();

export const getLayoutElements = async (nodes: Node[], edges: Edge[]) => {
    const initialNodes = nodes.filter(node => node.data.initial);
    const secondaryNodes = nodes.filter(node => !node.data.initial);

    const elkGraph = {
        id: "root",
        layoutOptions: {
            "elk.direction": "DOWN",
            "elk.algorithm": "layered",
            "elk.layered.layering.strategy": "INTERACTIVE",
            "elk.layered.nodePlacement.strategy": "LINEAR_SEGMENTS",
            "elk.spacing.nodeNode": "180",
            "elk.layered.spacing.nodeNodeBetweenLayers": "200",
            "elk.spacing.edgeNode": "220",
            "elk.edgeRouting": "ORTHOGONAL",
            "elk.layered.mergeEdges": "true",
            "elk.layered.considerModelOrder.strategy": "NODES_AND_EDGES"
        },
        children: [
            ...initialNodes.map((node) => ({
                id: node.id,
                width: 200, height: 150,
                layer: 0
            })),
            ...secondaryNodes.map((node) => ({
                id: node.id,
                width: 200, height: 150
            })),
        ],
        edges: edges.map((edge) => ({
            id: edge.id,
            sources: [edge.source],
            targets: [edge.target],
            layoutOptions: {
                "elk.edgeRouting": "POLYLINE",
                "elk.layered.allowSelfLoops": "true",
            },
        })),
    };

    const layout = await elk.layout(elkGraph);

    const updatedNodes = nodes.map((node) => {
        const layoutNode = layout.children?.find((n) => n.id === node.id);
        return {
            ...node,
            position: {
                x: layoutNode?.x || 0,
                y: layoutNode?.y || 0,
            },
            targetPosition: Position.Top,
            sourcePosition: Position.Bottom,
        };
    });

    return {nodes: updatedNodes, edges};
};
