export const buildAddDndNodes = () => {
    const properties = {
        objective: {
            "type": "string",
            "title": "Objective",
        }
    };

    const uiSchema = {
        "ui:submitButtonOptions": {
            "submitText": "Save",
            "norender": false,
            "props": {
                "type": "primary"
            }
        },
        objective: {
            "ui:widget": "textarea",
            "ui:placeholder": "Enter a objective for this node",
            "ui:options": {
                rows: 1,
                label: true
            }
        }
    };

    const schema = {
        type: 'object',
        properties: properties
    };

    return {
        schema,
        uiSchema
    };
}