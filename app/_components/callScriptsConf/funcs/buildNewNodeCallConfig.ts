import {CustomSelectWidget} from "@/app/_components/customForms/widgets/CustomSelectWidget";
import {generateId} from "@/app/_lib/utils/generateId";
import {DispositionsNodes} from "@/app/_components/callScriptsConf/types/DispositionsNodes";

export const buildNewNodeCallConfig = () => {
    const definitions = {
        "Disposition": {
            "title": "Disposition",
            "type": "string",
            "enum": DispositionsNodes,
            "enumNames": DispositionsNodes
        }
    };

    const formData = {};

    const properties = {
        base: {
            "type": "boolean",
            "title": "Base",
            "default": false
        },
        match: {
            "type": "string",
            "title": "Match",
            "default": "askingNewQuestion"
        },
        objective: {
            "type": "string",
            "title": "Objective",
            "minLength": 1
        },
        disposition: {
            "$ref": "#/definitions/Disposition"
        },
        responses: {
            "type": "array",
            "title": "Responses",
            "items": {
                "type": "object",
                "properties": {
                    match: {
                        "type": "string",
                        "title": "Match"
                    },
                    disposition: {
                        "$ref": "#/definitions/Disposition"
                    },
                    objective: {
                        "type": "string",
                        "title": "Objective",
                        "minLength": 1
                    },
                    scriptId: {
                        "type": "string",
                        "title": "Script ID",
                        "default": generateId()
                    }
                },
                "required": ["objective", "disposition"]
            }
        }
    };

    const uiSchema = {
        "ui:submitButtonOptions": {
            "submitText": "Save New Node",
            "norender": false,
            "props": {"type": "primary"}
        },
        base: {
            "ui:widget": "hidden"
        },
        match: {
            "ui:widget": "hidden"
        },
        objective: {
            "ui:widget": "textarea",
            "ui:placeholder": "Enter an objective for this node",
            "ui:options": {
                rows: 3,
                label: true
            }
        },
        disposition: {
            "ui:widget": "customSelectWidget"
        },
        responses: {
            items: {
                match: {
                    "ui:widget": "text"
                },
                disposition: {
                    "ui:widget": "customSelectWidget"
                },
                objective: {
                    "ui:widget": "textarea",
                    "ui:placeholder": "Enter an objective for this response",
                    "ui:options": {
                        rows: 3,
                        label: true
                    }
                },
                scriptId: {
                    "ui:widget": "hidden"
                }
            }
        }
    };

    const widgets = {
        customSelectWidget: CustomSelectWidget
    };

    const schema = {
        definitions,
        type: 'object',
        properties: properties,
        required: ["objective", "disposition"]
    };

    return {
        schema,
        uiSchema,
        formData,
        widgets
    };
};
