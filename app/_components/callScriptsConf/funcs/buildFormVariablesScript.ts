import _ from "lodash";

export const buildFormVariablesScript = (variableInfo: any, title: string, group: string) => {
    const valueGroup = ['company', 'compound']
    const schema: any = {
        type: "object",
        properties: {
            name: {
                type: "string",
                title: "Name",
            },
            type: {
                type: "string",
                title: "Type",
                default: "dynamic",
            },
        }
    }

    if (group !== "compound") {
        schema.properties.details = {
            type: "string",
            title: "Details",
        }
    }

    if (_.includes(valueGroup, group)) {
        schema.properties.value = {
            type: "string",
            title: "Value",
        }
    }

    const uiSchema: any = {
        "ui:submitButtonOptions": {
            "submitText": "Save variable",
            "norender": false,
            "props": {"type": "primary"}
        },
        name: {
            "ui:placeholder": "Enter variable name"
        },
        details: {
            "ui:placeholder": "Description of the variable"
        },
        value: {
            "ui:placeholder": "Value of the variable for take"
        },
        type: {
            "ui:widget": "hidden"
        },
    };

    if (title !== "Add" && variableInfo?.type === "static") {
        uiSchema.name = {
            ...uiSchema.name,
            "ui:widget": "hidden"
        }
    }

    if (group !== "company") {
        uiSchema.details = {
            ...uiSchema.details,
            "ui:widget": "textarea",
            "ui:options": {
                rows: 5,
                label: true
            }
        }
    }

    const formData = (title !== 'Add') ? variableInfo : {}
    const widgets = {}

    return {
        formData,
        widgets,
        schema,
        uiSchema
    }
}
