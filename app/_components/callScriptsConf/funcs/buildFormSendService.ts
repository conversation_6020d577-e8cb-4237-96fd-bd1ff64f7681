export const buildFormSendService = (dataSendService: any, isNew: boolean) => {
    const formData = dataSendService ?? {}
    const schema = {
        type: "object",
        properties: {
            description: {
                type: "string",
                title: "Description"
            },
            url: {
                type: "string",
                title: "URL"
            },
            method: {
                type: "string",
                title: "Method",
                enum: ["POST", "GET"]
            },
            headers: {
                type: "string",
                title: "Headers"
            },
            type: {
                type: "string",
                title: "Type",
                anyOf: [
                    {
                        title: "Webhook",
                        enum: ["webhook"]
                    },
                    {
                        title: "Petition",
                        enum: ["petition"]
                    }
                ]
            }
        }
    };

    const uiSchema = {
        "ui:submitButtonOptions": {
            "submitText": isNew ? "Next" : "Update",
            "norender": false,
            "props": {
                "disabled": false,
                "className": "bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded inline-flex items-center"
            },
        },
        description: {
            "ui:placeholder": "Enter a description for this event"
        },
        url: {
            "ui:placeholder": "Enter the URL"
        },
        method: {
            "ui:placeholder": "Select the method",
            "ui:enumNames": ["POST", "GET"]
        },
        headers: {
            "ui:placeholder": "Enter the headers"
        },
        type: {
            "ui:placeholder": "Select the type"
        }
    };

    const widgets = {}

    return {
        formData,
        widgets,
        schema,
        uiSchema
    }
}
