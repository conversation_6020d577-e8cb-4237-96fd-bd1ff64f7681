import _ from "lodash";

type Item = {
    match: string;
    scriptId: string;
    disposition: string;
    objective?: string;
    endNode?: boolean;
    isNewNode?: boolean;
    typeFD?: string;
};

export function fixResponses(items: Item[]): Omit<Item, 'objective' | 'endNode' | 'isNewNode' | 'typeFD'>[] {
    const cleanedItems = items.map(({objective, endNode, isNewNode, typeFD, ...rest}) => rest);
    return _.uniqBy(cleanedItems, 'scriptId');
}