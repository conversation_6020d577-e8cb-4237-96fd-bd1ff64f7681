import _ from "lodash";
import { DispositionsNodes } from "@/app/_components/callScriptsConf/types/DispositionsNodes";
import ThenTemplate from "@/app/_components/callScriptsConf/types/ThenTemplate";

export const buildUpdNodeForm = (
    dataNode: any,
    type: string,
    field: string,
    idsSearch: any,
    childLabel: string
) => {
    const definitions = {
        "Disposition": {
            "title": "Disposition",
            "type": "string",
            "anyOf": DispositionsNodes.map(option => ({
                type: "string",
                enum: [option],
                title: option
            }))
        }
    };

    let properties = {};
    const uiSchema: any = {
        "ui:submitButtonOptions": {
            "submitText": "Update Node",
            "norender": false,
            "props": { "type": "primary" }
        }
    };
    let formData = {};

    switch (field) {
        case 'match':
            properties = {
                match: {
                    type: "string",
                    title: "IF"
                },
                then: {
                    type: "object",
                    title: "Then",
                    properties: {
                        disposition: {
                            title: "-Set",
                            "$ref": "#/definitions/Disposition"
                        },
                        childLabel: {
                            type: "string",
                            title: '-' + child<PERSON>abel,
                            readOnly: true,
                            default: child<PERSON>abel
                        }
                    }
                }
            };

            uiSchema.match = {
                "ui:placeholder": "How the IA can go for this node"
            };

            uiSchema.then = {
                "ui:ObjectFieldTemplate": ThenTemplate,
                "ui:order": ["disposition", "childLabel"],
                "ui:options": { childLabelValue: childLabel },
                childLabel: {
                    "ui:readonly": true
                }
            };

            const getDisp = getFormData(dataNode, type, "disposition", idsSearch);
            formData = {
                match: getFormData(dataNode, type, "match", idsSearch) || "",
                then: {
                    disposition: getDisp !== null && getDisp !== "" ? getDisp : "silent",
                    childLabel: childLabel
                }
            };
            break;

        case 'objective':
            properties = {
                objective: {
                    type: "string",
                    title: "Objective of AI"
                }
            };
            uiSchema.objective = {
                "ui:widget": "textarea",
                "ui:placeholder": "Enter an objective for this node",
                "ui:options": {
                    rows: 1,
                    label: true
                }
            };
            const getObjective = getFormData(dataNode, type, field, idsSearch);
            formData = {
                objective: getObjective || null,
            }
            break
        case 'disposition':
            properties = {
                disposition: {
                    "title": "Disposition",
                    "$ref": "#/definitions/Disposition",
                }
            }
            uiSchema.disposition = {
                "ui:placeholder": "Select a disposition for this node",
            }
            const getDisposition = getFormData(dataNode, type, field, idsSearch)
            formData = {
                disposition: getDisposition
            }
            break;
    }

    const schema = {
        definitions,
        type: 'object',
        properties: properties
    };

    return {
        schema,
        uiSchema,
        formData,
    };
};

const getFormData = (dataNode: any, type: string, field: string, idsSearch: any) => {
    if (type === 'node') {
        return dataNode[field] || null;
    } else {
        const getResp = (!idsSearch?.match)
            ? dataNode?.responses?.find((resp: any) => resp.scriptId === idsSearch.target)
            : dataNode?.responses?.find((resp: any) => resp.scriptId === idsSearch.target && resp.match === idsSearch.match);
        return _.has(getResp, field) ? getResp[field] : null;
    }
};