import {Edge, Node} from "react-flow-renderer";
import {DataObject} from "@/app/_components/callScriptsConf/types/DataObject";
import {truncateText} from "@/app/_components/callScriptsConf/funcs/truncateText"
import dagre from "dagre";

export const buildFlowCallScripts = (dataArray: DataObject[], typeDiagram: boolean): { nodes: Node[]; edges: Edge[] } => {
    const nodesMap: { [key: string]: Node } = {};
    const edges: Edge[] = [];
    const edgesSet: Set<string> = new Set();
    const getMatch = typeDiagram ? 'greeting' : 'greetingNoName';

    const dagreGraph = new dagre.graphlib.Graph();
    dagreGraph.setDefaultEdgeLabel(() => ({}));
    dagreGraph.setGraph({rankdir: 'TB', ranksep: 100, nodesep: 50});

    const startNode = dataArray.find((node) => node?.match && node?.match === getMatch);

    const processNode = (currentNodeId: string) => {
        const currentNode = dataArray.find((node) => node._id === currentNodeId);
        if (!currentNode) return;

        const {_id, objective, endType, responses, disposition} = currentNode;

        if (!nodesMap[_id]) {
            let nodeStyle: any = {backgroundColor: 'white', border: '2px solid black'};
            if (endType === 'green') {
                nodeStyle = {backgroundColor: '#c8e6c9', border: '2px dashed green'};
            } else if (endType === 'red') {
                nodeStyle = {backgroundColor: '#FFCCCB', border: '2px dashed red'};
            }

            const truncatedTitle = truncateText(objective, 110);

            nodesMap[_id] = {
                id: _id,
                type: 'doubleNode',
                data: {
                    label: truncatedTitle,
                    disposition,
                },
                position: {x: 0, y: 0},
                style: nodeStyle,
            };

            dagreGraph.setNode(_id, {width: 200, height: 150});
        }

        if (responses) {
            responses.forEach((response: any) => {
                const {match, scriptId: targetId, disposition: targetDisposition} = response;

                if (!targetId) return;

                const edgeId = `e${_id}-${targetId}`;

                if (!edgesSet.has(edgeId)) {
                    edgesSet.add(edgeId);

                    edges.push({
                        // @ts-ignore
                        id: edgeId,
                        source: _id,
                        target: targetId,
                        data: {
                            label: match,
                            disposition: targetDisposition
                        },
                        type: 'customEdge',
                        animated: false,
                        style: {stroke: '#000', strokeWidth: 2},
                        markerEnd: {
                            // @ts-ignore
                            type: 'arrowclosed',
                            width: 20,
                            height: 20,
                            color: '#000',
                        },
                    });

                    dagreGraph.setEdge(_id, targetId);

                    processNode(targetId);
                }
            });
        }
    };

    processNode(startNode._id);

    dagre.layout(dagreGraph);

    dagreGraph.nodes().forEach((nodeId: any) => {
        const nodeWithPosition = dagreGraph.node(nodeId);
        if (nodesMap[nodeId]) {
            nodesMap[nodeId].position = {
                x: nodeWithPosition.x - (nodeWithPosition.width || 0) / 2,
                y: nodeWithPosition.y - (nodeWithPosition.height || 0) / 2,
            };
        }
    });

    const validNodeIds = new Set(Object.keys(nodesMap));
    const filteredEdges = edges.filter((edge) => validNodeIds.has(edge.source) && validNodeIds.has(edge.target));

    return {nodes: Object.values(nodesMap), edges: filteredEdges};
};
