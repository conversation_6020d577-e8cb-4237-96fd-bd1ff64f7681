import {generateId} from "@/app/_lib/utils/generateId";
import _ from "lodash";

type Item = {
    match: string;
    scriptId: string;
    disposition: string;
    objective?: string;
    endNode?: boolean;
    isNewNode?: boolean;
    typeFD?: string;
    [key: string]: any;
};

export function findDifferences(array1: Item[], array2: Item[]): {
    nodesRemove: Item[],
    newNodes: Item[],
    updatedNodes: Item[]
} {
    const scriptIdsInArray2 = new Set<string>();

    const sanitizedArray2 = array2.map(item => {
        if (scriptIdsInArray2.has(item.scriptId)) {
            return {...item, scriptId: generateId()};
        } else {
            scriptIdsInArray2.add(item.scriptId);
            return item;
        }
    });
    const sanitizedMap = new Map(sanitizedArray2.map(item => [item.scriptId, item]));

    const nodesRemove = array1.filter(item => !sanitizedMap.has(item.scriptId));
    const newNodes = sanitizedArray2.filter(item => item.isNewNode === true && item.typeFD !== 'selected');

    const updatedNodesFromSelection = sanitizedArray2.filter(item => item.typeFD === 'selected'); // Nodos con `typeFD === 'selected'`

    const updatedNodesFromComparison = array1
        .filter(item => sanitizedMap.has(item.scriptId))
        .filter(item => {
            const correspondingNode = sanitizedMap.get(item.scriptId);
            return (
                correspondingNode &&
                !_.isEqual(_.omit(item, ['isNewNode', 'typeFD']), correspondingNode)
            );
        })
        .map(item => sanitizedMap.get(item.scriptId)!);
    const updatedNodes = _.uniqBy(
        [...updatedNodesFromSelection, ...updatedNodesFromComparison],
        'scriptId'
    );

    return { nodesRemove, newNodes, updatedNodes };
}
