'use client'
import React, {useEffect, useState} from "react";
import {Box} from "@mui/joy";
import {Node, ReactFlowProvider} from "react-flow-renderer";
import {buildFlowCallScripts} from "@/app/_components/callScriptsConf/funcs/buildFlowCompleteDiagram";
import {getLayoutElements} from "@/app/_components/callScriptsConf/funcs/getLayoutedElements";
import SwitchCompleteDiagram from "@/app/_components/callScriptsConf/addCompt/SwitchCompleteDiagram";
import {DnDProvider} from "@/app/_components/callScriptsConf/dnd/DndContext";
import {ReactFlowView} from "@/app/_components/callScriptsConf/dnd/ReactFlowView";
import Typography from "@mui/joy/Typography";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";

export const CallConfigurationProvider: React.FC<any> = ({
                                                             campaignData,
                                                             tableName,
                                                             callCampaignId,
                                                             isDev
                                                         }) => {
    const [typeDiagram, setTypeDiagram] = useState(true);
    const [nodesRF, setNodesRF] = useState([]);
    const [edgesRF, setEdgesRF] = useState([]);
    const [loadingType, setLoadingType] = useState(false);

    useEffect(() => {
        if (campaignData && campaignData.length > 0) {
            setLoadingType(true);
            const getBuilt = buildFlowCallScripts(campaignData, typeDiagram);
            getLayoutElements(getBuilt.nodes, getBuilt.edges).then(({nodes, edges}) => {
                setNodesRF(nodes);
                setEdgesRF(edges);
                setLoadingType(false);
            });
        }
    }, [campaignData, typeDiagram]);

    return (
        <Box
            sx={{
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#fafafa',
                textAlign: 'center',
                padding: "0 4",
            }}>
            {(nodesRF.length > 0 && edgesRF.length > 0) && <>
                <Typography level="body-md" sx={{mb: 1}}>
                    View Complete Diagram by
                </Typography>
                <div className={'mb-5'}>
                    <SwitchCompleteDiagram typeDiagram={typeDiagram} setTypeDiagram={setTypeDiagram}/>
                </div>
                {loadingType ? <LoadingMessage message="Loading diagram..."/> :
                    <ReactFlowProvider>
                        <DnDProvider>
                            <ReactFlowView
                                nodesRF={nodesRF}
                                edgesRF={edgesRF}
                                orphanNodes={[]}
                                endCallNode={[]}
                                terminateData={[]}
                                callCampaignId={callCampaignId}
                                isDev={isDev}
                                data={campaignData}
                                tableName={tableName}
                                viewDnd={false}
                                onlyView={true}
                            />
                        </DnDProvider>
                    </ReactFlowProvider>}
            </>
            }
        </Box>
    )
        ;
}
