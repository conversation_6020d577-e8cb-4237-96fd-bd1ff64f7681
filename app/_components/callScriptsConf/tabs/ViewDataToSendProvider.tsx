import React, {useState} from "react";
import {Box, But<PERSON>, Stack} from "@mui/joy";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {DataTriggers} from "@/app/_components/queries/DataTriggers";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";
import {StepperTrigger} from "@/app/_components/callScriptsConf/triggers/StepperTrigger";
import {EventSelectionStep} from "@/app/_components/callScriptsConf/triggers/EventSelectionStep";
import {ServiceSendDataConf} from "@/app/_components/callScriptsConf/triggers/ServiceSendDataConf";
import {ViewMappingProvider} from "@/app/_components/callScriptsConf/triggers/ViewMappingProvider";
import {EventListView} from "@/app/_components/callScriptsConf/triggers/EventListView";


export const ViewDataToSendProvider: React.FC<any> = ({callCampaignId}) => {
    const [showSteps, setShowSteps] = useState(false);
    const [activeStep, setActiveStep] = useState<number>(0);
    const [subsSelected, setSubsSelected] = useState(null);
    const [eventShowing, setEventShowing] = useState("subscriptions");
    const queryClient = useQueryClient();

    const {data, isFetching} = useQuery(DataTriggers(callCampaignId));

    const handleStartSteps = (sub = null,) => {
        setSubsSelected(sub);
        setShowSteps(true);
        setActiveStep(0);
    };

    const handleBack = () => {
        const newStep = activeStep - 1;
        setActiveStep(newStep);
        if (newStep <= 0) {
            setShowSteps(false);
            setSubsSelected(null);
            setEventShowing("subscriptions");
        }
    };

    const handleFinish = () => {
        setShowSteps(false);
        setActiveStep(0);
        setSubsSelected(null);
        setEventShowing("subscriptions");
        queryClient.invalidateQueries({queryKey: [`data_triggers_${callCampaignId}`]});
    };

    return (
        <>
            {isFetching ? <LoadingMessage message="Loading..."/> : (
                <Box sx={{maxWidth: 1000, mx: "auto", p: 2}}>
                    {showSteps ? (<>
                        <StepperTrigger activeStep={activeStep}/>

                        {activeStep === 0 &&
                            <EventListView
                                eventsData={data?.events}
                                activeSubs={data?.subscriptions}
                                logsData={data?.logs}
                                campaignId={callCampaignId}
                                mapVariables={data?.mapVariables}
                                setActiveStep={setActiveStep}
                                setSubsSelected={setSubsSelected}
                            />}

                        {activeStep === 1 && (
                            <ServiceSendDataConf
                                subsSelected={subsSelected}
                                setActiveStep={setActiveStep}
                                isNew={true}
                            />
                        )}

                        {activeStep === 2 && (
                            <ViewMappingProvider subsSelected={subsSelected}/>
                        )}

                        <Stack direction="row" spacing={2} justifyContent="space-between" mt={4}>
                            <Button
                                color="neutral"
                                onClick={handleBack}
                            >
                                Back
                            </Button>
                            {activeStep === 2 &&
                                <Button
                                    color="neutral"
                                    onClick={handleFinish}
                                >
                                    Finish
                                </Button>}
                        </Stack>
                    </>) : (<>
                        {eventShowing === "subscriptions" && (
                            <EventSelectionStep
                                activeSubs={data?.subscriptions}
                                campaignId={callCampaignId}
                                logsData={data?.logs}
                                setShowSteps={handleStartSteps}
                                setSubsSelected={setSubsSelected}
                                setEventShowing={setEventShowing}
                            />
                        )}

                        {eventShowing === "data_sent" && (
                            <ServiceSendDataConf
                                subsSelected={subsSelected}
                                handleFinish={handleFinish}
                                isNew={false}
                            />)}

                        {eventShowing === "data_map" && (<ViewMappingProvider subsSelected={subsSelected}/>)}

                        {eventShowing !== "subscriptions" && (
                            <Box sx={{mt: 4}}>
                                <Button
                                    color="neutral"
                                    onClick={handleBack}
                                >
                                    Back
                                </Button>
                            </Box>
                        )}
                    </>)}
                </Box>
            )}
        </>
    );
};
