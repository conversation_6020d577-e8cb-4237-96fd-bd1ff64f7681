import React, {useEffect, useState} from "react";
import {Box} from "@mui/joy";
import {Node, ReactFlowProvider} from "react-flow-renderer";
import {buildFlowTask} from "@/app/_components/callScriptsConf/funcs/buildFlowTask";
import {getLayoutElements} from "@/app/_components/callScriptsConf/funcs/getLayoutedElements";
import {ReactFlowView} from "@/app/_components/callScriptsConf/dnd/ReactFlowView";
import {DnDProvider} from "@/app/_components/callScriptsConf/dnd/DndContext";
import _ from "lodash";

export const TasksConfiguration: React.FC<any> = ({
                                                      data,
                                                      isDev,
                                                      allData,
                                                      terminateData,
                                                      tableName,
                                                      callCampaignId
                                                  }) => {
    // console.log('data')
    // console.log(data)
    const [tasks, setTasks] = useState(data ?? []);
    const [nodesRF, setNodesRF] = useState([]);
    const [edgesRF, setEdgesRF] = useState([]);
    const [originalEdges, setOriginalEdges] = useState([]);
    const [endCallNode, setEndCallNode] = useState({});
    const [orphanNodes, setOrphanNodes] = useState([]);
    const [selectedNode, setSelectedNode] = useState<Node | null>(null);

    useEffect(() => {
        if (tasks && tasks.length > 0) {
            const getBuilt = buildFlowTask(tasks);
            setOrphanNodes(findOrphanNodes(tasks));
            setEndCallNode(findEndNode(tasks));
            getLayoutElements(getBuilt.nodes, getBuilt.edges).then(({nodes, edges}) => {
                setNodesRF(nodes);
                setOriginalEdges(edges);
                setEdgesRF(edges);
            });
        }
    }, [tasks]);

    useEffect(() => {
        if (selectedNode) {
            const updatedEdges = originalEdges.map(edge => {
                if (edge.source === selectedNode.id) {
                    return {
                        ...edge,
                        style: {...edge.style, stroke: 'red', strokeWidth: 2},
                        animated: false,
                    };
                }
                return edge;
            });
            setEdgesRF(updatedEdges);
        } else {
            setEdgesRF(originalEdges);
        }
    }, [selectedNode, originalEdges]);

    const findOrphanNodes = (dataNodes: any) => {
        const referencedIds = new Set(
            dataNodes.flatMap((item: any) =>
                (item.responses || []).map((response: any) =>
                    response.scriptId))
        );
        return dataNodes.filter((item: any) =>
            !referencedIds.has(item._id) && item.origin !== "parent" && _.isEmpty(item?.endType)
        );
    }

    const findEndNode = (dataNodes: any) => {
        return dataNodes.find((item: any) =>
            item.intent === 'endingCall' &&
            item.origin === 'ending'
        )
    }

    return (
        <Box
            sx={{
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#fafafa',
                textAlign: 'center',
                padding: 2,
            }}>

            {(nodesRF.length > 0 && edgesRF.length > 0) &&
                <ReactFlowProvider>
                    <DnDProvider>
                        <ReactFlowView
                            nodesRF={nodesRF}
                            edgesRF={edgesRF}
                            orphanNodes={orphanNodes}
                            endCallNode={endCallNode}
                            terminateData={terminateData}
                            callCampaignId={callCampaignId}
                            isDev={isDev}
                            data={data}
                            tableName={tableName}
                            viewDnd={true}
                        />
                    </DnDProvider>
                </ReactFlowProvider>}
        </Box>
    );
};
