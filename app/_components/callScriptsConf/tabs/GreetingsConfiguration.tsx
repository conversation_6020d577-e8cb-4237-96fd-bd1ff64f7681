'use client';

import React, {useEffect, useState} from "react";
import {Box} from "@mui/joy";
import {ReactFlowProvider,} from "react-flow-renderer";
import {DnDProvider} from "@/app/_components/callScriptsConf/dnd/DndContext";
import {buildFlowGreetings} from "@/app/_components/callScriptsConf/funcs/buildFlowGreetings";
import {ReactFlowView} from "@/app/_components/callScriptsConf/dnd/ReactFlowView";
import {getLayoutElements} from "@/app/_components/callScriptsConf/funcs/getLayoutedElements";

export const GreetingsConfiguration: React.FC<any> = ({
                                                          data,
                                                          isDev,
                                                          terminateData,
                                                          tableName,
                                                          callCampaignId
                                                      }) => {
    const [nodesRF, setNodesRF] = useState([]);
    const [edgesRF, setEdgesRF] = useState([]);

    useEffect(() => {
        if (data && data.length > 0) {
            const getBuilt = buildFlowGreetings(data);
            // setNodesRF(getBuilt.nodes);
            // setEdgesRF(getBuilt.edges);
            getLayoutElements(getBuilt.nodes, getBuilt.edges).then(({nodes, edges}) => {
                setNodesRF(nodes);
                setEdgesRF(edges);
            });
        }
    }, [data]);

    return (
        <Box
            sx={{
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "#fafafa",
                textAlign: "center",
                padding: 4,
            }}>
            {(nodesRF.length > 0 && edgesRF.length > 0) &&
                <ReactFlowProvider>
                    <DnDProvider>
                        <ReactFlowView
                            nodesRF={nodesRF}
                            edgesRF={edgesRF}
                            orphanNodes={[]}
                            endCallNode={[]}
                            terminateData={terminateData}
                            callCampaignId={callCampaignId}
                            isDev={isDev}
                            data={data}
                            tableName={tableName}
                            viewDnd={false}
                        />
                    </DnDProvider>
                </ReactFlowProvider>}
        </Box>
    );
};
