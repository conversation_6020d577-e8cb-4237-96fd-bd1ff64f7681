'use client';
import React, { useState, useRef, useEffect } from 'react';
import Box from '@mui/joy/Box';
import IconButton from '@mui/joy/IconButton';
import BuildIcon from '@mui/icons-material/Build';
import DeleteIcon from '@mui/icons-material/Delete';
import Modal from '@mui/joy/Modal';
import ModalDialog from '@mui/joy/ModalDialog';
import ModalClose from '@mui/joy/ModalClose';
import Button from '@mui/joy/Button';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import Textarea from '@mui/joy/Textarea';
import CircularProgress from '@mui/joy/CircularProgress';
import Typography from '@mui/joy/Typography';
import { showError, showSuccess } from '@/app/_components/alerts/toast/ToastMessages';
import { jsPDF } from "jspdf";

type Item = {
    _id: string;
    topic: string;
    a: string;
    vector?: number[];
    norm?: number;
    call_campaign_id?: string;
};

const buildLocalIndexConfig = (items: Item[], campaignId: string) => ({
    call_campaign_id: campaignId,
    version: 1,
    metadataConfig: {},
    items: items.map(obj => ({
        vector: obj.vector || [],
        norm: obj.norm || 0,
        metadata: {
            id: obj._id,
            topic: obj.topic,
            a: obj.a,
        },
    })),
});

export const KnowledgeConfiguration: React.FC<{
    callCampaignId: string;
    config: Item[] | null;
}> = ({ callCampaignId, config }) => {
    const [items, setItems] = useState<Item[]>(config || []);
    const [originalItems, setOriginalItems] = useState<Item[]>(config || []);
    const [openEditModal, setOpenEditModal] = useState(false);
    const [selectedItem, setSelectedItem] = useState<Item | null>(null);
    const [editedTopic, setEditedTopic] = useState('');
    const [editedAnswer, setEditedAnswer] = useState('');
    const [inputLoading, setInputLoading] = useState(false);
    const [openDeleteModal, setOpenDeleteModal] = useState(false);
    const [itemToDelete, setItemToDelete] = useState<Item | null>(null);
    const [loading, setLoading] = useState(false);
    const [openNewItemModal, setOpenNewItemModal] = useState(false);
    const [newTopic, setNewTopic] = useState('');
    const [newAnswer, setNewAnswer] = useState('');
    const [inputValue, setInputValue] = useState('');
    const [hasSearched, setHasSearched] = useState(false);

    useEffect(() => {
        setItems(config || []);
        setOriginalItems(config || []);
    }, [config]);

    const fileInputRef = useRef<HTMLInputElement>(null);
    const handleUploadClick = () => fileInputRef.current?.click();

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        if (!event.target.files?.length) return;
        const file = event.target.files[0];

        const formData = new FormData();
        formData.append('pdf', file);
        formData.append('callCampaignId', callCampaignId);

        setLoading(true);
        try {
            const uploadRes = await fetch('/api/gumloop/upload-pdf', {
                method: 'POST',
                body: formData,
            });

            if (!uploadRes.ok) throw new Error('Error uploading PDF.');
            const data = await uploadRes.json();
            showSuccess('PDF uploaded successfully.');

            const formattedRes = await fetch('/api/mongo/call-center/create-knowledge-config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ callCampaignId, data }),
            });

            if (!formattedRes.ok) throw new Error('Error formatting items.');
            const formattedItems: Item[] = await formattedRes.json();

            const mergedItems = (() => {
                const existingIds = new Set(items.map(it => it._id));
                const merged = [...items];
                formattedItems.forEach(it => {
                    if (!existingIds.has(it._id)) merged.push(it);
                });
                return merged;
            })();

            setItems(mergedItems);
            setOriginalItems(mergedItems);
            showSuccess('Knowledge configuration updated successfully.');
        } catch (err: any) {
            console.error(err);
            showError(err.message || 'Unexpected error.');
        } finally {
            setLoading(false);
        }
    };

    const handleExport = () => {
        const filteredItems = items.map(item => ({
            _id: item._id,
            topic: item.topic,
            a: item.a
        }));
        let content = "Knowledge Base Configuration:\n\n";
        filteredItems.forEach(item => {
            content += `ID: ${item._id}\nTopic: ${item.topic}\nAnswer: ${item.a}\n\n`;
        });
        const doc = new jsPDF();
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const margin = 10;
        const lines = doc.splitTextToSize(content, pageWidth - margin * 2);
        let y = margin;
        lines.forEach(line => {
            if (y > pageHeight - margin) {
                doc.addPage();
                y = margin;
            }
            doc.text(line, margin, y);
            y += 10;
        });
        doc.save('export_configuration.pdf');
    };

    const editItem = (item: Item) => {
        setSelectedItem(item);
        setEditedTopic(item.topic);
        setEditedAnswer(item.a);
        setOpenEditModal(true);
    };

    const promptDeleteItem = (item: Item) => {
        setItemToDelete(item);
        setOpenDeleteModal(true);
    };

    const confirmDelete = async () => {
        if (!itemToDelete) return;
        try {
            const res = await fetch('/api/mongo/call-center/delete-vectra-item', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ _id: itemToDelete._id }),
            });
            const data = await res.json();
            if (!data.response) throw new Error(data.error || 'Error deleting item.');

            showSuccess('Item deleted successfully.');
            setItems(prev => {
                const updated = prev.filter(it => it._id !== itemToDelete._id);
                setOriginalItems(updated);
                return updated;
            });
        } catch (err: any) {
            console.error(err);
            showError(err.message || 'Error deleting item.');
        } finally {
            setOpenDeleteModal(false);
            setItemToDelete(null);
        }
    };

    const handleSave = async () => {
        if (!selectedItem) return;

        const updatedItem = { ...selectedItem, topic: editedTopic, a: editedAnswer };
        const updatedItems = items.map(it => (it._id === selectedItem._id ? updatedItem : it));
        setItems(updatedItems);
        setOriginalItems(updatedItems);

        try {
            const res = await fetch('/api/mongo/call-center/update-vectra-object', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ _id: updatedItem._id, item: updatedItem }),
            });
            const data = await res.json();
            if (!data.response) throw new Error('Error updating item.');
            showSuccess('Item updated successfully.');
        } catch (err: any) {
            console.error(err);
            showError(err.message || 'Error updating item.');
        } finally {
            setOpenEditModal(false);
        }
    };

    const handleAddNewItem = () => {
        setNewTopic('');
        setNewAnswer('');
        setOpenNewItemModal(true);
    };

    const handleSaveNewItem = async () => {
        if (!newTopic || !newAnswer) {
            showError('Please fill in all fields.');
            return;
        }

        setLoading(true);
        const newItem = { topic: newTopic, a: newAnswer };

        try {
            const res = await fetch('/api/mongo/call-center/add-knowledge-item', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ item: newItem, callCampaignId }),
            });
            const data = await res.json();
            if (!data.response) throw new Error('Error adding item.');

            showSuccess('Item added successfully.');
            setItems(prev => {
                const updated = [...prev, data.item];
                setOriginalItems(updated);
                return updated;
            });
            setOpenNewItemModal(false);
        } catch (err: any) {
            console.error(err);
            showError(err.message || 'Error adding item.');
        } finally {
            setLoading(false);
        }
    };

    const handleSendInput = async () => {
        setInputLoading(true);
        try {
            if (!inputValue.trim()) {
                setItems(originalItems);
                return;
            }
            const campaignIdFromItems =
                Array.isArray(items) && items.length > 0 && items[0].call_campaign_id
                    ? items[0].call_campaign_id
                    : 'unknownCallCampaign';

            const localIndexConfig = buildLocalIndexConfig(originalItems, campaignIdFromItems);

            const response = await fetch("/api/vectra/testKnowledgeBase", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    message: inputValue,
                    config: localIndexConfig,
                }),
            });

            if (!response.ok) {
                throw new Error("AI chat request failed.");
            }

            const result = await response.json();
            if (Array.isArray(result.data)) {
                const filteredItems: Item[] = result.data.map((obj: any) => ({
                    _id: obj.id,
                    topic: obj.topic,
                    a: obj.a
                }));
                setItems(filteredItems);
                setHasSearched(true);
            } else {
                console.warn("Unexpected result format:", result);
            }
        } catch (err: any) {
            console.error("Error sending input to AI chat:", err.message);
        } finally {
            setInputLoading(false);
        }
    };

    return (
        <>
            <Box
                sx={{
                    border: '1px solid #e4e4e4',
                    borderRadius: '10px',
                    p: 2,
                    mb: 2,
                    textAlign: 'center',
                    backgroundColor: '#f9f9f9',
                }}
            >
                <p>{originalItems.length ? 'Knowledge configuration already exists.' : 'There is no knowledge configuration yet.'}</p>
                <Button variant="solid" color="neutral" onClick={handleUploadClick}>
                    {items.length ? 'Update' : 'Upload'}
                </Button>
                <input
                    type="file"
                    accept="application/pdf"
                    ref={fileInputRef}
                    style={{ display: 'none' }}
                    onChange={handleFileChange}
                />
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Button variant="solid" color="neutral" onClick={handleAddNewItem}>
                    Add New Item
                </Button>
                {items.length > 0 && (
                    <Button variant="solid" color="neutral" onClick={handleExport}>
                        Export configuration
                    </Button>
                )}
            </Box>
            {items.length > 0 && (
                <>
                    <Box sx={{ mt: 2, display: 'flex', gap: 2, alignItems: 'center', mb: 2 }}>
                        <input
                            type="text"
                            placeholder="Test knowledge base"
                            value={inputValue}
                            onChange={(e) => setInputValue(e.target.value)}
                            style={{
                                padding: '8px',
                                borderRadius: '4px',
                                border: '1px solid #ccc',
                                flex: 1,
                            }}
                        />
                        <Button onClick={handleSendInput} variant="solid" color="primary">
                            Send
                        </Button>
                    </Box>
                    {hasSearched && (
                        <Button
                            onClick={() => {
                                setItems(originalItems);
                                setInputValue('');
                                setHasSearched(false);
                            }}
                            variant="outlined"
                            color="neutral"
                            sx={{ mb: 2 }}
                        >
                            Restart Search
                        </Button>
                    )}
                </>
            )}
            {items.length > 0 && (
                <Box
                    sx={{
                        backgroundColor: '#fafafa',
                        border: '1px solid #e4e4e4',
                        borderRadius: '10px',
                        p: 2,
                        minHeight: '200px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: inputLoading ? 'center' : 'normal',
                    }}
                >
                    {inputLoading ? (
                        <CircularProgress size="lg" />
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
                            {items.map(item => (
                                <div key={item._id} className="relative border p-4 rounded-lg shadow-lg bg-white">
                                    <div className="absolute top-0 right-0 flex gap-1 m-1">
                                        <IconButton size="sm" color="primary" onClick={() => editItem(item)}>
                                            <BuildIcon />
                                        </IconButton>
                                        <IconButton size="sm" color="danger" onClick={() => promptDeleteItem(item)}>
                                            <DeleteIcon />
                                        </IconButton>
                                    </div>
                                    <p className="text-lg font-bold">
                                        ID: <span className="font-normal">{item._id.length > 4 ? `…${item._id.slice(-4)}` : item._id}</span>
                                    </p>
                                    <p className="text-lg font-bold">
                                        TOPIC: <span className="font-normal">{item.topic}</span>
                                    </p>
                                    <p className="text-lg font-bold">
                                        ANSWER: <span className="font-normal">{item.a}</span>
                                    </p>
                                </div>
                            ))}
                        </div>
                    )}
                </Box>
            )}
            <Modal open={openEditModal} onClose={() => setOpenEditModal(false)}>
                <ModalDialog sx={{ width: '70vw', maxWidth: '70vw', height: '70vh' }}>
                    <ModalClose />
                    <h2>Edit Item</h2>
                    <FormControl>
                        <FormLabel>Topic</FormLabel>
                        <Textarea
                            sx={{ height: '15vh', width: '100%' }}
                            value={editedTopic}
                            onChange={e => setEditedTopic(e.target.value)}
                        />
                    </FormControl>
                    <FormControl>
                        <FormLabel>Answer</FormLabel>
                        <Textarea
                            sx={{ height: '30vh', width: '100%' }}
                            value={editedAnswer}
                            onChange={e => setEditedAnswer(e.target.value)}
                            minRows={3}
                        />
                    </FormControl>
                    <Button onClick={handleSave} variant="solid" color="neutral" sx={{ mt: 2 }}>
                        Save
                    </Button>
                </ModalDialog>
            </Modal>
            <Modal open={openNewItemModal} onClose={() => setOpenNewItemModal(false)}>
                <ModalDialog sx={{ width: '70vw', maxWidth: '70vw', height: '70vh' }}>
                    <ModalClose />
                    <h2>Add New Item</h2>
                    <FormControl>
                        <FormLabel>Topic</FormLabel>
                        <Textarea
                            sx={{ height: '15vh', width: '100%' }}
                            value={newTopic}
                            onChange={e => setNewTopic(e.target.value)}
                        />
                    </FormControl>
                    <FormControl>
                        <FormLabel>Answer</FormLabel>
                        <Textarea
                            sx={{ height: '30vh', width: '100%' }}
                            value={newAnswer}
                            onChange={e => setNewAnswer(e.target.value)}
                            minRows={3}
                        />
                    </FormControl>
                    <Button onClick={handleSaveNewItem} variant="solid" color="neutral" sx={{ mt: 2 }}>
                        Save
                    </Button>
                </ModalDialog>
            </Modal>
            <Modal open={openDeleteModal} onClose={() => setOpenDeleteModal(false)}>
                <ModalDialog>
                    <ModalClose />
                    <h2>Confirm Delete</h2>
                    <p>Are you sure you want to delete this item?</p>
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                        <Button variant="plain" color="neutral" onClick={() => setOpenDeleteModal(false)} sx={{ mr: 1 }}>
                            Cancel
                        </Button>
                        <Button onClick={confirmDelete} variant="solid" color="danger">
                            Delete
                        </Button>
                    </Box>
                </ModalDialog>
            </Modal>
            <Modal open={loading}>
                <ModalDialog sx={{ width: '70vw', height: '70vh', maxWidth: '70vw' }}>
                    <Box
                        sx={{
                            height: '100%',
                            width: '100%',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            flexDirection: 'column',
                        }}
                    >
                        <CircularProgress size="lg" />
                        <Typography>Loading, please wait...</Typography>
                    </Box>
                </ModalDialog>
            </Modal>
        </>
    );
};
