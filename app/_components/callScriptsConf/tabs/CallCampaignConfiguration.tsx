import React, {useEffect, useRef, useState} from "react";
import {useQuery} from "@tanstack/react-query";
import {CallCampaignScript} from "@/app/_components/queries/CallCampaignScript";
import {Button, Modal, ModalDialog, Typography} from "@mui/joy";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";

const preprocessBooleansToString = (obj: any, path = '', store: Set<string>): any => {
    if (typeof obj !== 'object' || obj === null) return obj;
    const clone: any = Array.isArray(obj) ? [] : {};
    for (const key in obj) {
        const val = obj[key];
        const currentPath = path ? `${path}.${key}` : key;
        if (typeof val === 'boolean') {
            clone[key] = val.toString();
            store.add(currentPath);
        } else if (typeof val === 'object') {
            clone[key] = preprocessBooleansToString(val, currentPath, store);
        } else {
            clone[key] = val;
        }
    }
    return clone;
};

const CollapsibleJSONEditor: React.FC<{
    label: string,
    value: any,
    onChange: (newValue: any) => void,
    booleanPaths: React.MutableRefObject<Set<string>>
}> = ({label, value, onChange, booleanPaths}) => {
    const [expanded, setExpanded] = useState(false);
    return (
        <div style={{marginBottom: "1rem", paddingLeft: "1rem"}}>
            <div onClick={() => setExpanded(!expanded)}
                 style={{display: "flex", alignItems: "center", justifyContent: "space-between", cursor: "pointer"}}>
                <label style={{fontWeight: "bold", marginBottom: 4}}>{label}</label>
                <span style={{marginLeft: "1rem"}}>
                    {expanded ? "▾" : "▸"}
                </span>
            </div>
            {expanded && (
                <textarea
                    style={{width: "100%", height: "100px"}}
                    value={JSON.stringify(value, null, 2)}
                    onChange={(e) => {
                        try {
                            const newValue = JSON.parse(e.target.value);
                            const processed = preprocessBooleansToString(newValue, label, booleanPaths.current);
                            onChange(processed);
                        } catch (err) {
                        }
                    }}
                />
            )}
        </div>
    );
};

export const CallCampaignConfiguration: React.FC<{ callCampaignId: string }> = ({callCampaignId}) => {
    const [configData, setConfigData] = useState<any>(null);
    const [initialConfig, setInitialConfig] = useState<any>(null);
    const [openConfirm, setOpenConfirm] = useState(false);
    const [transferExpanded, setTransferExpanded] = useState(false);
    const [voiceExpanded, setVoiceExpanded] = useState(false);
    const [expandedSubKeys, setExpandedSubKeys] = useState<{ [key: string]: boolean }>({});
    const booleanPaths = useRef<Set<string>>(new Set());

    const {data, isFetching} = useQuery(CallCampaignScript(callCampaignId));
    useEffect(() => {
        if (data && typeof data === "object" && Object.keys(data).length > 0) {
            setConfigData(data);
            if (!initialConfig) {
                setInitialConfig(data);
            }
        }
    }, [data]);

    const hasChanges = initialConfig ? JSON.stringify(configData) !== JSON.stringify(initialConfig) : false;

    const updateConfiguration = () => {
        setOpenConfirm(true);
    };

    const confirmUpdate = () => {
        setOpenConfirm(false);
        const updatedConfig = {...initialConfig};
        Object.keys(configData).forEach(key => {
            if (JSON.stringify(configData[key]) !== JSON.stringify(initialConfig[key])) {
                updatedConfig[key] = configData[key];
            }
        });
        booleanPaths.current.forEach((path) => {
            const keys = path.split(".");
            const lastKey = keys.pop()!;
            let current = updatedConfig;
            for (const k of keys) {
                if (!(k in current)) return;
                current = current[k];
            }
            if (typeof current?.[lastKey] === 'string') {
                const val = current[lastKey].toLowerCase();
                if (val === 'true' || val === 'false') {
                    current[lastKey] = val === 'true';
                }
            }
        });

        const payload = {
            collection: 'campaigns',
            filter: {_id: updatedConfig._id},
            update: {$set: updatedConfig},
            options: {upsert: true}
        }
        fetch("/api/mongo/call-center/update-data", {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(payload),
        })
            .then((res) => {
                if (!res.ok) {
                    return res.text().then((err) => {
                        console.error("Error updating configuration:", err);
                        showError("Error updating configuration: " + err);
                    });
                }
                showSuccess("Configuration updated successfully.");
                setInitialConfig(updatedConfig);
                setConfigData(updatedConfig);
            })
            .catch((err) => {
                console.error("Error during fetch:", err);
                showError("Error updating configuration");
            });
    };

    if (isFetching) return <div>Loading...</div>;
    if (!configData || Object.keys(configData).length === 0) return <div>No configuration found.</div>;

    const hasBoolean = (obj: any): boolean => {
        if (typeof obj !== 'object' || obj === null) return false;
        for (const val of Object.values(obj)) {
            if (typeof val === 'boolean') return true;
            if (typeof val === 'object' && hasBoolean(val)) return true;
        }
        return false;
    };

    const renderExpandableObject = (
        key: string,
        value: any,
        parentKey: string,
        expandedKeys: { [key: string]: boolean },
        setExpandedKeys: React.Dispatch<React.SetStateAction<{ [key: string]: boolean }>>,
        renderValue: (k: string, v: any, p: string) => React.ReactNode
    ) => {
        const stateKey = `${parentKey}.${key}`;
        const expanded = expandedKeys[stateKey] ?? false;

        return (
            <div key={stateKey} style={{paddingLeft: "1rem", marginBottom: "1rem"}}>
                <div
                    onClick={() =>
                        setExpandedKeys(prev => ({
                            ...prev,
                            [stateKey]: !prev[stateKey],
                        }))
                    }
                    style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        cursor: "pointer",
                    }}
                >
                    <label style={{fontWeight: key === "bargeIn" ? "bold" : "normal", marginBottom: 4}}>{key}</label>
                    <span>{expanded ? "▾" : "▸"}</span>
                </div>
                {expanded && (
                    <div style={{paddingLeft: "1rem"}}>
                        {Object.entries(value).map(([subK, subV]) =>
                            renderValue(subK, subV, `${parentKey}.${key}`)
                        )}
                    </div>
                )}
            </div>
        );
    };

    const renderField = (key: string, value: any) => {
        if (/id/i.test(key) || key === "version" || key === "variables" || key === "dispositions" || key === "organization" || key === "schemaVersion" || key === "application_sid" || key === "name" || key === "test" || key === "vectra" || key === "nobelBiz") return null;        if (value === null || value === undefined) return null;

        const handleUpdate = (field: string, val: any) => {
            setConfigData((prev: any) => ({...prev, [field]: val}));
        };

        const renderValue = (subKey: string, subVal: any, parentKey: string) => {
            const fullKey = `${parentKey}.${subKey}`;

            if (Array.isArray(subVal)) {
                return (
                    <div key={fullKey} style={{paddingLeft: "1rem", marginBottom: "1rem"}}>
                        <label style={{fontWeight: "normal", display: "block", marginBottom: 4}}>{subKey}</label>
                        {subVal.map((item, idx) => {
                            if (typeof item === "object" && item !== null) {
                                return (
                                    <div key={`${fullKey}[${idx}]`} style={{
                                        paddingLeft: "1rem",
                                        marginBottom: "0.5rem",
                                        borderLeft: "2px solid #ccc"
                                    }}>
                                        {Object.entries(item).map(([k, v]) =>
                                            renderValue(k, v, `${fullKey}[${idx}]`)
                                        )}
                                    </div>
                                );
                            } else {
                                return (
                                    <div key={`${fullKey}[${idx}]`} style={{paddingLeft: "1rem"}}>
                                        <input
                                            type="text"
                                            value={item}
                                            style={{width: "100%", marginBottom: 4}}
                                            onChange={(e) => {
                                                const updated = [...subVal];
                                                updated[idx] = e.target.value;
                                                setConfigData((prev: any) => ({
                                                    ...prev,
                                                    [parentKey]: {
                                                        ...prev[parentKey],
                                                        [subKey]: updated,
                                                    },
                                                }));
                                            }}
                                        />
                                    </div>
                                );
                            }
                        })}
                    </div>
                );
            }

            if (typeof subVal === "boolean") {
                return (
                    <div key={fullKey} style={{marginBottom: "0.5rem", paddingLeft: "1rem"}}>
                        <label style={{fontWeight: "normal", display: "block"}}>{subKey}</label>
                        <select
                            value={subVal.toString()}
                            style={{width: "10%"}}
                            onChange={(e) => {
                                const newValue = e.target.value === "true";
                                setConfigData((prev: any) => {
                                    const keys = parentKey.split(".");
                                    const lastKey = keys.pop()!;
                                    let nested = {...prev};
                                    let current = nested;
                                    for (const k of keys) {
                                        current[k] = {...current[k]};
                                        current = current[k];
                                    }
                                    current[lastKey] = {
                                        ...current[lastKey],
                                        [subKey]: newValue,
                                    };
                                    return nested;
                                });
                            }}
                        >
                            <option value="true">true</option>
                            <option value="false">false</option>
                        </select>
                    </div>
                );
            }

            if (typeof subVal === "object" && subVal !== null && !Array.isArray(subVal)) {
                if (hasBoolean(subVal)) {
                    return renderExpandableObject(
                        subKey,
                        subVal,
                        parentKey,
                        expandedSubKeys,
                        setExpandedSubKeys,
                        renderValue
                    );
                }

                return (
                    <CollapsibleJSONEditor
                        key={fullKey}
                        label={subKey}
                        value={subVal}
                        onChange={(newValue) => {
                            setConfigData((prev: any) => ({
                                ...prev,
                                [parentKey]: {
                                    ...prev[parentKey],
                                    [subKey]: newValue,
                                },
                            }));
                        }}
                        booleanPaths={booleanPaths}
                    />
                );
            }

            const inputType = typeof subVal === "number" ? "number" : "text";
            return (
                <div key={fullKey} style={{marginBottom: "0.5rem", paddingLeft: "1rem"}}>
                    <label style={{fontWeight: "normal", display: "block"}}>{subKey}</label>
                    <input
                        type={inputType}
                        value={subVal}
                        style={{width: "100%"}}
                        onChange={(e) => {
                            const newValue = inputType === "number" ? Number(e.target.value) : e.target.value;
                            setConfigData((prev: any) => ({
                                ...prev,
                                [parentKey]: {
                                    ...prev[parentKey],
                                    [subKey]: newValue,
                                },
                            }));
                        }}
                    />
                </div>
            );
        };

        if (typeof value === "object" && value !== null && !Array.isArray(value)) {
            if (key === "transfer") {
                return (
                    <div key={key} style={{marginBottom: "1rem", paddingLeft: "1rem"}}>
                        <div
                            onClick={() => setTransferExpanded(!transferExpanded)}
                            style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                                cursor: "pointer"
                            }}
                        >
                            <label style={{fontWeight: "bold", marginBottom: 4}}>{key}</label>
                            <span style={{marginLeft: "1rem"}}>{transferExpanded ? "▾" : "▸"}</span>
                        </div>
                        {transferExpanded && (
                            <div style={{marginTop: "0.5rem"}}>
                                {Object.entries(value).map(([subKey, subVal]) => {
                                    if (subKey === "application_sid") {
                                        return null;
                                    }
                                    if (subKey === "to" || subKey === "to2") {
                                        return (
                                            <CollapsibleJSONEditor
                                                key={`${key}.${subKey}`}
                                                label={subKey}
                                                value={subVal}
                                                onChange={(newValue) =>
                                                    setConfigData((prev: any) => ({
                                                        ...prev,
                                                        [key]: {
                                                            ...prev[key],
                                                            [subKey]: newValue,
                                                        },
                                                    }))
                                                }
                                                booleanPaths={booleanPaths}
                                            />
                                        );
                                    }

                                    const inputType =
                                        typeof subVal === "number" ? "number" : typeof subVal === "boolean" ? "checkbox" : "text";
                                    return (
                                        <div key={`${key}.${subKey}`}
                                             style={{marginBottom: "0.5rem", paddingLeft: "1rem"}}>
                                            <label style={{fontWeight: "normal", display: "block"}}>{subKey}</label>
                                            {subVal === true || subVal === false ? (
                                                <select
                                                    value={subVal.toString()}
                                                    style={{width: "10%"}}
                                                    onChange={(e) => {
                                                        const newValue = e.target.value === "true";
                                                        setConfigData((prev: any) => ({
                                                            ...prev,
                                                            [key]: {
                                                                ...prev[key],
                                                                [subKey]: newValue,
                                                            },
                                                        }));
                                                    }}
                                                >
                                                    <option value="true">true</option>
                                                    <option value="false">false</option>
                                                </select>
                                            ) : (
                                                <input
                                                    type={inputType}
                                                    value={subVal as string | number}
                                                    style={{width: "100%"}}
                                                    onChange={(e) => {
                                                        const newValue = inputType === "number" ? Number(e.target.value) : e.target.value;
                                                        setConfigData((prev: any) => ({
                                                            ...prev,
                                                            [key]: {
                                                                ...prev[key],
                                                                [subKey]: newValue,
                                                            },
                                                        }));
                                                    }}
                                                />
                                            )}
                                        </div>
                                    );
                                })}
                            </div>
                        )}
                    </div>
                );
            }
            if (key === "voiceListen") {
                return (
                    <div key={key} style={{marginBottom: "1rem", paddingLeft: "1rem"}}>
                        <div
                            onClick={() => setVoiceExpanded(!voiceExpanded)}
                            style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                                cursor: "pointer"
                            }}
                        >
                            <label style={{fontWeight: "bold", marginBottom: 4}}>{key}</label>
                            <span style={{marginLeft: "1rem"}}>{voiceExpanded ? "▾" : "▸"}</span>
                        </div>
                        {voiceExpanded && (
                            <div style={{marginTop: "0.5rem"}}>
                                {Object.entries(value).map(([subKey, subVal]) => {
                                    const inputType =
                                        typeof subVal === "number" ? "number" :
                                            typeof subVal === "boolean" ? "checkbox" : "text";

                                    if (typeof subVal === "object" && subVal !== null && !Array.isArray(subVal)) {
                                        const subValHasBoolean = hasBoolean(subVal);
                                        if (subValHasBoolean) {
                                            const subKeyStateKey = `${key}.${subKey}`;
                                            const subKeyExpanded = expandedSubKeys[subKeyStateKey] ?? false;
                                            return (
                                                <div key={subKeyStateKey}
                                                     style={{marginBottom: "0.5rem", paddingLeft: "1rem"}}>
                                                    <div
                                                        onClick={() =>
                                                            setExpandedSubKeys((prev) => ({
                                                                ...prev,
                                                                [subKeyStateKey]: !prev[subKeyStateKey],
                                                            }))
                                                        }
                                                        style={{
                                                            display: "flex",
                                                            alignItems: "center",
                                                            justifyContent: "space-between",
                                                            cursor: "pointer",
                                                        }}
                                                    >
                                                        <label style={{
                                                            fontWeight: "bold",
                                                            marginBottom: 4
                                                        }}>{subKey}</label>
                                                        <span
                                                            style={{marginLeft: "1rem"}}>{subKeyExpanded ? "▾" : "▸"}</span>
                                                    </div>
                                                    {subKeyExpanded && (
                                                        <div style={{paddingLeft: "1rem"}}>
                                                            {Object.entries(subVal).map(([k, v]) =>
                                                                renderValue(k, v, `${key}.${subKey}`)
                                                            )}
                                                        </div>
                                                    )}
                                                </div>
                                            );
                                        }
                                        return (
                                            <CollapsibleJSONEditor
                                                key={`${key}.${subKey}`}
                                                label={subKey}
                                                value={subVal}
                                                onChange={(newValue) =>
                                                    setConfigData((prev: any) => ({
                                                        ...prev,
                                                        [key]: {
                                                            ...prev[key],
                                                            [subKey]: newValue,
                                                        },
                                                    }))
                                                }
                                                booleanPaths={booleanPaths}
                                            />
                                        );
                                    }
                                    return (
                                        <div key={`${key}.${subKey}`}
                                             style={{marginBottom: "0.5rem", paddingLeft: "1rem"}}>
                                            <label style={{fontWeight: "normal", display: "block"}}>{subKey}</label>
                                            {subVal === true || subVal === false ? (
                                                <select
                                                    value={subVal.toString()}
                                                    style={{width: "10%"}}
                                                    onChange={(e) => {
                                                        const newValue = e.target.value === "true";
                                                        setConfigData((prev: any) => ({
                                                            ...prev,
                                                            [key]: {
                                                                ...prev[key],
                                                                [subKey]: newValue,
                                                            },
                                                        }));
                                                    }}
                                                >
                                                    <option value="true">true</option>
                                                    <option value="false">false</option>
                                                </select>
                                            ) : (
                                                <input
                                                    type={inputType}
                                                    value={subVal as string | number}
                                                    style={{width: "100%"}}
                                                    onChange={(e) => {
                                                        const newValue = inputType === "number" ? Number(e.target.value) : e.target.value;
                                                        setConfigData((prev: any) => ({
                                                            ...prev,
                                                            [key]: {
                                                                ...prev[key],
                                                                [subKey]: newValue,
                                                            },
                                                        }));
                                                    }}
                                                />
                                            )}
                                        </div>
                                    );
                                })}
                            </div>
                        )}
                    </div>
                );
            }
            if (hasBoolean(value)) {
                return (
                    <div key={key} style={{marginBottom: "1rem", paddingLeft: "1rem"}}>
                        <label style={{fontWeight: "bold", marginBottom: 4}}>{key}</label>
                        <div style={{marginTop: "0.5rem"}}>
                            {Object.entries(value).map(([subKey, subVal]) =>
                                renderValue(subKey, subVal, key)
                            )}
                        </div>
                    </div>
                );
            }

            return (
                <CollapsibleJSONEditor
                    key={key}
                    label={key}
                    value={value}
                    onChange={(newValue) => setConfigData((prev: any) => ({...prev, [key]: newValue}))}
                    booleanPaths={booleanPaths}
                />
            );
        }

        const inputType = typeof value === "number" ? "number" : "text";
        return (
            <div key={key} style={{marginBottom: "1rem"}}>
                <label style={{fontWeight: "bold", display: "block", marginBottom: 4}}>{key}</label>
                <input
                    type={inputType}
                    value={value}
                    style={{width: "100%"}}
                    onChange={(e) => {
                        const newValue = inputType === "number" ? Number(e.target.value) : e.target.value;
                        handleUpdate(key, newValue);
                    }}
                />
            </div>
        );
    };

    return (
        <div style={{padding: "1rem"}}>
            {Object.entries(configData).map(([key, value]) => renderField(key, value))}
            <div style={{marginTop: "1rem"}}>
                <Button
                    variant="solid"
                    color="neutral"
                    sx={{m: "0 1rem"}}
                    onClick={updateConfiguration}
                    disabled={!hasChanges}
                >
                    Update Campaign Configuration
                </Button>
            </div>
            <Modal open={openConfirm} onClose={() => setOpenConfirm(false)}>
                <ModalDialog>
                    <Typography component="h2">Confirm Update</Typography>
                    <Typography>Are you sure you want to update the configuration?</Typography>
                    <div style={{display: "flex", justifyContent: "flex-end", marginTop: "1rem"}}>
                        <Button onClick={() => setOpenConfirm(false)} variant="plain" color="neutral"
                                sx={{marginRight: "1rem"}}>
                            Cancel
                        </Button>
                        <Button onClick={confirmUpdate} variant="solid" color="neutral">
                            Confirm
                        </Button>
                    </div>
                </ModalDialog>
            </Modal>
        </div>
    );
};
