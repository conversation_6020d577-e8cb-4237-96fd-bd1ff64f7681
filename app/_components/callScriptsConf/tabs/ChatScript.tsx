"use client";
import {<PERSON>, <PERSON><PERSON>, Grid, <PERSON><PERSON>, Modal<PERSON>lose, Modal<PERSON>ialog, Typography} from "@mui/joy";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/joy";
import React, {useEffect, useRef, useState} from "react";
import {tryit} from "radash";
import moment from "moment-timezone";
import {useQuery, useQueryClient} from "@tanstack/react-query";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";
import _ from "lodash";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {DataPromptDisplay} from "../../chatScript/DataPromptDisplay";
import {DataKBDisplay} from "../../chatScript/DataKBDisplay";
import {MessagesBoxDisplay} from "../../chatScript/MessagesBoxDisplay";
import {DataQuestionsDisplay} from "../../chatScript/DataQuestionsDisplay";
import {useEntity} from "simpler-state";
import {
    dataExtractedChatScript,
    dataQuestChatScript,
    enableExtractionChatScript,
    messagesChatScript,
    promptChatScript,
    sessionDataChatScript,
    setChatScriptStarted, setDataExtractedChatScript,
    setDataQuestChatScript,
    setEnableExtractionChatScript,
    setMessagesChatScripts,
    setPromptChatScript,
    setSessionDataChatScript,
    setViewExtractedChatScript,
    viewExtractedChatScript,
} from "@/app/_components/table/states/nodeSriptsStates";
import {CallLeadsProvider} from "@/app/(private)/callLeads/_components/CallLeadsProvider";
import {ViewDataMapped} from "@/app/_components/callScriptsConf/triggers/ViewDataMapped";
import {MenuChatProvider} from "@/app/_components/chatScript/MenuChatProvider";
import {DataObjectiveDisplay} from "@/app/_components/chatScript/DataObjectiveDisplay";
import {DataEventsDisplay} from "@/app/_components/chatScript/DataEventsDisplay";

function generateHexId(): string {
    const chars = "abcdef0123456789";
    let result = "";
    for (let i = 0; i < 24; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

function generateRandomPhoneNumber(): string {
    let number = "";
    for (let i = 0; i < 10; i++) {
        number += Math.floor(Math.random() * 10);
    }
    return `+1${number}`;
}

export const ChatScriptProvider: React.FC<{
    chatData: any;
    callCampaignId?: string;
    userId?: string;
    confCallLeads: any;
    principalAudioRef: any;
    vectraConfig: any;
}> = ({chatData, callCampaignId, userId, confCallLeads, principalAudioRef, vectraConfig}) => {
    const sessionData: any = useEntity(sessionDataChatScript);
    const chatMessages = useEntity(messagesChatScript);
    const actualPrompt = useEntity(promptChatScript);
    const viewExtracted = useEntity(viewExtractedChatScript);
    const enableExtraction = useEntity(enableExtractionChatScript);
    const dataQuest: any = useEntity(dataQuestChatScript);
    const dataExtracted: any = useEntity(dataExtractedChatScript);
    const systemOptions = React.useMemo(
        () => sessionData?.scripts?.filter((s: any) => s.base === true) || [],
        [sessionData]
    );
    const [isDev, setIsDev] = useState(chatData?.dev);
    const [isLoading, setIsLoading] = useState(false);
    const [currentStreamingMessage, setCurrentStreamingMessage] = useState("");
    const [spokenMessages, setSpokenMessages] = useState<Set<string>>(new Set());
    const [localChatData, setLocalChatData] = useState(chatData?.metadata);
    const [isCallEnd, setIsCallEnd] = useState(false);
    const [openModal, setOpenModal] = useState(false);
    const [typeModal, setTypeModal] = useState("");
    const audioRef = useRef<HTMLAudioElement | null>(null);
    const queryClient = useQueryClient();

    const replaceCompanyName = (text: string) => {
        return text.replace(/\$\{company_name\}/g, "Company ABC");
    };

    useEffect(() => {
        if (!localChatData?.chat) {
            setLocalChatData((prev: any) => ({
                ...prev,
                chat: {
                    _id: generateHexId(),
                    first_name: "John",
                    last_name: "Doe",
                    zip: "89138",
                    ipaddress: "*******",
                    phone: generateRandomPhoneNumber(),
                    gender: "m",
                    dob: "01/01/1990",
                    email: "<EMAIL>",
                    street_address: "15 Berneri Drive",
                    city: "Las Vegas",
                    state: "NV",
                    transfer: "+17027034123",
                },
            }));
        }
    }, [localChatData]);

    useEffect(() => {
        if (
            dataQuest &&
            Object.keys(dataQuest).length > 0 &&
            (dataQuest.newIntent === "endingCall" ||
                dataQuest.newIntent === "dncEndCall" ||
                dataQuest.newIntent === "transferringCall")
        ) {
            openModalContent("final")
            setEnableExtractionChatScript(true);
            setIsCallEnd(true);
            queryClient.invalidateQueries({queryKey: ["script-data-extraction"]});
            if (sessionData?.sessionId) {
                fetch("/api/vectra/endChatSession", {
                    method: "POST",
                    headers: {"Content-Type": "application/json"},
                    body: JSON.stringify({sessionId: sessionData.sessionId}),
                }).catch(err => console.warn("Failed to end chat session", err));
            }
        }
    }, [dataQuest]);

    const getSession = async () => {
        await restartConversation(false)
        if (!callCampaignId || !localChatData?.chat?.phone) {
            console.warn("Missing callCampaignId or localChatData.chat.phone. Skipping initialSetup request.");
            return null;
        }
        const [sessionError, response] = await tryit(async () =>
            fetch("/api/vectra/initialSetup", {
                method: "POST",
                headers: {"Content-Type": "application/json"},
                body: JSON.stringify({
                    campaignId: callCampaignId,
                    incomingCallerId: localChatData?.chat?.phone,
                }),
            })
        )();
        if (sessionError) {
            console.error(sessionError);
            return null;
        } else {
            const se = await response.json();
            if (se.messages && Array.isArray(se.messages)) {
                se.messages = se.messages.map((msg: any) => {
                    if (msg.content) {
                        msg.content = replaceCompanyName(msg.content);
                    }
                    return msg;
                });
            }
            setSessionDataChatScript(se);
            setMessagesChatScripts(se.messages || []);
            if (se.intent && typeof se.intent === "string") {
                const intentMatch = se.intent.match(/\|([^\[]+)\[/);
                const idMatch = se.intent.match(/\[([^\]]+)\]/);
                const initialIntent = intentMatch ? intentMatch[1] : "";
                const initialScriptId = idMatch ? idMatch[1] : "";

                if (initialScriptId) {
                    setDataQuestChatScript({
                        currentIntent: initialIntent,
                        scriptId: initialScriptId,
                        newIntent: initialIntent,
                        newScriptId: initialScriptId,
                        previousScriptId: initialScriptId,
                        timeTaken: "0 ms"
                    });
                }
            }
            return se;
        }
    };

    const getAudio = async (msg: Array<{ role: string; content: string }>, forceSend: boolean = false) => {
        if (_.isEmpty(msg)) return;
        const messageToSpeak = getLastAssistantMessage(msg);
        if (!messageToSpeak) return;
        if (!forceSend && spokenMessages.has(messageToSpeak)) return;
        try {
            resetCurrentAudio();
            const response = await fetch("/api/deepgram/speech", {
                method: "POST",
                body: JSON.stringify({text: messageToSpeak}),
            });
            if (!response.ok) {
                const {error} = await response.json();
                showError(error ?? "Something went wrong");
                return;
            }
            const audioBlob = await response.blob();
            const audioURL = URL.createObjectURL(audioBlob);
            const newAudio = new Audio(audioURL);
            audioRef.current = newAudio;
            principalAudioRef.current = newAudio;
            if (newAudio) {
                await newAudio.play();
            } else {
                document.body.addEventListener(
                    "click",
                    () => {
                        newAudio.play().catch((err) => console.error("Error on playing audio:", err));
                    },
                    {once: true}
                );
            }
            setSpokenMessages((prev) => new Set(prev).add(messageToSpeak));
            newAudio.onended = () => {
                if (audioRef.current === newAudio) {
                    resetCurrentAudio();
                }
            };
            return null;
        } catch (error) {
            console.error("Error generating audio:", error);
        }
    };

    const resetCurrentAudio = () => {
        if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0;
            audioRef.current = null;
        }
    };

    const handleSubmit = async (inputMessage: string) => {
        if (!inputMessage.trim() || isLoading) return;
        const userMessage = {role: "user", content: inputMessage};
        setMessagesChatScripts((prev) => [...prev, userMessage]);
        setIsLoading(true);
        setCurrentStreamingMessage("");
        setPromptChatScript("");
        setDataQuestChatScript({});
        setChatScriptStarted(true);

        const timerMessage = moment().valueOf();
        const newSession = {...sessionData, timer: timerMessage};


        let campaignIdFromItems = "unknownCallCampaign";
        if (Array.isArray(vectraConfig) && vectraConfig.length > 0 && vectraConfig[0]?.call_campaign_id) {
            campaignIdFromItems = vectraConfig[0].call_campaign_id;
        }
        try {
            const [leadDataError, leadDataResponse] = await tryit(async () =>
                fetch("/api/mongo/call-center/get-data", {
                    method: "POST",
                    headers: {"Content-Type": "application/json"},
                    body: JSON.stringify({
                        collection: "campaigns",
                        filter: {_id: callCampaignId},
                    }),
                })
            )();
            if (leadDataError) {
                console.warn("Error fetching call lead data:", leadDataError);
            } else {
                const result = await leadDataResponse.json();
                if (result?.response?.phone) {
                    newSession.campaignPhone = result.response.phone;
                }
            }
        } catch (err) {
            console.error("Failed to fetch call lead metadata:", err);
        }

        const localIndexConfig = {
            call_campaign_id: campaignIdFromItems,
            version: 1,
            metadataConfig: {},
            items: Array.isArray(vectraConfig)
                ? vectraConfig.map((obj: any) => ({
                    vector: obj.vector || [],
                    norm: obj.norm || 0,
                    metadata: {
                        id: obj._id,
                        topic: obj.topic,
                        a: obj.a
                    }
                }))
                : []
        };
        try {
            const response = await fetch("/api/vectra/aiChat", {
                method: "POST",
                headers: {"Content-Type": "application/json"},
                body: JSON.stringify({
                    message: inputMessage,
                    session: newSession,
                    campaignId: callCampaignId,
                    config: localIndexConfig,
                }),
            });
            const reader = response.body?.getReader();
            const decoder = new TextDecoder();
            let builtMessage = "";
            let buffer = "";
            while (true) {
                const {value, done} = await reader!.read();
                if (value) {
                    buffer += decoder.decode(value, {stream: true});
                    const lines = buffer.split("\n");
                    buffer = lines.pop() || "";
                    for (const line of lines) {
                        if (line.startsWith("data: ")) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                if (data.content) {
                                    setCurrentStreamingMessage((prev) => prev + data.content);
                                    builtMessage += data.content;
                                }
                                if (data.done) {
                                    setSessionDataChatScript(data.session);
                                    setPromptChatScript(data.session?.prompt || "");
                                    setDataQuestChatScript(data.session?.dataQuestions || {});
                                    setCurrentStreamingMessage("");
                                    break;
                                }
                                if (data.error) {
                                    console.error("Error:", data.error);
                                    break;
                                }
                            } catch (e) {
                                console.error("Error parsing SSE data:", e);
                            }
                        }
                    }
                }
                if (done) break;
            }
            if (buffer.trim() !== "" && buffer.startsWith("data: ")) {
                try {
                    const data = JSON.parse(buffer.slice(6));
                    if (data.content) {
                        setCurrentStreamingMessage((prev) => prev + data.content);
                        builtMessage += data.content;
                    }
                } catch (e) {
                    console.error("Error parsing remaining SSE data:", e);
                }
            }
            const newMess = {
                role: "assistant",
                content: replaceCompanyName(builtMessage.split("|")[0]),
            };
            const allMessages = [...chatMessages, newMess];
            getAudio(allMessages, true);
            setMessagesChatScripts((prev) => [...prev, newMess]);
        } catch (error) {
            console.error("Failed to send message:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const getLastAssistantMessage = (messages: Array<{ role: string; content: string }>): string | null => {
        const assistantMessages = messages.filter((msg) => msg.role === "assistant");
        return assistantMessages.length > 0 ? assistantMessages[assistantMessages.length - 1].content : null;
    };

    const handleMetadataUpdated = (updatedMetadata: any) => {
        setLocalChatData(updatedMetadata);
        showSuccess("Data updated successfully");
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    };

    const renderFinalModalContent = () => {
        let message = "";
        switch (dataQuest.newIntent) {
            case "dncEndCall":
                message = "This contact has been marked as Do Not Call.";
                break;
            case "transferringCall":
                message = "Call is being transferred. Our AI supervisor is reviewing the call, please wait for his summary.";
                break;
            case "endingCall":
                message = "Client was disqualified.";
                break;
            default:
                message = "Something went wrong. Please try again later.";
                break;
        }
        return (
            <Box>
                <Typography level="title-lg" sx={{mb: '10px'}}>
                    Call Ended
                </Typography>
                <Typography level="body-md">{message}</Typography>
                <Box sx={{display: "flex", justifyContent: "space-around", marginTop: 2}}>
                    <Button
                        color="neutral"
                        onClick={() => {
                            closeModal()
                        }}>
                        OK
                    </Button>
                </Box>
            </Box>
        );
    };

    const openModalContent = (newType: string) => {
        setTypeModal(newType)
        setOpenModal(true)
    }

    const closeModal = () => {
        setOpenModal(false)
        setTypeModal("")
    }

    const {isFetching} = useQuery({
        queryKey: ["session-data", callCampaignId, localChatData?.chat?.phone],
        queryFn: getSession,
        refetchOnWindowFocus: false,
        enabled: !!callCampaignId && !!localChatData?.chat?.phone,
    });

    const restartConversation = async (resetSession = false) => {
        setOpenModal(false)
        setChatScriptStarted(false)
        setEnableExtractionChatScript(false)
        setViewExtractedChatScript(false)
        setIsCallEnd(false)
        setDataExtractedChatScript({})
        setMessagesChatScripts([]);
        setSessionDataChatScript({});
        setPromptChatScript("");
        setDataQuestChatScript({});
        setSpokenMessages(new Set());
        resetCurrentAudio()
        if (resetSession) await getSession();
    };

    const modalContent = () => {
        switch (typeModal) {
            case "client":
                return <CallLeadsProvider
                    chatData={localChatData}
                    userId={userId}
                    onMetadataUpdated={handleMetadataUpdated}
                    confCallLeads={confCallLeads}/>
            case "final":
                return renderFinalModalContent()
            default:
                return <></>
        }
    }

    return (<>
        <div>
            {isFetching ? (
                <LoadingMessage message="Loading data..."/>
            ) : (<>
                <MenuChatProvider
                    restartConversation={restartConversation}
                    sessionData={sessionData}
                    dataQuest={dataQuest}
                    callCampaignId={callCampaignId}
                    enableExtraction={enableExtraction}
                    openModalContent={openModalContent}
                />
                <Box sx={{padding: "1rem", overflow: "scroll", height: "80vh", backgroundColor: "#f5f5f5"}}>
                    <Grid container spacing={2}>
                        <Grid xs={12} md={8} lg={8}>
                            <MessagesBoxDisplay
                                messages={chatMessages}
                                currentStreamingMessage={currentStreamingMessage}
                                isLoading={isLoading}
                                handleSubmit={handleSubmit}
                                isCallEnd={isCallEnd}
                            />
                        </Grid>
                        <Grid xs={12} md={4} lg={4}>
                          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', bgcolor: 'background.body' }}>
                            <Tabs defaultValue={0} sx={{ flexGrow: 1 }}>
                              <TabList>
                                <Tab>Objective</Tab>
                                <Tab>Events</Tab>
                                <Tab>Knowledge</Tab>
                                {isDev && (
                                  <>
                                    <Tab>Questions</Tab>
                                    <Tab>Prompt</Tab>
                                    <Tab>Mapping</Tab>
                                  </>
                                )}
                              </TabList>
                              <TabPanel value={0} sx={{ flexGrow: 1, overflowY: 'auto' }}>
                                <DataObjectiveDisplay
                                  dataQuest={dataQuest}
                                  sessionData={sessionData}
                                  isLoading={isLoading}
                                  systemOptions={systemOptions}
                                  isCallEnd={isCallEnd}
                                />
                              </TabPanel>
                              <TabPanel value={1} sx={{ flexGrow: 1, overflowY: 'auto' }}>
                                <DataEventsDisplay sessionId={sessionData?.sessionId} />
                              </TabPanel>
                              <TabPanel value={2} sx={{ flexGrow: 1, overflowY: 'auto' }}>
                                <DataKBDisplay actualPrompt={actualPrompt} isLoading={isLoading}/>
                              </TabPanel>
                              {isDev && (
                                <>
                                  <TabPanel value={3} sx={{ flexGrow: 1, overflowY: 'auto' }}>
                                    <DataQuestionsDisplay dataQuestions={dataQuest} isLoading={isLoading}/>
                                  </TabPanel>
                                  <TabPanel value={4} sx={{ flexGrow: 1, overflowY: 'auto' }}>
                                    <DataPromptDisplay actualPrompt={actualPrompt} isLoading={isLoading}/>
                                  </TabPanel>
                                  <TabPanel value={5} sx={{ flexGrow: 1, overflowY: 'auto' }}>
                                    {enableExtraction && _.isEmpty(dataExtracted) ? (
                                      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                                        <Button loading variant="plain" />
                                      </Box>
                                    ) : (
                                      <ViewDataMapped dataExtracted={dataExtracted}/>
                                    )}
                                  </TabPanel>
                                </>
                              )}
                            </Tabs>
                          </Box>
                        </Grid>
                    </Grid>
                </Box>
            </>)}
        </div>
        <Modal open={openModal} onClose={() => closeModal()}>
            <ModalDialog sx={{overflowY: 'auto'}}>
                <ModalClose/>
                {modalContent()}
            </ModalDialog>
        </Modal>
    </>);
};
