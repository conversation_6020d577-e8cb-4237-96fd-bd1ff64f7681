'use client'
import React from "react";
import Box from "@mui/joy/Box";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";
import {useQuery} from "@tanstack/react-query";
import {DataCallCampaign} from "@/app/_components/queries/DataCallCampaign";
import {TableSection} from "@/app/_components/callScriptsConf/variables/TableSection";
import {Typography} from "@mui/joy";

export const VarConfComponent: React.FC<any> = ({callCampaignId}) => {
    const {data, isFetching} = useQuery(DataCallCampaign(callCampaignId));

    return (
        <Box
            sx={{
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#fafafa',
                border: '1px solid #e4e4e4',
                borderRadius: '10px',
                textAlign: 'center',
                padding: 2,
            }}>
            <Typography level="body-sm" sx={{mb: "20px"}}>
                You can compound variables using the following syntax in the <strong>details</strong> field:
                <br/>
                <span style={{fontWeight: 'bold'}}>
                    Example to get <code>${'{'}variable_name{'}'}</code> value
                </span>
            </Typography>
            {isFetching ? <LoadingMessage message="Loading..."/> : <>
                <TableSection
                    group="company"
                    data={data?.company}
                    allVariables={data?.all}
                    campaignId={callCampaignId}
                />
                <TableSection
                    group="client"
                    data={data?.client}
                    allVariables={data?.all}
                    campaignId={callCampaignId}
                />
                <TableSection
                    group="compound"
                    data={data?.compound}
                    allVariables={data?.all}
                    campaignId={callCampaignId}
                />
                <TableSection
                    group="script"
                    data={data?.script}
                    allVariables={data?.all}
                    campaignId={callCampaignId}
                />
            </>}
        </Box>
    );
};
