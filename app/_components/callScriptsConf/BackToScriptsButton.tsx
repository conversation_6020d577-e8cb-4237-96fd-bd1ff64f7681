import React from "react";
import {Tooltip} from '@mui/joy';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import {useRouter} from 'next/navigation';
import IconButton from "@mui/joy/IconButton";
import {setLoading} from "@/app/_components/Loading/loadingState";
import {
    setChatScriptStarted, setDataExtractedChatScript, setDataQuestChatScript,
    setEnableExtractionChatScript, setMessagesChatScripts, setPromptChatScript, setSessionDataChatScript,
    setViewExtractedChatScript
} from "@/app/_components/table/states/nodeSriptsStates";

const BackToScriptsButton = () => {
    const router = useRouter();

    const handleGoBack = async () => {
        setLoading(true)
        setChatScriptStarted(false)
        setEnableExtractionChatScript(false)
        setViewExtractedChatScript(false)
        setDataExtractedChatScript({})
        setMessagesChatScripts([]);
        setSessionDataChatScript({});
        setPromptChatScript("");
        setDataQuestChatScript({});
        router.push('/call-center/script');
    };

    return (
        <Tooltip title="Go back" variant="soft" placement="left">
            <IconButton
                onClick={handleGoBack}
                variant="outlined"
                color="neutral"
                size="sm"
                sx={{cursor: 'pointer', ml: 5,}}>
                <ArrowBackIcon/>
            </IconButton>
        </Tooltip>
    );
};

export default BackToScriptsButton;
