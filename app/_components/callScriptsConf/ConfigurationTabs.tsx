'use client';
import React, {useState} from "react";
import {useQuery} from "@tanstack/react-query";
import {useEntity} from "simpler-state";
import {dataChangeNodeScriptsCount} from "@/app/_components/table/states/nodeSriptsStates";
import {TabsViewCallScriptsComponent} from "@/app/_components/callScriptsConf/TabsViewCallScriptsComponent";
import _ from "lodash";
import {Typography} from "@mui/joy";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";
import {NodeScriptsTabs} from "@/app/_components/queries/NodeScriptsTabs";

export const ConfigurationTabs: React.FC<any> = ({
                                                     callCampaignId,
                                                     userMetadata,
                                                     confCallLeads,
                                                     tabNum,
                                                     userId,
                                                 }) => {
    // TODO: check all methods go to call-center DB
    const tableName = 'scripts';
    const [paginateConfig] = useState({page: 1, size: 10});
    const dataNodeScriptsCount = useEntity(dataChangeNodeScriptsCount);

    const {
        data,
        isFetching
    } = useQuery(NodeScriptsTabs(tableName, paginateConfig, callCampaignId, dataNodeScriptsCount, tabNum));

    return (<>
        {(isFetching) ?
            <LoadingMessage message="Loading data..."/> :
            !_.isEmpty(data) ?
                <TabsViewCallScriptsComponent
                    tableName={tableName}
                    userMetadata={userMetadata}
                    greetingsData={data?.greetingsData}
                    dispositionData={data?.dispositionData}
                    tasksData={data?.tasksData}
                    terminateData={data?.terminateData}
                    campaignData={data?.campaignData}
                    callCampaignId={callCampaignId}
                    userId={userId}
                    confCallLeads={confCallLeads}
                    vectraConfig={data?.vectraConfig}
                /> :
                <div className="flex justify-center mt-10">
                    <Typography level="title-lg">
                        No data to show
                    </Typography>
                </div>}
    </>);
};
