import React from 'react';
import {getBezierPath} from 'react-flow-renderer';
import {Chip, Stack, Typography} from "@mui/joy";

const CustomEdge: React.FC<any> = ({
                                       id,
                                       sourceX,
                                       sourceY,
                                       targetX,
                                       targetY,
                                       sourcePosition,
                                       targetPosition,
                                       style = {},
                                       markerEnd,
                                       data,
                                   }) => {
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent) ?? true;
    const edgePath = getBezierPath({
        sourceX,
        sourceY,
        sourcePosition,
        targetX,
        targetY,
        targetPosition,
    });
    const midX = (sourceX + targetX) / 2;
    const midY = (sourceY + targetY) / 2;
    return (
        <>
            <path
                id={id}
                style={{
                    ...style,
                    stroke: '#000',
                    mixBlendMode: isSafari ? 'normal' : 'color',
                }}
                className="react-flow__edge-path"
                d={edgePath}
                markerEnd={markerEnd}
            />
            <foreignObject
                width={1}
                height={1}
                x={midX - 50}
                y={midY - 50}
                style={{overflow: 'visible'}}
            >
                <Stack
                    spacing={1}
                    sx={{
                        borderRadius: '5px',
                        padding: '10px',
                        width: '125px',
                        fontSize: '10px',
                        backgroundColor: '#e0e6ff',
                        display: 'flex',
                        justifyContent: 'center',
                        border: '2px dashed blue',
                        alignItems: 'center',
                        color: 'black',
                        cursor: 'pointer',
                        userSelect: 'none'
                    }}
                >
                    <Typography level="body-xs">
                        {data?.label || 'No defined'}
                    </Typography>
                    <Chip variant="outlined" size="sm">
                        {data?.disposition || 'No defined'}
                    </Chip>
                </Stack>
            </foreignObject>
        </>
    );
};

export default CustomEdge;