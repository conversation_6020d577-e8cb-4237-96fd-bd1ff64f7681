import React, { useEffect, useRef } from "react";
import { useEntity } from "simpler-state";
import { createTableEntities } from "@/app/_components/table/states/tableDataEntity";
import { buildFilters } from "@/app/_lib/nova/functions/utils/buildFilters";
import { showError, showSuccess } from "@/app/_components/alerts/toast/ToastMessages";

export const ResetCampaign: React.FC<any> = ({ callCampaignId }) => {
    const tableName = "reload_node_scripts";
    const {
        paginateConfigEntity,
        filtersEntity,
        setPaginateConfigEntity,
    } = createTableEntities(tableName);

    const paginateConfig = useEntity(paginateConfigEntity);
    const filters = useEntity(filtersEntity);
    const hasRun = useRef(false);

    useEffect(() => {
        if (hasRun.current) return;
        hasRun.current = true;

        setPaginateConfigEntity((prevConfig) => ({
            ...prevConfig,
            size: 25,
        }));

        const fetchAndReplaceItems = async () => {
            try {
                const payload = getPayload();

                const response = await fetch("/api/mongo/getItems", {
                    method: "POST",
                    body: JSON.stringify(payload)
                });

                if (response.ok) {
                    const { response: data } = await response.json();
                    if (Array.isArray(data) && data.length > 0) {
                        await replaceItemsInNodeScripts(data);
                    } else {
                        showError("No items found to insert.");
                    }
                } else {
                    const { error } = await response.json();
                    showError(`Error fetching items: ${error}`);
                }
            } catch (error) {
                console.error("Error fetching items:", error);
                showError("Error fetching items");
            }
        };

        const replaceItemsInNodeScripts = async (items: any[]) => {
            try {
                const deletePayload = {
                    collection: "scripts",
                    filter: { callCampaignId },
                    deleteMany: true,
                };

                const deleteResponse = await fetch("/api/mongo/call-center/delete-data", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify(deletePayload),
                });

                if (!deleteResponse.ok) {
                    const { error } = await deleteResponse.json();
                    showError(`Error deleting items: ${error}`);
                    return;
                }
                const normalizedItems = items.map((item) => ({
                    ...item,
                    match: item.match ?? false,
                }));

                const insertPayload = {
                    collection: "scripts",
                    documents: normalizedItems,
                };

                const insertResponse = await fetch("/api/mongo/insertManyObjects", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify(insertPayload),
                });

                if (insertResponse.ok) {
                    showSuccess("All scripts successfully updated");
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    const { error } = await insertResponse.json();
                    showError(`Error inserting items: ${error}`);
                }
            } catch (error) {
                console.error("Error replacing items:", error);
                showError(`Error replacing items: ${error.message}`);
            }
        };

        fetchAndReplaceItems();
    }, []);

    const getPayload = () => {
        const queryData = {
            _id: 1,
            action:1,
            base: 1,
            callCampaignId: 1,
            controlNode: 1,
            disposition: 1,
            endType: 1,
            examples: 1,
            intent: 1,
            match: 1,
            notes: 1,
            objective: 1,
            origin: 1,
            responses: 1,
            version: 1
        };

        const context = { timezone: "America/Chicago" };
        const mongoFilters = buildFilters(filters, context);

        return {
            filters,
            mongoFilters,
            options: {
                sort: null,
                skip: 0,
                limit: 50,
            },
            collection: tableName,
            query: queryData,
            context,
        };
    };

    return null;
};
