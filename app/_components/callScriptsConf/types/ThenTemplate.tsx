import React from "react";

const ThenTemplate = (props: any) => {
    const { title, properties, uiSchema } = props;
    const childLabelValue = uiSchema?.["ui:options"]?.childLabelValue || "";

    return (
        <div>
            <h3>{title}</h3>
            <div style={{ paddingLeft: "20px" }}>
                {properties.map((prop: any) => {
                    if (prop.name === "childLabel") {
                        return (
                            <div key={prop.name} style={{ marginBottom: "10px" }}>
                                <label> - {childLabelValue}</label>
                            </div>
                        );
                    }
                    return (
                        <div key={prop.name} style={{ marginBottom: "10px" }}>
                            {prop.content}
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default ThenTemplate;