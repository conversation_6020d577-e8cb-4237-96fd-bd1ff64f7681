import React, {useEffect, useState} from "react";
import {Theme as AntDTheme} from "@rjsf/antd";
import {withTheme} from "@rjsf/core";
import {buildUpdNodeForm} from "@/app/_components/callScriptsConf/funcs/buildUpdNodeForm";
import validator from "@rjsf/validator-ajv8";
import {Box} from "@mui/joy";
import {showError, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {LoadingMessage} from "@/app/_components/Loading/LoadingMessage";

const ThemedForm = withTheme(AntDTheme);

export const UpdatedNodeComponent: React.FC<any> = ({
                                                        tableName,
                                                        nodeData,
                                                        scriptsData,
                                                        nodesData,
                                                        idsSearch,
                                                        edgesData,
                                                        type,
                                                        field,
                                                        onSubmit,
                                                        childLabel
                                                    }) => {
    // console.log('idsSearch')
    // console.log(idsSearch)
    const [valuesForm, setValuesForm] = useState(null);
    const [loadingSub, setLoadingSub] = useState(false);

    useEffect(() => {
        const getSchema = buildUpdNodeForm(nodeData, type, field, idsSearch, childLabel);
        setValuesForm(getSchema);
    }, []);

    const onSubmitUpd = async (submitData: any) => {
        try {
            const formData = submitData?.formData;
            setLoadingSub(true);

            if (!nodeData?._id) {
                showError('Node not found');
                finalOnSubmit(null, null, null);
                return;
            }

            const {_id, ...remainData} = nodeData;
            let updData = {};

            if (type === 'node') {
                updData = {
                    ...remainData,
                    [field]: formData[field],
                };
            } else {
                const newResponses = nodeData?.responses?.map((resp: any) => {
                    const checkEqual = idsSearch?.match
                        ? (resp.scriptId === idsSearch.target && resp?.match === idsSearch.match)
                        : (resp.scriptId === idsSearch.target);
                    if (checkEqual) {
                        return {
                            ...resp,
                            match: formData.match,
                            disposition: formData.then?.disposition || "",
                        };
                    }
                    return resp;
                });
                updData = {
                    ...remainData,
                    responses: newResponses,
                };
            }

            const isUpdatedNode = await updateInformation(_id, updData);
            if (!isUpdatedNode) return;

            let newScripts = scriptsData;
            if (type === 'node') {
                newScripts = newScripts.map((scriptInf: any) => {
                    if (scriptInf._id === _id) {
                        return {
                            ...scriptInf,
                            [field]: formData[field],
                        };
                    }
                    return scriptInf;
                });
            } else if (type === 'edge') {
                newScripts = newScripts.map((scriptInf: any) => {
                    if (scriptInf._id === idsSearch.source) {
                        const responses = scriptInf.responses.map((resp: any) => {
                            if (resp.scriptId === idsSearch.target) {
                                return {
                                    ...resp,
                                    match: formData.match,
                                    disposition: formData.then?.disposition || "",
                                };
                            }
                            return resp;
                        });
                        return {
                            ...scriptInf,
                            responses,
                        };
                    }
                    return scriptInf;
                });
            }

            let newNodes = nodesData;
            let newEdges = edgesData;
            const labelChanged = field === 'match' ? 'match' : (field !== 'disposition' ? 'label' : 'disposition');
            if (type === 'node') {
                newNodes = newNodes.map((nodeInf: any) => {
                    if (nodeInf.id === idsSearch.id) {
                        const data = {
                            ...nodeInf.data,
                            [labelChanged]: formData[field],
                        };
                        return {
                            ...nodeInf,
                            data,
                        };
                    }
                    return nodeInf;
                });
            } else if (type === 'edge') {
                newEdges = newEdges.map((edgeInf: any) => {
                    if (
                        edgeInf?.source === idsSearch.source &&
                        (edgeInf?.target === idsSearch?.target || edgeInf?.target === idsSearch?.targetCompound)
                    ) {
                        const data = {
                            ...edgeInf.data,
                            match: formData.match,
                            label: formData.match,
                            disposition: formData.then?.disposition || "",
                        };
                        if (edgeInf.data.idEndNode) {
                            newNodes = newNodes.map((nodeInf: any) => {
                                if (nodeInf.id === edgeInf?.target) {
                                    const data = {
                                        ...nodeInf.data,
                                        match: formData.match,
                                        label: formData.match,
                                        disposition: formData.then?.disposition || "",
                                    };
                                    return {
                                        ...nodeInf,
                                        data,
                                    };
                                }
                                return nodeInf;
                            });
                        }
                        return {
                            ...edgeInf,
                            data,
                        };
                    }
                    return edgeInf;
                });
            }

            showSuccess('Node updated successfully');
            finalOnSubmit(newNodes, newEdges, newScripts);
        } catch (error) {
            console.error('Error updating nodes:', error);
            showError('We had a problem updating nodes');
            finalOnSubmit(nodesData, edgesData, scriptsData);
        }
    };
    const finalOnSubmit = (returnedNodes: any, returnedEdges: any, returnedScripts: any) => {
        setLoadingSub(false);
        onSubmit(returnedNodes, returnedEdges, returnedScripts);
    }

    const updateInformation = async (_id: string, updatedData: any) => {
        const updatePayload = {
            collection: tableName,
            filter: {_id},
            update: {
                $set: updatedData
            },
            options: {
                upsert: true
            }
        };

        const updateResult = await fetch("/api/mongo/call-center/update-data", {
            method: 'PUT',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(updatePayload)
        });

        if (updateResult.status !== 200) {
            const errorResponse = await updateResult.text();
            console.error('Failed to update node:', errorResponse);
            showError('Problem updating node: ' + errorResponse);
            return false;
        } else {
            return true;
        }
    }

    return (
        <Box>
            {loadingSub ?
                <LoadingMessage message="Updating node..."/>
                : valuesForm && (
                <ThemedForm
                    schema={valuesForm?.schema}
                    formData={valuesForm?.formData}
                    validator={validator}
                    onSubmit={onSubmitUpd}
                    uiSchema={valuesForm?.uiSchema}
                />
            )}
        </Box>
    );
};
