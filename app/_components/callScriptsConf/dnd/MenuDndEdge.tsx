import React from "react";
import {ListItemDecorator, Menu, MenuItem} from "@mui/joy";
import Edit from "@mui/icons-material/Edit";

export const MenuDndEdge: React.FC<any> = ({
                                               menuRef,
                                               menuAnchor,
                                               setMenuAnchor,
                                               handleAction,
                                           }) => {
    return (
        <Menu
            ref={menuRef}
            open={!!menuAnchor}
            anchorEl={menuAnchor}
            onClose={() => setMenuAnchor(null)}>
            <MenuItem onClick={() => {
                setMenuAnchor(null);
                handleAction("edge", "edit", "match");
            }}>
                <ListItemDecorator>
                    <Edit/>
                </ListItemDecorator>
                Edit Match
            </MenuItem>
        </Menu>
    )
}