import React from "react";
import {ListItemDecorator, Menu, MenuItem} from "@mui/joy";
import Edit from "@mui/icons-material/Edit";
import DeleteForever from "@mui/icons-material/DeleteForever";
import WarningAmberOutlinedIcon from "@mui/icons-material/WarningAmberOutlined";

export const MenuDndNode: React.FC<any> = ({
                                               menuRef,
                                               menuAnchor,
                                               setMenuAnchor,
                                               isDev,
                                               handleAction,
                                               selectedNode,
                                               showDelete,
                                               onlyView
                                           }) => {
    const isEndingCallNode = selectedNode?.data?.isEndNode;
    const noDelete = (selectedNode?.data?.noDelete || !showDelete);

    return (
        <Menu
            ref={menuRef}
            open={!!menuAnchor}
            anchorEl={menuAnchor}
            onClose={() => setMenuAnchor(null)}>

            {(!isEndingCallNode && !onlyView) && (
                <>
                    <MenuItem
                        onClick={() => {
                            setMenuAnchor(null);
                            handleAction("node", "edit", "objective");
                        }}>
                        <ListItemDecorator>
                            <Edit/>
                        </ListItemDecorator>
                        Edit Objective
                    </MenuItem>
                    <MenuItem
                        onClick={() => {
                            setMenuAnchor(null);
                            handleAction("node", "edit", "disposition");
                        }}>
                        <ListItemDecorator>
                            <Edit/>
                        </ListItemDecorator>
                        Edit Disposition
                    </MenuItem>
                </>
            )}

            {!noDelete ? (
                <MenuItem
                    onClick={() => {
                        setMenuAnchor(null);
                        handleAction("node", "delete", null);
                    }}
                    color="danger">
                    <ListItemDecorator sx={{color: "inherit"}}>
                        <DeleteForever/>
                    </ListItemDecorator>
                    Delete Connections
                </MenuItem>
            ) : (
                showDelete && <MenuItem
                    onClick={() => {
                        setMenuAnchor(null);
                    }}
                    color="warning">
                    <ListItemDecorator sx={{color: "inherit"}}>
                        <WarningAmberOutlinedIcon/>
                    </ListItemDecorator>
                    No option available
                </MenuItem>
            )}
        </Menu>
    );
};
