"use client";
import React, {useCallback, useEffect, useMemo, useRef, useState} from "react";
import React<PERSON>low, {
    addEdge,
    Background,
    Connection,
    Controls,
    Edge,
    Node as FlowNode,
    useEdgesState,
    useNodesState,
    useReactFlow,
} from "react-flow-renderer";
import {Box, Grid} from "@mui/joy";
import {TaskDndNodes} from "@/app/_components/callScriptsConf/dnd/TaskDndNodes";
import {ControlDndNodes} from "@/app/_components/callScriptsConf/dnd/ControlDndNodes";
import {useDnD} from "@/app/_components/callScriptsConf/dnd/DndContext";
import {generateId} from "@/app/_lib/utils/generateId";
import _ from "lodash";
import {showError, showInfo, showSuccess} from "@/app/_components/alerts/toast/ToastMessages";
import {setIdNodeAddedPanel} from "@/app/_components/table/states/nodeSriptsStates";
import Modal from "@mui/joy/Modal";
import ModalDialog from "@mui/joy/ModalDialog";
import ModalClose from "@mui/joy/ModalClose";
import Typography from "@mui/joy/Typography";
import Button from "@mui/joy/Button";
import {UpdatedNodeComponent} from "@/app/_components/callScriptsConf/dnd/UpdatedNodeComponent";
import CustomEdge from "@/app/_components/callScriptsConf/edges/CustomEdge";
import {MenuDndNode} from "@/app/_components/callScriptsConf/dnd/MenuDndNode";
import {MenuDndEdge} from "@/app/_components/callScriptsConf/dnd/MenuDndEdge";
import {styledName} from "@/app/_components/utils/styledName";
import CircularProgress from "@mui/material/CircularProgress";
import CustomNode from "@/app/_components/callScriptsConf/nodes/CustomNode";
import {DataObject} from "@/app/_components/callScriptsConf/types/DataObject";

const taskPanelsStyle = {
    height: "80vh",
    border: "3px solid #ddd",
    justifyContent: "center",
    alignItems: "center",
};

export const ReactFlowView: React.FC<any> = ({
                                                 nodesRF,
                                                 edgesRF,
                                                 orphanNodes,
                                                 callCampaignId,
                                                 endCallNode,
                                                 terminateData,
                                                 isDev,
                                                 data,
                                                 tableName,
                                                 viewDnd,
                                                 onlyView = false
                                             }) => {
    const reactFlowWrapper = useRef<HTMLDivElement>(null);
    const [scriptsData, setScriptsData] = useState<DataObject[]>(data);
    const [nodes, setNodes, onNodesChange] = useNodesState<FlowNode>(nodesRF);
    const [edges, setEdges, onEdgesChange] = useEdgesState(edgesRF);
    const {project} = useReactFlow();
    const [type] = useDnD();
    const [loadingSub, setLoadingSub] = useState(false);
    const [nodeData, setNodeData] = useState<any>({});
    const [menuNodeAnchor, setMenuNodeAnchor] = useState<HTMLElement | null>(null);
    const [menuEdgeAnchor, setMenuEdgeAnchor] = useState<HTMLElement | null>(null);
    const [selectedNode, setSelectedNode] = useState<any>(null);
    const [selectedEdge, setSelectedEdge] = useState<any>(null);
    const menuRef = useRef<HTMLDivElement>(null);
    const [openModal, setOpenModal] = useState(false);
    const [dataModal, setDataModal] = useState({
        type: null,
        action: null,
        field: null
    });
    const [openMatchModal, setOpenMatchModal] = useState(false);
    const [matchValue, setMatchValue] = useState("");
    const [pendingParentNode, setPendingParentNode] = useState<any>(null);
    const [pendingChildNode, setPendingChildNode] = useState<any>(null);
    const [pendingNewNodes, setPendingNewNodes] = useState<any[]>([]);
    const [pendingNewEdges, setPendingNewEdges] = useState<any[]>([]);
    const [pendingAddNodeId, setPendingAddNodeId] = useState<string | null>(null);
    const [availableNodes, setAvailableNodes] = useState(orphanNodes);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setMenuNodeAnchor(null);
                setMenuEdgeAnchor(null);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    useEffect(() => {
        if (openMatchModal) {
            setMatchValue("");
        }
    }, [openMatchModal]);

    const nodeTypes = useMemo(
        () => ({
            doubleNode: CustomNode,
        }),
        []
    );

    const edgeTypes = useMemo(
        () => ({
            customEdge: CustomEdge,
        }),
        []
    );

    const detectParentNode = useCallback(
        (position: { x: number; y: number }) => {
            return nodes.find((node) => {
                const width = node.width || 265;
                const height = node.height || 160;
                const withinX =
                    position.x >= node.position.x &&
                    position.x <= node.position.x + width;
                const withinY =
                    position.y >= node.position.y &&
                    position.y <= node.position.y + height;
                return withinX && withinY;
            });
        },
        [nodes]
    );

    const onConnectHandler = useCallback(
        (params: Connection) => setEdges((eds) => addEdge(params, eds)),
        []
    );

    const onDragOverHandler = useCallback((event: React.DragEvent) => {
        event.preventDefault();
        event.dataTransfer.dropEffect = "move";
    }, []);

    const checkDup = useCallback(
        (id: string) => {
            const findNode = nodes.find((node: FlowNode) => node.id === id);
            return !findNode;
        },
        [nodes]
    );

    const getDispositionById = useCallback(
        (nodeId: string): string => {
            const node = scriptsData.find((n) => n._id === nodeId);
            if (node) return node.disposition;
            const terminateNode = terminateData.find((n) => n._id === nodeId);
            if (terminateNode) return terminateNode.disposition;
            return "default_disposition";
        },
        [scriptsData, terminateData]
    );

    const onDrop = useCallback(
        async (event: React.DragEvent) => {
            event.preventDefault();
            if (!type) return;
            if (!reactFlowWrapper.current) return;

            const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
            const droppedPosition = project({
                x: event.clientX - reactFlowBounds.left,
                y: event.clientY - reactFlowBounds.top,
            });
            const roundedPosition = {
                x: Math.round(droppedPosition.x),
                y: Math.round(droppedPosition.y),
            };

            const parentNode: any = detectParentNode(roundedPosition);
            if (!parentNode) {
                showInfo("You can't create a new node without selecting a parent");
                return;
            }

            if (parentNode?.data?.isEndNode) {
                showInfo("Cannot create a child under an ending node. Control nodes must remain final.");
                return;
            }

            const draggedNodeData: DataObject = type["data"] ?? {};
            const isEndType = !!draggedNodeData.endType;
            const disposition = getDispositionById(draggedNodeData._id);

            const realBackendId = draggedNodeData._id;
            const uiNodeId = isEndType
                ? `${realBackendId}-ui-${generateId()}`
                : realBackendId;

            let dataAddNode: FlowNode = {
                id: uiNodeId,
                type: "doubleNode",
                data: {
                    backendId: realBackendId,
                    label: draggedNodeData?.objective || "No objective",
                    disposition: disposition,
                },
                position: roundedPosition,
                style: {backgroundColor: "white", border: "2px solid black"},
            };

            let additionalNode: FlowNode | null = null;
            let additionalEdge: Edge | null = null;

            if (type["node"] && !isEndType) {
                const {
                    _id: specialNodeId,
                    objective,
                    disposition: disEndNode
                } = endCallNode;
                const additionalNodeId = generateId();
                additionalNode = {
                    id: additionalNodeId,
                    type: "doubleNode",
                    data: {
                        label: objective,
                        disposition: disEndNode,
                        isEndNode: isEndType
                    },
                    position: {x: roundedPosition.x, y: roundedPosition.y + 200},
                    style: {backgroundColor: "#FFCCCB", border: "2px dashed red"},
                };
                additionalEdge = {
                    id: `e${realBackendId}-${additionalNodeId}-new`,
                    source: realBackendId,
                    target: additionalNodeId,
                    type: "customEdge",
                    animated: false,
                    style: {stroke: "#000", strokeWidth: 2},
                    markerEnd: {
                        // @ts-ignore
                        type: "arrowclosed",
                        width: 20,
                        height: 20,
                        color: "#000",
                    },
                    data: {
                        label: "find if there are no other matches",
                        disposition: "incomplete",
                    },
                };
            } else if (isEndType) {
                const styleNode =
                    draggedNodeData.endType === "green"
                        ? {backgroundColor: "#c8e6c9", border: "2px dashed green"}
                        : {backgroundColor: "#FFCCCB", border: "2px dashed red"};

                dataAddNode = {
                    ...dataAddNode,
                    style: styleNode,
                    data: {
                        backendId: realBackendId,
                        label: draggedNodeData.objective,
                        disposition: disposition,
                        idEndNode: endCallNode?._id ?? null,
                        isEndNode: true,
                    },
                };
            }

            const newPendingNodes: FlowNode[] = [dataAddNode];
            const newPendingEdges: Edge[] = [];

            if (additionalNode) newPendingNodes.push(additionalNode);
            if (additionalEdge) newPendingEdges.push(additionalEdge);

            setMatchValue("");
            setPendingParentNode({
                parentId: parentNode.id,
                draggedNodeData,
                childNode: dataAddNode,
                childNodeId: isEndType ? realBackendId : dataAddNode.id,
                isEndType,
            });
            setPendingChildNode(dataAddNode);
            setPendingNewNodes(newPendingNodes);
            setPendingNewEdges(newPendingEdges);
            setOpenMatchModal(true);
        },
        [type, project, detectParentNode, getDispositionById, endCallNode]
    );

    const onNodeCreated = (nodeData: DataObject) => {
        setScriptsData([...scriptsData, nodeData]);
    };

    const handleConfirmMatch = async () => {
        if (!matchValue.trim()) {
            showError("The match field cannot be empty.");
            return;
        }
        setOpenMatchModal(false);
        setLoadingSub(true);

        const {parentId, childNodeId, childNode, isEndType} = pendingParentNode;
        if (!childNodeId) {
            showError("Child node ID not found to update child");
            setLoadingSub(false);
            return;
        }
        const matchToAssign = matchValue.trim();

        const updateParent = {
            collection: tableName,
            filter: {_id: parentId},
            update: {
                $push: {
                    responses: {
                        scriptId: childNodeId,
                        match: matchToAssign,
                        disposition: "incomplete",
                    },
                },
            },
            options: {
                upsert: true
            }
        };

        try {
            const updateParentResult = await fetch("/api/mongo/call-center/update-data", {
                method: "PUT",
                headers: {"Content-Type": "application/json"},
                body: JSON.stringify(updateParent),
            });
            if (!updateParentResult.ok) {
                const errorResponse = await updateParentResult.text();
                showError("Problem updating parent node: " + errorResponse);
                setLoadingSub(false);
                return;
            } else {
                setScriptsData((prev) =>
                    prev.map((node) => {
                        if (node._id === parentId) {
                            return {
                                ...node,
                                responses: [
                                    ...node.responses,
                                    {
                                        scriptId: childNodeId,
                                        match: matchToAssign,
                                        disposition: "incomplete",
                                    },
                                ],
                            };
                        }
                        return node;
                    })
                );
            }

            if (!isEndType) {
                let endingCallId = endCallNode._id;
                const dashIndex = endingCallId.indexOf("-");
                if (dashIndex !== -1) {
                    endingCallId = endingCallId.substring(0, dashIndex);
                }
                const parentNodeData = scriptsData.find((p: any) => p._id === parentId);
                let childResponses: any[] = [];

                if (parentNodeData && Array.isArray(parentNodeData.responses)) {
                    const responseForChild = parentNodeData.responses.find(
                        (r: any) => r.scriptId === childNodeId && r.disposition !== "DNQ"
                    );
                    if (responseForChild) {
                        childResponses.push(responseForChild);
                    }
                }
                const hasDNQ = childResponses.some((r: any) => r.disposition === "DNQ");
                if (!hasDNQ) {
                    const defaultTerminateNode = terminateData.find(
                        (n) => n._id === endingCallId
                    );
                    const defaultDisposition = defaultTerminateNode
                        ? defaultTerminateNode.disposition
                        : "DNQ";
                    childResponses.push({
                        scriptId: endCallNode._id,
                        match: "find if there are no other matches",
                        disposition: "incomplete",
                    });
                }
                const updatedChildFromId = {
                    collection: tableName,
                    filter: {_id: childNodeId},
                    update: {
                        $set: {
                            origin: "child",
                            responses: childResponses,
                            disposition: childNode?.data?.disposition || "incomplete",
                        },
                    },
                    options: {
                        upsert: true
                    }
                };
                const updateChildResult = await fetch("/api/mongo/call-center/update-data", {
                    method: "PUT",
                    headers: {"Content-Type": "application/json"},
                    body: JSON.stringify(updatedChildFromId),
                });
                if (!updateChildResult.ok) {
                    const errorResponse = await updateChildResult.text();
                    showError("Problem updating child node: " + errorResponse);
                } else {
                    setScriptsData((prev) =>
                        prev.map((node) => {
                            if (node._id === childNodeId) {
                                return {
                                    ...node,
                                    origin: "child",
                                    responses: childResponses,
                                };
                            }
                            return node;
                        })
                    );
                }
            }

            let dynamicDisposition = "incomplete";
            const terminateNode = _.find(terminateData, {_id: childNodeId});
            if (terminateNode && terminateNode._id !== endCallNode._id) {
                dynamicDisposition = terminateNode.disposition;
            } else if (childNodeId === endCallNode._id) {
                dynamicDisposition = endCallNode.disposition;
            }

            const newEdge: Edge = {
                id: `e${parentId}-${childNode.id}-${generateId()}`,
                source: parentId,
                target: childNode.id,
                type: "customEdge",
                style: {stroke: "#000", strokeWidth: 2, strokeDasharray: "5,5"},
                data: {
                    label: matchToAssign,
                    disposition: dynamicDisposition,
                    idEndNode: endCallNode?._id ?? null
                },
                markerEnd: {
                    // @ts-ignore
                    type: "arrowclosed",
                    width: 20,
                    height: 20,
                    color: "#000",
                },
            };

            setNodes((nds) => [...nds, ...pendingNewNodes]);
            if (pendingNewEdges.length > 0) {
                setEdges((eds) => eds.concat([newEdge, ...pendingNewEdges]));
            } else {
                setEdges((eds) => eds.concat(newEdge));
            }
            if (pendingAddNodeId) {
                setAvailableNodes((prev) =>
                    prev.filter((n: any) => n._id !== pendingAddNodeId)
                );
            }
            setIdNodeAddedPanel(childNodeId);
            showSuccess("Updates completed successfully");
        } catch (error) {
            showError("We had a problem updating nodes");
        } finally {
            setLoadingSub(false);
            resetVariables();
        }
    };

    const handleCancelMatch = () => {
        setOpenMatchModal(false);
        setMatchValue("");
        setPendingParentNode(null);
        setPendingChildNode(null);
        setPendingNewNodes([]);
        setPendingNewEdges([]);
        setPendingAddNodeId(null);
        setIdNodeAddedPanel(null);
        showInfo("Match canceled. The node remains in the tasks list.");
    };

    const handleDeleteAction = async () => {
        if (!selectedNode || !selectedNode.id) {
            resetVariables();
            return;
        }
        const childId = (selectedNode?.data?.isEndNode) ? selectedNode?.data?.idEndNode : selectedNode.id;
        const childOriginal = scriptsData.find((item: any) => item._id === childId);
        if (!childOriginal) {
            showError("Selected node not found in data");
            resetVariables();
            return;
        }
        const hasEndType = !!childOriginal.endType;
        const parentNodeData = scriptsData.find(
            (item: any) =>
                Array.isArray(item.responses) &&
                item.responses.some((resp: any) => resp.scriptId === childId)
        );
        if (!parentNodeData) {
            showError("No parent node found that references this child");
            resetVariables();
            return;
        }

        const filteredResponses = (selectedNode?.data?.isEndNode)
            ? parentNodeData.responses.filter((r: any) => r.scriptId !== childId || r.match !== selectedNode?.data?.match)
            : parentNodeData.responses.filter((r: any) => r.scriptId !== childId);

        const parentUpdate = {
            collection: tableName,
            filter: {_id: parentNodeData._id},
            update: {
                $set: {
                    responses: filteredResponses,
                },
            },
            options: {
                upsert: true
            }
        };
        const childUpdateData = hasEndType
            ? null
            : {
                origin: "orphan",
                responses: [],
            };
        const childUpdate = childUpdateData
            ? {
                collection: tableName,
                filter: {_id: childOriginal._id},
                update: {
                    $set: childUpdateData,
                },
                options: {
                    upsert: true
                }
            }
            : null;
        try {
            setLoadingSub(true);
            const updateParentResult = await fetch("/api/mongo/call-center/update-data", {
                method: "PUT",
                headers: {"Content-Type": "application/json"},
                body: JSON.stringify(parentUpdate),
            });
            if (!updateParentResult.ok) {
                await updateParentResult.text();
                showError("We had a problem updating the parent node");
                setLoadingSub(false);
                resetVariables();
                return;
            }
            if (childUpdate) {
                const updateChildResult = await fetch("/api/mongo/call-center/update-data", {
                    method: "PUT",
                    headers: {"Content-Type": "application/json"},
                    body: JSON.stringify(childUpdate),
                });
                if (!updateChildResult.ok) {
                    await updateChildResult.text();
                    showError("We had a problem updating the child node");
                } else {
                    setScriptsData((prev) =>
                        prev.map((node) => {
                            if (node._id === childId) {
                                return {...node, origin: "orphan", responses: []};
                            }
                            return node;
                        })
                    );
                }
            }
            if (!hasEndType) {
                const queue: string[] = [];
                childOriginal.responses.forEach((response: any) => {
                    queue.push(response.scriptId);
                });
                while (queue.length > 0) {
                    const currentId = queue.shift()!;
                    const currentNode = scriptsData.find(
                        (node: any) => node._id === currentId
                    );
                    if (!currentNode) continue;
                    if (currentNode.intent === "endingCall") continue;
                    if (currentNode.origin === "orphan") continue;
                    const descendantUpdate = {
                        collection: tableName,
                        filter: {_id: currentId},
                        update: {
                            $set: {
                                origin: "orphan",
                                responses: [],
                            },
                        },
                        options: {
                            upsert: true
                        }
                    };
                    const updateDescendantResult = await fetch("/api/mongo/call-center/update-data", {
                        method: "PUT",
                        headers: {"Content-Type": "application/json"},
                        body: JSON.stringify(descendantUpdate),
                    });
                    if (!updateDescendantResult.ok) {
                        showError(`We had a problem updating node ${currentId}`);
                    } else {
                        showInfo(`Node ${currentId} set to orphan`);
                        setScriptsData((prev) =>
                            prev.map((node) => {
                                if (node._id === currentId) {
                                    return {...node, origin: "orphan", responses: []};
                                }
                                return node;
                            })
                        );
                        currentNode.responses.forEach((response: any) => {
                            queue.push(response.scriptId);
                        });
                    }
                }
            }
            setNodes((nds) => nds.filter((node: FlowNode) => node.id !== childId));
            setEdges((eds) =>
                eds.filter((edge: Edge) => edge.source !== childId && edge.target !== childId)
            );
            showSuccess("Parent, child, and applicable descendants updated successfully");
            if (callCampaignId) {
                window.location.reload();
            } else {
                console.error("callCampaignId está indefinido o es nulo");
            }
        } catch (error) {
            showError("We had a problem updating nodes");
        } finally {
            setLoadingSub(false);
            resetVariables();
        }
    };

    const handleAction = (typeMenu: string, actionMenu: string, fieldMenu: string) => {
        if (actionMenu === "showData") {
            setDataModal({type: typeMenu, action: actionMenu, field: fieldMenu});
            setOpenModal(true);
            return;
        }
        if (typeMenu === "node") {
            getDataNode(selectedNode);
        } else {
            getDataEdge(selectedEdge);
        }
        setDataModal({type: typeMenu, action: actionMenu, field: fieldMenu});
        setOpenModal(true);
    };

    const getDataNode = (dataNode: any) => {
        if (!dataNode) return;
        const isEndNode = dataNode?.data?.isEndNode ?? false;
        const realId = dataNode?.data?.backendId || dataNode?.id;
        const currentNodeData = isEndNode
            ? endCallNode
            : scriptsData.find((item: any) => item && item?._id === realId);
        setNodeData(currentNodeData || {});
    };

    const getDataEdge = (dataEdge: any) => {
        if (!dataEdge) return;
        const currentEdgeData = scriptsData.find(
            (item: any) => item && item?._id === dataEdge?.source
        );
        setNodeData(currentEdgeData || {});
    };

    const getBodyModal = () => {
        if (dataModal?.action === "showData") {
            let nodeId = selectedNode?.id;
            if (selectedNode?.data?.isEndNode) {
                const dashIndex = nodeId.indexOf("-");
                if (dashIndex !== -1) {
                    nodeId = nodeId.substring(0, dashIndex);
                }
            }
            const nodeData = scriptsData.find((item) => item._id === nodeId);
            return (
                <>
                    <Typography component="h2" fontWeight="bold">
                        Node Data
                    </Typography>
                    <Typography sx={{mt: 2}}>
                        <strong>ID:</strong> {nodeData?._id || "N/A"}
                    </Typography>
                    <Typography
                        sx={{mt: 1, wordWrap: "break-word", maxWidth: "100%"}}
                    >
                        <strong>Objective:</strong>{" "}
                        <span
                            style={{
                                display: "inline-block",
                                whiteSpace: "normal",
                                wordWrap: "break-word",
                            }}
                        >
              {nodeData?.objective || "N/A"}
            </span>
                    </Typography>
                    <Button
                        sx={{mt: 2}}
                        color="neutral"
                        onClick={() => setOpenModal(false)}
                    >
                        Close
                    </Button>
                </>
            );
        }
        const idsSearch =
            dataModal?.type === "node"
                ? {
                    id: selectedNode?.id,
                }
                : {
                    source: selectedEdge?.source,
                    target: selectedEdge?.data?.idEndNode
                        ? selectedEdge?.data?.idEndNode
                        : selectedEdge?.target,
                    targetCompound: selectedEdge?.target,
                    match: selectedEdge?.data?.match,
                };
        const urlPath = window.location.pathname;
        const segments = urlPath.split("/");
        const lastSegment = segments[segments.length - 1];
        const isTask = lastSegment === "2";
        switch (dataModal?.action) {
            case "delete":
                return (
                    <>
                        <Typography component="h2" fontWeight="bold">
                            Delete Node Relations
                        </Typography>
                        <Typography sx={{mt: 1}}>
                            Are you sure you want to delete all relations of this node?
                        </Typography>
                        <Box
                            sx={{
                                mt: 2,
                                display: "flex",
                                justifyContent: "flex-end",
                            }}
                        >
                            <Button onClick={() => resetVariables()} sx={{mr: 2}}>
                                Cancel
                            </Button>
                            <Button
                                variant="solid"
                                color="danger"
                                onClick={handleDeleteAction}
                            >
                                Delete
                            </Button>
                        </Box>
                    </>
                );
            case "edit":
                if (dataModal?.type === "node") {
                    if (!selectedNode) {
                        console.error("No node has been selected for editing.");
                        return <Typography>No node has been selected for editing.</Typography>;
                    }
                    return (
                        <>
                            <Typography component="h2" fontWeight="bold">
                                {isTask ? "Task" : `Edit ${styledName(dataModal?.type)} Information`}
                            </Typography>
                            <UpdatedNodeComponent
                                tableName={tableName}
                                nodeData={nodeData}
                                scriptsData={scriptsData}
                                nodesData={nodes}
                                edgesData={edges}
                                idsSearch={idsSearch}
                                type={dataModal?.type}
                                field={dataModal?.field}
                                onSubmit={(
                                    newNodes: any,
                                    newEdges: any,
                                    newScripts: any
                                ) => {
                                    if (!_.isEmpty(newNodes)) setNodes(newNodes);
                                    if (!_.isEmpty(newEdges)) setEdges(newEdges);
                                    if (!_.isEmpty(newScripts)) setScriptsData(newScripts);
                                    resetVariables();
                                }}
                            />
                        </>
                    );
                } else if (dataModal?.type === "edge") {
                    if (!selectedEdge) {
                        console.error("No node has been selected for editing.");
                        return <Typography>No node has been selected for editing.</Typography>;
                    }
                    const parentNode = scriptsData.find(node => node._id === selectedEdge.source);
                    const childNode: any = nodes.find((node: FlowNode) => node.id === selectedEdge.target);
                    return (
                        <>
                            {dataModal.field === "match" ? (
                                <Typography component="h2" fontWeight="bold">
                                    {parentNode?.objective}
                                </Typography>
                            ) : (
                                <Typography component="h2" fontWeight="bold">
                                    Edit {styledName(dataModal?.type)} Information
                                </Typography>
                            )}
                            <UpdatedNodeComponent
                                tableName={tableName}
                                nodeData={nodeData}
                                scriptsData={scriptsData}
                                nodesData={nodes}
                                edgesData={edges}
                                idsSearch={idsSearch}
                                type={dataModal?.type}
                                field={dataModal?.field}
                                childLabel={childNode?.data?.label}
                                onSubmit={(newNodes: any, newEdges: any, newScripts: any) => {
                                    if (!_.isEmpty(newNodes)) setNodes(newNodes);
                                    if (!_.isEmpty(newEdges)) setEdges(newEdges);
                                    if (!_.isEmpty(newScripts)) setScriptsData(newScripts);
                                    resetVariables();
                                }}
                            />
                        </>
                    );
                }
            return null;
        }
    };

    const onNodeClick = (event: React.MouseEvent, node: FlowNode) => {
        event.preventDefault();
        setMenuEdgeAnchor(null);
        setSelectedNode(node);
        setMenuNodeAnchor(event.currentTarget as HTMLElement);
    };

    const onEdgeClick = (event: any, edge: Edge) => {
        event.preventDefault();
        setMenuNodeAnchor(null);
        const idScriptNode = edge.target.split("-")[0];
        const copyIndex = edge.target.split("-")[2];
        let isEndConnection = false;
        if (idScriptNode === endCallNode._id && copyIndex === '0') {
            isEndConnection = true;
        }
        const selected = { ...edge, endConnection: isEndConnection };
        setSelectedEdge(selected);
        if (!onlyView && !isEndConnection) {
            getDataEdge(selected);
            handleAction("edge", "edit", "match");
        }
    };

    const onPaneClick = useCallback(() => {
        setMenuNodeAnchor(null);
        setMenuEdgeAnchor(null);
    }, []);

    const resetVariables = () => {
        setLoadingSub(false);
        setOpenModal(false);
        setMenuNodeAnchor(null);
        setMenuEdgeAnchor(null);
        setSelectedNode(null);
        setSelectedEdge(null);
        setNodeData({});
        setDataModal({
            type: null,
            action: null,
            field: null
        });
    };

    return (
        <Grid container spacing={2}>
            {viewDnd && (
                <Grid xs={2}>
                    <Box sx={{...taskPanelsStyle, padding: "1rem"}}>
                        <TaskDndNodes
                            orphanNodes={availableNodes}
                            endCallNode={endCallNode}
                            callCampaignId={callCampaignId}
                            onNodeCreated={onNodeCreated}
                        />
                    </Box>
                </Grid>
            )}
            <Grid xs={viewDnd ? 8 : 12}>
                {isDev && <div>{selectedNode?.id || ""}</div>}
                <div ref={reactFlowWrapper} style={{width: "100%", height: "100%"}}>
                    <Box sx={taskPanelsStyle}>
                        <ReactFlow
                            nodes={nodes}
                            edges={edges}
                            onNodesChange={onNodesChange}
                            onEdgesChange={onEdgesChange}
                            onConnect={onConnectHandler}
                            nodeTypes={nodeTypes}
                            edgeTypes={edgeTypes}
                            onDrop={onDrop}
                            onDragOver={onDragOverHandler}
                            nodesConnectable={false}
                            fitView
                            style={{backgroundColor: "#F7F9FB"}}
                            onNodeClick={onNodeClick}
                            onEdgeClick={onEdgeClick}
                            onPaneClick={onPaneClick}
                        >
                            <Background/>
                            <Controls/>
                        </ReactFlow>
                    </Box>
                </div>
            </Grid>
            {viewDnd && (
                <Grid xs={2}>
                    <Box sx={{...taskPanelsStyle, padding: "1rem"}}>
                        <ControlDndNodes terminateData={terminateData} isDev={isDev}/>
                    </Box>
                </Grid>
            )}
            {menuNodeAnchor && (
                <MenuDndNode
                    menuRef={menuRef}
                    isDev={isDev}
                    menuAnchor={menuNodeAnchor}
                    setMenuAnchor={setMenuNodeAnchor}
                    handleAction={handleAction}
                    selectedNode={selectedNode}
                    showDelete={viewDnd}
                    onlyView={onlyView}
                />
            )}
            <Modal
                open={openModal}
                onClose={() => {
                    resetVariables();
                }}
            >
                <ModalDialog>
                    <ModalClose/>
                    {getBodyModal()}
                </ModalDialog>
            </Modal>
            <Modal open={openMatchModal} onClose={handleCancelMatch}>
                <ModalDialog>
                    <ModalClose/>
                    <Typography component="h2" fontWeight="bold">
                        Add Match
                    </Typography>
                    <Typography sx={{mt: 1}}>
                        Please add the match field before saving the relationship:
                    </Typography>
                    <input
                        type="text"
                        value={matchValue}
                        onChange={(e) => setMatchValue(e.target.value)}
                        style={{width: "100%", marginTop: "8px"}}
                        placeholder="Enter match text"
                    />
                    <Box sx={{mt: 2, display: "flex", justifyContent: "flex-end"}}>
                        <Button onClick={handleCancelMatch}>Cancel</Button>
                        <Button
                            variant="solid"
                            color="primary"
                            sx={{ml: 2}}
                            onClick={handleConfirmMatch}
                        >
                            Confirm
                        </Button>
                    </Box>
                </ModalDialog>
            </Modal>
            {loadingSub && (
                <Modal open={loadingSub} hideBackdrop>
                    <ModalDialog
                        variant="plain"
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            justifyContent: "center",
                            minWidth: "300px",
                            padding: "2rem",
                        }}
                    >
                        <CircularProgress/>
                        <Typography sx={{mt: 2}}>Loading...</Typography>
                    </ModalDialog>
                </Modal>
            )}
        </Grid>
    );
};
