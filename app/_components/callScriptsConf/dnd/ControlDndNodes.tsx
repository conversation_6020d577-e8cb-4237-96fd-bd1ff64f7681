import React, {useEffect, useState} from 'react';
import Typography from "@mui/joy/Typography";
import {useDnD} from './DndContext';
import {Box, Chip, Stack, Tooltip} from '@mui/joy';
import lodash from "lodash";

const styleNode: any = {
    borderRadius: '5px',
    border: '1px solid black',
    padding: '10px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    margin: '15px 0',
};

export const ControlDndNodes: React.FC<any> = ({terminateData, isDev}) => {
    const [controlNodes, setControlNodes] = useState([]);
    const [_, setType] = useDnD();

    useEffect(() => {
        const filterTD = !lodash.isEmpty(terminateData)
            ? terminateData.filter((item: any) =>
                !lodash.isEmpty(item?.endType) && item?.controlNode === true
            )
            : [];
        setControlNodes(filterTD);
    }, [terminateData]);

    const onDragStart = (event: any, nodeType: string, data: any) => {
        setType({endType: nodeType, data});
        event.dataTransfer.effectAllowed = 'move';
    };

    return (<>
        <div className="description">
            <Typography level="body-lg">
                Control Nodes
            </Typography>
            <Typography level="body-xs">
                You can drag these nodes to the panel
            </Typography>
        </div>
        <Box sx={{overflowY: 'auto', maxHeight: '90%', width: '100%'}}>
            {controlNodes.length > 0 && controlNodes.map((controlNode: any, index: number) => (
                <div
                    style={{
                        ...styleNode,
                        backgroundColor: ((controlNode?.endType === 'red') ? '#FFCCCB' : '#c8e6c9')
                    }}
                    onDragStart={(event) => onDragStart(event, controlNode?.endType, controlNode)}
                    key={index} className="dndnode" draggable>
                    <Tooltip
                        arrow
                        sx={{width: '200px'}}
                        title={
                            <div>
                                <Typography level="body-sm">
                                    <strong>Objective:</strong> {controlNode.objective || "Unnamed Node"}
                                </Typography>
                                {isDev && (
                                    <Typography level="body-sm">
                                        <strong>ID:</strong> {controlNode._id || "No ID"}
                                    </Typography>
                                )}
                            </div>
                        }
                        variant="soft"
                        size="sm"
                        placement="left">
                        <Stack
                            spacing={1}
                            sx={{
                                fontSize: '10px',
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                color: 'black'
                            }}>
                            <Typography level="body-sm">
                                {controlNode?.objective || "Unnamed Node"}
                            </Typography>
                            <Chip variant="outlined" size="sm">
                                {controlNode?.disposition || 'No defined'}
                            </Chip>
                        </Stack>
                    </Tooltip>
                </div>
            ))}
        </Box>
    </>)
}
