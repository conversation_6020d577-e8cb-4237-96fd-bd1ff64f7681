import {createContext, useContext, useState} from 'react';

interface typeInterface {
    node?: string,
    endType?: string,
    data: any
}

const DnDContext = createContext([null, (_: typeInterface) => {}]);

export const DnDProvider = ({children}) => {
    const [type, setType] = useState(null);

    return (
        <DnDContext.Provider value={[type, setType]}>
            {children}
        </DnDContext.Provider>
    );
}

export default DnDContext;

export const useDnD = () => {
    return useContext(DnDContext);
}