import {<PERSON>, <PERSON><PERSON>, Sheet, Typography} from "@mui/joy";
import React from "react";

export const DataPromptDisplay: React.FC<any> = ({actualPrompt, isLoading}) => {
    const formatText = (text: string) => {
        const elements = [];
        const lines = text.split('\n');

        lines.forEach((line, index) => {
            const parts = line.split(/(\*\*.*?\*\*|<knowledge-base>|<\/knowledge-base>)/);

            const formattedLine = parts.map((part, idx) => {
                if (/^\*\*(.*?)\*\*$/.test(part)) {
                    return (
                        <Typography
                            component="span"
                            level="body-md"
                            fontWeight="bold"
                            key={`bold-${index}-${idx}`}>
                            {part.replace(/\*\*/g, '')}
                        </Typography>
                    );
                } else if (part === '<knowledge-base>') {
                    return (
                        <Typography
                            component="div"
                            level="body-lg"
                            fontWeight="bold"
                            sx={{ mt: 2, mb: 1 }}
                            key={`knowledge-title-${index}-${idx}`}>
                            KNOWLEDGE BASE:
                        </Typography>
                    );
                } else if (part === '</knowledge-base>') {
                    return null;
                }

                return (
                    <Typography
                        component="span"
                        level="body-md"
                        key={`normal-${index}-${idx}`}>
                        {part}
                    </Typography>
                );
            });

            elements.push(
                <Typography
                    component="div"
                    level="body-md"
                    sx={{ mb: 1 }}
                    key={`line-${index}`}>
                    {formattedLine}
                </Typography>
            );
        });

        return elements;
    };

    return (
        <Sheet
            sx={{
                margin: 'auto',
                display: 'flex',
                height: '100%',
                flexGrow: 1,
                flexDirection: 'column',
                borderRadius: 'md',
                boxShadow: 'lg',
                p: 2,
                gap: 2,
                mb: 2,
                bgcolor: 'background.body',
            }}>
            <Typography level="h3">Prompt</Typography>
            <Box
                sx={{
                    height: 340,
                    flexGrow: 1,
                    overflowY: 'auto',
                    display: 'flex',
                    flexDirection: 'column',
                    padding: '1rem',
                    borderRadius: 'sm',
                    border: '1px solid',
                    borderColor: 'neutral.outlinedBorder',
                    bgcolor: '#fafafa',
                }}>
                {!isLoading ?
                    <Box>{formatText(actualPrompt || 'No prompt set')}</Box> :
                    <Button loading variant="plain"></Button>}
            </Box>
        </Sheet>
    )
}
