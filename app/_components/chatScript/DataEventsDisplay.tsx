import React, {useEffect, useState} from "react";
import {
    <PERSON>,
    Sheet,
    Typography,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    AccordionGroup,
    Divider,
    List,
    ListItem,
    Button
} from "@mui/joy";

interface TriggerDoc {
    _id: string;
    event?: string;
    created_at?: string;
    metadata?: Record<string, any>;

    [key: string]: any;
}

export const DataEventsDisplay: React.FC<{ sessionId?: string }> = ({sessionId}) => {
    const [events, setEvents] = useState<TriggerDoc[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!sessionId) return;

        const fetchEvents = async () => {
            setLoading(true);
            setError(null);

            try {
                const res = await fetch("/api/mongo/getItemsById", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                        collection: "triggers",
                        key: "session_id",
                        value: sessionId,
                    }),
                });

                const data = await res.json();

                if (res.ok && data.response) {
                    setEvents(data.response as TriggerDoc[]);
                } else {
                    setError(data.error || "Unable to fetch events.");
                }
            } catch (err) {
                console.error(err);
                setError("Network error while fetching events.");
            } finally {
                setLoading(false);
            }
        };

        fetchEvents();
    }, [sessionId]);

    return (
        <Sheet
            sx={{
                display: 'flex',
                flexDirection: 'column',
                height: '100%',
                flexGrow: 1,
                maxHeight: '67vh',
                overflowY: 'auto',
                borderRadius: 'md',
                boxShadow: 'lg',
                p: 2,
                gap: 2,
                bgcolor: 'background.body',
            }}
        >
            {loading
                ? <Button loading variant="plain"/>
                : error
                    ? <Typography level="body-sm" color="danger">{error}</Typography>
                    : (!events.length
                            ? <Typography level="body-sm" sx={{color: 'text.secondary'}}>No events to display
                                yet.</Typography>
                            : (
                                <AccordionGroup
                                    sx={{"--Accordion-gap": "0.5rem"}}
                                    variant="plain"
                                >
                                    {events.map((evt) => (
                                        <Accordion key={evt._id}>
                                            <AccordionSummary>
                                                <Typography level="body-sm" sx={{flex: 1}}>
                                                    {evt.event || "Unnamed trigger"}
                                                </Typography>
                                                {evt.created_at && (
                                                    <Typography level="body-xs" sx={{color: "text.secondary"}}>
                                                        {new Date(evt.created_at).toLocaleString()}
                                                    </Typography>
                                                )}
                                            </AccordionSummary>
                                            <AccordionDetails sx={{pl: 2}}>
                                                <Box
                                                    sx={{
                                                        p: 2,
                                                        borderRadius: 'sm',
                                                        border: '1px solid',
                                                        borderColor: 'neutral.outlinedBorder',
                                                        bgcolor: '#fafafa',
                                                        overflowWrap: 'break-word',
                                                        wordBreak: 'break-word',
                                                        width: '100%',
                                                        overflowY: 'auto',
                                                        height: 340
                                                    }}
                                                >
                                                    <List>
                                                        {Object.entries(evt).map(([key, value]) => {
                                                            if (key === 'metadata' || key === '_id') return null;
                                                            return (
                                                                <ListItem key={key} sx={{py: 0.5}}>
                                                                    <Typography component="span" fontWeight="bold"
                                                                                sx={{fontSize: '10px'}}>
                                                                        {key}:
                                                                    </Typography>{' '}
                                                                    <Typography component="span"
                                                                                sx={{fontSize: '10px'}}>
                                                                        {String(value)}
                                                                    </Typography>
                                                                </ListItem>
                                                            );
                                                        })}
                                                        {evt.metadata && (
                                                            <>
                                                                <Typography sx={{mt: 1, mb: 0.5, fontSize: '10px'}}>
                                                                    Metadata
                                                                </Typography>
                                                                {Object.entries(evt.metadata).map(([mKey, mValue]) => (
                                                                    <ListItem key={mKey} sx={{
                                                                        pl: 2,
                                                                        py: 0.25,
                                                                        overflowWrap: 'break-word',
                                                                        wordBreak: 'break-word'
                                                                    }}>
                                                                        <Typography component="span" fontStyle="italic"
                                                                                    sx={{fontSize: '10px'}}>
                                                                            {mKey}:
                                                                        </Typography>{' '}
                                                                        <Typography component="span"
                                                                                    sx={{fontSize: '10px'}}>
                                                                            {String(mValue)}
                                                                        </Typography>
                                                                    </ListItem>
                                                                ))}
                                                            </>
                                                        )}
                                                    </List>
                                                </Box>
                                            </AccordionDetails>
                                            <Divider/>
                                        </Accordion>
                                    ))}
                                </AccordionGroup>
                            )
                    )
            }
        </Sheet>
    );
};
