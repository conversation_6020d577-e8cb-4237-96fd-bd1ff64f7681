import { Sheet, Typography, Box, Button } from "@mui/joy";
import React from "react";

export const DataKBDisplay: React.FC<{ actualPrompt: string; isLoading?: boolean }> = ({ actualPrompt, isLoading }) => {
    const extractKnowledgeBase = (text: string): string | null => {
        const match = text?.match(/<knowledge-base>([\s\S]*?)<\/knowledge-base>/);
        return match ? match[1].trim() : null;
    };

    const content = extractKnowledgeBase(actualPrompt);

    return (
        <Sheet
            sx={{
                margin: 'auto',
                display: 'flex',
                height: '100%',
                flexGrow: 1,
                flexDirection: 'column',
                borderRadius: 'md',
                boxShadow: 'lg',
                p: 2,
                gap: 2,
                mb: 2,
                bgcolor: 'background.body',
                overflowY: 'hidden'
            }}
        >
            <Typography level="h3">Knowledge Base</Typography>
            <Box
                sx={{
                    height: 340,
                    flexGrow: 1,
                    overflowY: 'auto',
                    display: 'flex',
                    flexDirection: 'column',
                    padding: '1rem',
                    borderRadius: 'sm',
                    border: '1px solid',
                    borderColor: 'neutral.outlinedBorder',
                    bgcolor: '#fafafa',
                    whiteSpace: 'pre-wrap',
                    fontSize: 14,
                }}>
                {isLoading
                  ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <Button loading variant="plain" />
                    </Box>
                  )
                  : (content || 'No knowledge base provided')}
            </Box>
        </Sheet>
    );
};