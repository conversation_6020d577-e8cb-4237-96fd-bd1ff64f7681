import { Box, Sheet, Typography, Button } from "@mui/joy";
import React from "react";

export const DataObjectiveDisplay: React.FC<any> = ({ dataQuest, sessionData, isLoading, systemOptions = [], isCallEnd = false }) => {
    const script = sessionData?.scripts?.find((s: any) => s._id === dataQuest?.newScriptId);
    const responses = script?.responses || [];

    return (
        <Sheet
            sx={{
                margin: 'auto',
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 'md',
                boxShadow: 'lg',
                p: 2,
                gap: 0,
                mb: 2,
                bgcolor: 'background.body',
                height: 620,
            }}
        >
            <Typography level="h3">Objective</Typography>
            <Box
                sx={{
                    minHeight: 120,
                    overflowY: 'auto',
                    padding: '1rem',
                    borderRadius: 'sm',
                    border: '1px solid',
                    borderColor: 'neutral.outlinedBorder',
                    bgcolor: '#fafafa',
                    whiteSpace: 'pre-wrap',
                    fontSize: 14,
                    mb: 2,
                }}
            >
                {isLoading
                  ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <Button loading variant="plain" />
                    </Box>
                  )
                  : (script?.objective || 'No objective provided')}
            </Box>

            <Typography level="h4">Options</Typography>
            <Box
                sx={{
                    minHeight: 160,
                    overflowY: 'auto',
                    padding: '1rem',
                    borderRadius: 'sm',
                    border: '1px solid',
                    borderColor: 'neutral.outlinedBorder',
                    bgcolor: '#fafafa',
                    whiteSpace: 'pre-wrap',
                    fontSize: 14,
                }}
            >
                {isLoading
                  ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <Button loading variant="plain" />
                    </Box>
                  )
                  : (
                    responses.length > 0
                      ? responses.map((resp: any, index: number) => (
                          <Typography key={index} level="body-sm">- {resp.match}</Typography>
                      ))
                      : <Typography level="body-sm">No responses found</Typography>
                )}
            </Box>

            <Typography level="h4">System&nbsp;Options</Typography>
            <Box
                sx={{
                    minHeight: 120,
                    overflowY: 'auto',
                    padding: '1rem',
                    borderRadius: 'sm',
                    border: '1px solid',
                    borderColor: 'neutral.outlinedBorder',
                    bgcolor: '#fafafa',
                    whiteSpace: 'pre-wrap',
                    fontSize: 14,
                }}
            >
                {isCallEnd ? (
                    <Typography level="body-sm">No system options available</Typography>
                ) : isLoading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Button loading variant="plain" />
                    </Box>
                ) : (
                    systemOptions.length > 0 ? (
                        systemOptions.map((opt: any, idx: number) => (
                            <Typography key={idx} level="body-sm">
                                • {opt.match ?? opt.intent}
                            </Typography>
                        ))
                    ) : (
                        <Typography level="body-sm">No system options found</Typography>
                    )
                )}
            </Box>
        </Sheet>
    );
};