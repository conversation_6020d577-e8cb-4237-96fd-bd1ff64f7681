import React from 'react';
import {Box, Button, Sheet, Typography} from '@mui/joy';
import _ from 'lodash';

export const DataQuestionsDisplay: React.FC<any> = ({dataQuestions, isLoading}) => {
    const formatKey = (key: string) => {
        return key.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase());
    };

    return (
        <Sheet
            sx={{
                margin: 'auto',
                display: 'flex',
                height: '100%',
                flexGrow: 1,
                flexDirection: 'column',
                borderRadius: 'md',
                boxShadow: 'lg',
                p: 2,
                gap: 2,
                bgcolor: 'background.body',
            }}>
            <Typography level="h3">Questions Data</Typography>
            <Box
                sx={{
                    flexGrow: 1,
                    overflowY: 'auto',
                    display: 'flex',
                    flexDirection: 'column',
                    padding: '1rem',
                    borderRadius: 'sm',
                    border: '1px solid',
                    borderColor: 'neutral.outlinedBorder',
                    bgcolor: '#fafafa',
                }}>
                {isLoading ?
                    <Button loading variant="plain"></Button> :
                    !_.isEmpty(dataQuestions) ? (
                        Object.entries(dataQuestions).map(([key, value]) => (
                            <Typography key={key} level="body-md" sx={{mb: 1}}>
                                {/* @ts-ignore */}
                                <b>{formatKey(key)}:</b> {value ?? 'No Info'}
                            </Typography>
                        ))
                    ) : (
                        <Typography level="body-md">No data available</Typography>
                    )}
            </Box>
        </Sheet>
    );
};
