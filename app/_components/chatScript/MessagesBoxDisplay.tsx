import React, {useEffect, useRef, useState} from "react";
import {Box, Button, CircularProgress, IconButton, Input, Sheet, Typography,} from "@mui/joy";
import MicIcon from "@mui/icons-material/Mic";
import StopIcon from "@mui/icons-material/Stop";
import {createClient, LiveTranscriptionEvents} from "@deepgram/sdk";
import {useQuery} from "@tanstack/react-query";
import {ParamAMPT} from "@/app/_components/queries/ParamAMPT";
import _ from "lodash";
import {showError, showWarning} from "@/app/_components/alerts/toast/ToastMessages";
import {tryit} from "radash";

export const MessagesBoxDisplay: React.FC<any> = ({
                                                      messages,
                                                      currentStreamingMessage,
                                                      isLoading,
                                                      handleSubmit,
                                                      isCallEnd,
                                                  }) => {
    const [inputMessage, setInputMessage] = useState<string>("");
    const [isRecording, setIsRecording] = useState(false);
    const mediaRecorderRef = useRef<MediaRecorder | null>(null);
    const audioChunksRef = useRef<Blob[]>([]);
    const messagesEndRef = useRef<HTMLDivElement | null>(null);
    const connectionRef = useRef<any>(null);
    const transcriptBuffer = useRef<string>("");

    useEffect(() => {
        scrollToBottom();
    }, [messages, currentStreamingMessage]);

    const {data} = useQuery(ParamAMPT("DEEPGRAM_APIKEY"));

    const startTranscription = async () => {
        if (_.isEmpty(data)) {
            showWarning("Transcription service is not available now, try again later");
            return;
        }

        const deepgram = createClient(data?.value);
        const recording = await prepareAudioRecording();
        if (!recording) return;

        const {mediaRecorder} = recording;
        const connection = deepgram.listen.live({
            model: "nova-3",
            language: "en-US",
            smart_format: true,
            interim_results: true,
            utterance_end_ms: 1000,
        });

        connectionRef.current = connection;

        connection.on(LiveTranscriptionEvents.Open, () => {
            mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    connection.send(event.data);
                }
            };
            mediaRecorder.start(250);

            mediaRecorder.onerror = (event) => {
                console.error('MediaRecorder error:', event);
            };

            connection.on(LiveTranscriptionEvents.Transcript, (data) => {
                const isFinal = data.is_final;
                const transcript = data.channel.alternatives[0]?.transcript || "";

                if (transcript && isFinal) {
                    transcriptBuffer.current += transcript + " ";
                }
            });

            connection.on(LiveTranscriptionEvents.UtteranceEnd, () => {
                if (transcriptBuffer.current.trim()) {
                    sendMessage(transcriptBuffer.current.trim());
                    transcriptBuffer.current = "";
                }
            });

            connection.on(LiveTranscriptionEvents.Error, (err) => {
                console.error("Connection error:", err);
            });

            connection.on(LiveTranscriptionEvents.Close, () => {
                if (transcriptBuffer.current.trim()) {
                    sendMessage(transcriptBuffer.current.trim());
                    transcriptBuffer.current = "";
                }
            });

            setIsRecording(true);
            mediaRecorderRef.current = mediaRecorder;
        });
    };

    const prepareAudioRecording = async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {echoCancellation: true},
            });

            const mimeType = MediaRecorder.isTypeSupported("audio/webm")
                ? "audio/webm"
                : MediaRecorder.isTypeSupported("audio/mp4")
                    ? "audio/mp4"
                    : MediaRecorder.isTypeSupported("audio/ogg")
                        ? "audio/ogg"
                        : "";

            if (!mimeType) {
                showWarning("Your browser does not support any supported audio format.");
                return null;
            }

            const mediaRecorder = new MediaRecorder(stream, {mimeType});
            return {mediaRecorder, stream};
        } catch (error) {
            console.error("Error preparing audio recording:", error);
            showWarning("It was not possible to access the microphone. Check the permissions and try again.");
            return null;
        }
    };


    const startRecording = async () => {
        const stream = await navigator.mediaDevices.getUserMedia({audio: true});
        const mediaRecorder = new MediaRecorder(stream, {mimeType: "audio/webm"});
        mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                audioChunksRef.current.push(event.data);
            }
        };
        mediaRecorder.onstop = async () => {
            const audioBlob = new Blob(audioChunksRef.current, {type: "audio/webm"});
            audioChunksRef.current = [];
            const formData = new FormData();
            formData.append("audio", audioBlob);
            try {
                const response = await fetch("/api/deepgram/transcribe", {
                    method: "POST",
                    body: audioBlob,
                });
                if (!response.ok) {
                    const {error} = await response.json();
                    showError(error ?? "Something went wrong");
                    return null;
                }
                const data = await response.json();
                if (data.text) {
                    setInputMessage(data.text);
                }
            } catch (error) {
                console.error("Error transcribing audio:", error);
            }
        };
        mediaRecorder.start();
        setTimeout(() => {
            mediaRecorder.stop();
        }, 5000);
        setIsRecording(true);
        mediaRecorderRef.current = mediaRecorder;
    };

    const stopRecording = () => {
        mediaRecorderRef.current?.stop();
        connectionRef.current?.finish();
        setIsRecording(false);
    };

    const sendMessage = (message: string) => {
        if (!message.trim()) return;
        handleSubmit(message);
    };

    const scrollToBottom = () => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({behavior: "smooth"});
        }
    };

    return (
        <Box sx={{position: "relative"}}>
            <Sheet
                sx={{
                    height: 700,
                    margin: "auto",
                    display: "flex",
                    flexDirection: "column",
                    borderRadius: "md",
                    boxShadow: "lg",
                    p: 2,
                    gap: 2,
                    bgcolor: "background.body",
                }}
            >
                <Box
                    sx={{
                        flexGrow: 1,
                        overflowY: "auto",
                        display: "flex",
                        flexDirection: "column",
                        gap: 3,
                        padding: "1rem",
                        borderRadius: "sm",
                        border: "1px solid",
                        borderColor: "neutral.outlinedBorder",
                        bgcolor: "#fafafa",
                    }}
                >
                    {messages.map(
                        (message: any, index: number) =>
                            message.role !== "system" && (
                                <Box
                                    key={`${message.id}-${index}`}
                                    sx={{
                                        alignSelf: message.role === "user" ? "flex-end" : "flex-start",
                                        maxWidth: "80%",
                                        bgcolor: message.role === "user" ? "primary.softBg" : "neutral.softBg",
                                        color: message.role === "user" ? "primary.softColor" : "text.primary",
                                        borderRadius: "md",
                                        px: 2,
                                        py: 1,
                                        boxShadow: "sm",
                                    }}
                                >
                                    <Typography level="body-md" fontWeight="bold">
                                        {message.role}:
                                    </Typography>
                                    <Typography level="body-md">{message.content}</Typography>
                                </Box>
                            )
                    )}
                    {currentStreamingMessage && (
                        <Box
                            sx={{
                                alignSelf: "flex-start",
                                maxWidth: "80%",
                                bgcolor: "neutral.softBg",
                                color: "text.secondary",
                                borderRadius: "md",
                                px: 2,
                                py: 1,
                                boxShadow: "sm",
                            }}
                        >
                            <Typography level="body-md" fontWeight="bold">
                                assistant:
                            </Typography>
                            <Typography level="body-md">{currentStreamingMessage}</Typography>
                        </Box>
                    )}
                    <div ref={messagesEndRef}></div>
                </Box>
                <Box
                    sx={{
                        display: "flex",
                        gap: 1,
                        alignItems: "center",
                        justifyContent: "center",
                        mt: "auto",
                        position: "relative",
                    }}
                >
                    {isLoading ? (
                        <IconButton>
                            <CircularProgress variant="plain" sx={{mr: 1}}/>
                            Writing...
                        </IconButton>
                    ) : (
                        <>
                            <Input
                                autoFocus
                                disabled={isRecording || isCallEnd}
                                placeholder="Type your message..."
                                value={inputMessage}
                                onChange={(e) => setInputMessage(e.target.value)}
                                onKeyDown={(e) => {
                                    if (e.key === "Enter") {
                                        e.preventDefault();
                                        handleSubmit(inputMessage);
                                        setInputMessage("");
                                    }
                                }}
                                sx={{flexGrow: 1}}
                            />
                            <IconButton
                                onClick={isRecording ? stopRecording : startTranscription}
                                color="neutral"
                                disabled={isCallEnd}
                            >
                                {isRecording ? <StopIcon/> : <MicIcon/>}
                            </IconButton>
                            <Button
                                variant="solid"
                                color="neutral"
                                onClick={() => {
                                    handleSubmit(inputMessage);
                                    setInputMessage("");
                                }}
                                disabled={isLoading || isRecording || isCallEnd}
                                sx={{flexShrink: 0}}
                            >
                                Send
                            </Button>
                        </>
                    )}
                </Box>
            </Sheet>
        </Box>
    );
};
