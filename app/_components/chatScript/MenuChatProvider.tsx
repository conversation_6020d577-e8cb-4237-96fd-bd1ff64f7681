import React from "react";
import {<PERSON>, Button} from "@mui/joy";
import _ from "lodash";
import {useQuery} from "@tanstack/react-query";
import {tryit} from "radash";
import {
    chatScriptStarted,
    setChatScriptStarted,
    setDataExtractedChatScript,
    setViewExtractedChatScript
} from "@/app/_components/table/states/nodeSriptsStates";
import {useEntity} from "simpler-state";

export const MenuChatProvider: React.FC<any> = ({
                                                    restartConversation,
                                                    callCampaignId,
                                                    sessionData,
                                                    enableExtraction,
                                                    dataQuest,
                                                    openModalContent
                                                }) => {
    const chatStarted = useEntity(chatScriptStarted)

    const buttonControlChat = () => {
        setChatScriptStarted(false)
        if (restartConversation) restartConversation(true)
    }

    const getDataExtraction = async () => {
        const [extractionError, extractionResponse] = await tryit(async () =>
            fetch("/api/vectra/dataExtraction", {
                method: "POST",
                headers: {"Content-Type": "application/json"},
                body: JSON.stringify({
                    campaignId: callCampaignId,
                    transcript: sessionData?.messages,
                    metadata: sessionData?.client,
                    finalIntent: dataQuest?.newIntent
                }),
            })
        )();
        if (extractionError) {
            console.error(extractionError);
            return null
        } else {
            const {response} = await extractionResponse.json();
            setDataExtractedChatScript(response)
            if (response) {
                setViewExtractedChatScript(true)
            }
            return response
        }
    }

    const {data, isFetching, refetch} = useQuery({
        queryKey: ["script-data-extraction"],
        queryFn: getDataExtraction,
        refetchOnWindowFocus: false,
        enabled: !!enableExtraction
    });

    return (
        <Box sx={{display: "flex", alignItems: "center", padding: "0.5rem 1rem"}}>
            {chatStarted &&
                <Button variant="solid" color="neutral" onClick={() => buttonControlChat()}>
                    Restart chat
                </Button>}
            <Button
                variant="solid" color="neutral" sx={{m: (chatStarted) ? "0 1rem" : ""}}
                onClick={() => openModalContent("client")}>
                Client info
            </Button>
            <Box sx={{flexGrow: 1}}/>
        </Box>
    )
}
