import "./globals.css";
import type {Metadata} from "next";
import {Inter} from "next/font/google";
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';
import TanstackProvider from "@/app/_components/providers/TanstackProvider";
import React from "react";
import {AuthGuardProvider} from "@/app/_components/providers/AuthGuardProvider";

const inter = Inter({subsets: ["latin"]});

export const metadata: Metadata = {
    title: "AurionX",
    icons: {
        icon: [
            {url: "/favicon-32x32.png", sizes: "32x32", type: "image/png"},
            {url: "/favicon-16x16.png", sizes: "16x16", type: "image/png"},
        ],
        apple: {
            url: "/apple-touch-icon.png",
            sizes: "180x180",
            type: "image/png"
        },
        other: [
            {
                rel: 'mask-icon',
                url: '/android-chrome-192x192.png',
                sizes: '192x192'
            },
            {
                rel: 'mask-icon',
                url: '/android-chrome-512x512.png',
                sizes: '512x512'
            }
        ]
    },
    manifest: "/site.webmanifest",
};

export default function RootLayout({children}: { children: React.ReactNode }) {
    return (
        <html lang="en" style={{width: "100%", height: "100%"}}>
        <head>
            <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png"/>
            <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png"/>
            <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png"/>
            <link rel="manifest" href="/site.webmanifest"/>
            <title>AurionX</title>
        </head>
        <body style={{width: "100%", height: "100%", margin: "0", overflowX: "hidden"}} className={inter.className}>
        <TanstackProvider>
            <AuthGuardProvider>
                {children}
            </AuthGuardProvider>
        </TanstackProvider>
        </body>
        </html>
    );
}

