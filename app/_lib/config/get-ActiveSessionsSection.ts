import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { authClient } from "@/app/_lib/auth/auth-client";

type Session = {
  id: string;
  token: string;
  userAgent: string;
  createdAt: string;
  expiresAt: string;
};

// Función para obtener las sesiones del usuario usando authClient
export async function getUserSessions(): Promise<Session[]> {
  try {
    const { data, error } = await authClient.listSessions();
    if (error) {
      console.error("Failed to fetch sessions:", error);
      throw error;
    }

    // Transformar las sesiones al formato esperado
    return (data || []).map((session: any) => ({
      id: session.id,
      token: session.sessionToken || session.token,
      userAgent: session.userAgent || "Unknown Browser",
      createdAt: new Date(session.createdAt).toISOString(),
      expiresAt: new Date(session.expires || session.expiresAt).toISOString(),
    }));
  } catch (error) {
    console.error("Error obteniendo sesiones:", error);
    return [];
  }
}

// Configuración de React Query para sesiones
export const sessionsQueryKey = ["get_sessions"];

export const useSessionsQuery = () => {
  return useQuery({
    queryKey: sessionsQueryKey,
    queryFn: getUserSessions,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
  });
};

// Mutaciones para sesiones usando authClient
export const useRevokeSessionMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (token: string) => {
      const { error } = await authClient.revokeSession({ token });
      if (error) {
        throw new Error(`Error revocando sesión: ${error.message}`);
      }
      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: sessionsQueryKey });
    },
    onError: (error) => {
      console.error("Error revocando sesión:", error);
    },
  });
};

export const useRevokeAllSessionsMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const { error } = await authClient.revokeOtherSessions();
      if (error) {
        throw new Error(`Error revocando todas las sesiones: ${error.message}`);
      }
      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: sessionsQueryKey });
    },
    onError: (error) => {
      console.error("Error revocando todas las sesiones:", error);
    },
  });
};
