export const getNumberMonths = async () => {
    return [
        {type: 'number', enum: [1], title: 'January'},
        {type: 'number', enum: [2], title: 'February'},
        {type: 'number', enum: [3], title: 'March'},
        {type: 'number', enum: [4], title: 'April'},
        {type: 'number', enum: [5], title: 'May'},
        {type: 'number', enum: [6], title: 'June'},
        {type: 'number', enum: [7], title: 'July'},
        {type: 'number', enum: [8], title: 'August'},
        {type: 'number', enum: [9], title: 'September'},
        {type: 'number', enum: [10], title: 'October'},
        {type: 'number', enum: [11], title: 'November'},
        {type: 'number', enum: [12], title: 'December'},
    ]
}
