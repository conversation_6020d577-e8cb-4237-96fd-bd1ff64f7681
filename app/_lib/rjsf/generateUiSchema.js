import {getTags} from "@/app/_lib/rjsf/getTags.js";

export const generateUiSchema = async (fields, fullTableConfiguration) => {
    console.log('fields: ', fields)
    const uiSchema = {
        "ui:submitButtonOptions": {
            "submitText": "Update",
            "norender": false,
            "props": {
                "type": "primary",
            },
        },
        transferred_to: {
            items: {
                tags: {
                    "ui:widget": "MultiSelectWidget",
                    "ui:options": {
                        tagsData: await getTags()
                    }
                },
                "ui:title": ""
            },
            "type": "select",
            "extractValueType": "method",
            "extractValueMethod": "getTags",
        }
    };

    fields.forEach(field => {
        if (field === "number" || field === "client") {
            uiSchema[field] = {
                "ui:widget": "textarea"
            };
        }
    });

    return uiSchema;
};

