export const buildUiSchema = async (uiSchema, fields) => {
    // const uiSchemaPromises = _.map(uiSchema, (value, key) => {
    //     console.log('key: ',key)
    //     console.log('value: ',value)
    // });
    // await Promise.all(uiSchemaPromises);
    // return uiSchema;
    return {
        "ui:submitButtonOptions": {
            "submitText": "Update",
            "norender": false,
            "props": {
                "type": "primary",
            },
        },
        transferred_to: {
            items: {
                tags: {
                    "ui:widget": "MultiSelectWidget",
                    "ui:options": {
                        tagsData: tagsData
                    }
                },
                "ui:title": ""
            },
            "type": "select",
            "extractValueType": "method",
            "extractValueMethod": "getTags",
        }
    };
    fields.forEach(field => {
        if (field === "number" || field === "client") {
            uiSchema[field] = {
                "ui:widget": "textarea"
            };
        }
    });
    return uiSchema;

}
