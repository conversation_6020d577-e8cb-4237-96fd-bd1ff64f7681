'use client'
import {entity, persistence} from 'simpler-state';

const setPersistence = () => {
    if (typeof window !== 'undefined') {
        return [persistence(`accounting-invoices`)];
    }
    return [];
}
export const accountingInvoiceIds = entity([], setPersistence());
export const accountingInvoiceLabels = entity('', setPersistence());

export const setAccountingInvoiceIds = (invoiceIds) => {
    accountingInvoiceIds.set(invoiceIds);
}

export const setAccountingInvoiceLabels = (label) => {
    accountingInvoiceLabels.set(label);
}



