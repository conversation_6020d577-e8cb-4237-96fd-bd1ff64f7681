import supabase from "@/utils/supabase";
import {DateTime} from "luxon";
import {buildTableFilters} from "@/app/_lib/supabase/utils/buildTableFilters";

export const applyNewRvnExp = async (filters: any,
                                     amount: number,
                                     clientAmount: number,
                                     amountPerEvent: number,
                                     totalEvents: number,
                                     label: string) => {
    const utcDateTime = DateTime.utc().toFormat('yyyy-MM-dd HH:mm')
    const revInsertResult = await supabase
        .from('rvn_exp')
        .insert(
            {
                cf_amount: amount,
                client_amount: clientAmount,
                applied: true,
                applied_date: utcDateTime,
                label,
                total_events: totalEvents,
                amount_by_event: amountPerEvent
            }
        ).select("id").single();
    const rvnExpId = revInsertResult?.data?.id ? revInsertResult.data.id : '';
    if (rvnExpId && rvnExpId != '') {
        let updateAccountingQuery = supabase
            .from('accounting').update({rvn_exp_id: rvnExpId, amount: amountPerEvent});
        updateAccountingQuery = buildTableFilters(filters, updateAccountingQuery);
        updateAccountingQuery.filter('rvn_exp_id', 'is', null);
        const {error} = await updateAccountingQuery;
        console.log('error: ', error)
        if (error) {
            throw error
        }
    }
    return revInsertResult;
}
