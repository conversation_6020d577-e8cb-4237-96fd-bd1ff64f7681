import supabase from "@/utils/supabase";
import {buildTableFilters} from "@/app/_lib/supabase/utils/buildTableFilters";

export async function eventsWithoutRvn(tableName: string, filters: any) {
    let supabaseQuery = supabase
        .from(tableName)
        .select('id', {count: 'exact', head: true})
        .filter('rvn_exp_id', 'is', null);
    supabaseQuery = buildTableFilters(filters, supabaseQuery);
    const {error, count, data} = await supabaseQuery;
    return count;
}
