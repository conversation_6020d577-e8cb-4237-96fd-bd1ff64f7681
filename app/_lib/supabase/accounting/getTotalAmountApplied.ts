import supabase from "@/utils/supabase";
import _ from "lodash";
import {buildPrimitiveFilters} from "@/app/_lib/supabase/utils/buildPrimitiveFilters";


export async function getTotalAccountingAmount(columnAmountName: string, tableName: string, filters: any) {
    const totalAccountingAmountFilters = {
        column_name: columnAmountName,
        table_name: tableName,
        filters: []
    }
    const filtersFunctions = buildPrimitiveFilters(filters)
    _.set(totalAccountingAmountFilters, 'filters', filtersFunctions);
    const {data, error} = await supabase.rpc('dynamic_filter_sum', totalAccountingAmountFilters);
    return data ? data : 0;
}

