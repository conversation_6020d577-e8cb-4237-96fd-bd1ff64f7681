import supabase from "@/utils/supabase";
import _ from "lodash";
import {buildPrimitiveFilters} from "@/app/_lib/supabase/utils/buildPrimitiveFilters";


export async function getCountTable(tableName: string, filters: any) {
    const countFilters = {
        table_name: tableName,
        filters: []
    }
    const filtersFunctions = buildPrimitiveFilters(filters)
    _.set(countFilters, 'filters', filtersFunctions);
    const {data, error} = await supabase.rpc('get_count', countFilters);
    return data ? data : 0;
}

