import {useEffect} from "react";
import supabase from "@/utils/supabase";

export const useSupabaseChannel = (eventName: string, eventType: any, dbTableName: string, onData: any) => {
    useEffect(() => {
        const channel = supabase
            .channel(eventName)
            .on('postgres_changes', {event: eventType, schema: 'public', table: dbTableName},
                (payload) => {
                    onData(payload);
                }
            )
            .subscribe();

        return () => {
            supabase.removeChannel(channel);
        };
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dbTableName, eventType, onData]);
};
