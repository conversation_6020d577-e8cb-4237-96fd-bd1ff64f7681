import supabase from "@/utils/supabase";
import {buildTableFilters} from "@/app/_lib/supabase/utils/buildTableFilters";
import _ from "lodash";
import {getCountTable} from "@/app/_lib/supabase/getCountTable";

export async function getSupabaseData(tableName: string, page: number, pageSize: number, filters: any, selectColumns: string = '*', action: string = 'NEXT', lastMaxOffset = 0, lastMinOffset = 0) {
    let supabaseQuery = supabase.from(tableName).select(selectColumns, {count: "planned"});
    supabaseQuery = buildTableFilters(filters, supabaseQuery);
    const countData = await getCountTable(tableName, filters);
    if (action === 'NEXT') {
        supabaseQuery.filter('cv_operation_id', 'gt', lastMaxOffset).order('cv_operation_id', {ascending: true})
    }
    if (action === 'PREVIOUS') {
        supabaseQuery.filter('cv_operation_id', 'lt', lastMinOffset).order('cv_operation_id', {ascending: false})
    }
    if (action === 'END') {
        const maxValueRequest = {
            column_name: 'cv_operation_id',
            table_name: tableName,
        }
        const {data: maxValue, error} = await supabase.rpc('get_max_value', maxValueRequest);
        supabaseQuery.filter('cv_operation_id', 'lte', maxValue).order('cv_operation_id', {ascending: false})
    }
    if (action === 'START') {
        supabaseQuery.filter('cv_operation_id', 'gt', 0).order('cv_operation_id', {ascending: true})
    }

    const {data, error} = await supabaseQuery.limit(pageSize);
    if (error) {
        console.error("Error fetching data:", error);
        return {data: [], hasNextPage: false};
    }
    let totalPages = 0;
    if (countData != null) {
        totalPages = countData / pageSize;
    }
    const orderedData = _.orderBy(data || [], ["cv_operation_id"], ["asc"]);
    const hasNextPage = data.length === pageSize && data.length > 0;
    return {data: orderedData, hasNextPage, count: countData, totalPages: Math.floor(totalPages + 1)};
}

