const operators: Record<string, string> = {
    gte: '>=',
    lte: '<=',
    eq: '=',
    ilike: 'like',
    between: 'between'
};

export const buildPrimitiveFilters = (filters: any) => {
    const filtersFunctions = [];
    for (const key in filters) {
        if (Object.hasOwnProperty.call(filters, key)) {
            const column = key.replaceAll(`-${filters[key].filterType}`, '');
            if (filters[key].value != 'NA_FILTER') {
                const operator = operators[filters[key].filterType]
                const value = filters[key].value
                if (operator === 'between') {
                    if (!value.start.includes('NA_FILTER') && !value.end.includes('NA_FILTER')) {
                        filtersFunctions.push({column, operator: '>=', value: value.start, type: 'text'})
                        filtersFunctions.push({column, operator: '<=', value: value.end, type: 'text'})
                    }
                } else {
                    filtersFunctions.push({column, operator, value, type: 'text'})
                }
            }
        }
    }
    return filtersFunctions;
}
