import {applyBetweenFilter} from "@/app/_lib/supabase/utils/applyBetweenFilter";
import {applyNormalFilter} from "@/app/_lib/supabase/utils/applyNormalFilter";
import {applyNotFilter} from "@/app/_lib/supabase/utils/applyNotFilter";

export const buildTableFilters = (filters: Record<string, any>, supabaseQuery: any): any => {
    for (const [key, value] of Object.entries(filters)) {
        const {filterType, value: filterValue} = value;
        if (filterValue !== 'NA_FILTER') {
            const column = key.replace(`-${filterType}`, '');
            if (filterType === 'between') {
                applyBetweenFilter(supabaseQuery, column, filterValue);
            } else if (filterType === 'not') {
                applyNotFilter(supabaseQuery, column, value.notFilter, filterValue);
            } else {
                applyNormalFilter(supabaseQuery, column, filterType, filterValue);
            }
        }
    }
    return supabaseQuery;
};
