import _ from "lodash";
import {getNovaItems} from "@conversionfinder/conversion-finder-utils";
import * as mapperMethods from "../functions/utils/mappers/index.js"
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";
import {params} from "@ampt/sdk";
import {convertDateStringsToObjects} from "@/app/_lib/utils/convertDateStringsToObjects.js";
import {buildFilters} from "@/app/_lib/nova/functions/utils/buildFilters.js";


export default async function getItems(payload) {
    const utilsUri = params('UTILS_URI');
    const {
        filters,
        options = {limit: 10, skip: 0},
        collection,
        query,
        context = {timezone: "America/Chicago"},
        dbName = "conversion_finder",
        mongoFilters
    } = payload;
    if (!options.sort) options.sort = {created_at: 1};
    try {
        if (!query) {
            console.error("Query not found")
            return {error: 'Query not found', status: 400}
        }
        const mongoFiltersWithDates = mongoFilters && !_.isEmpty(mongoFilters) ? convertDateStringsToObjects(mongoFilters) : buildFilters(filters, context);
        const newOpts = (_.has(mongoFiltersWithDates, '$text')) ? {
            limit: options?.limit,
            skip: options?.skip
        } : options
        const payload = {
            filters: mongoFiltersWithDates,
            options: newOpts,
            collection,
            query,
            context,
            dbName,
            utilsUri
        }
        const response = await getNovaItems(payload)
        const tableConfiguration = await getTableConfiguration(collection, context, utilsUri);
        let items = await executeMapperMethods(response.response, collection, context, tableConfiguration);
        if (tableConfiguration[0]?.lock_on_modify && tableConfiguration[0]?.lock_on_modify === true) {
            items = await getLockRows(collection, items);
        }
        response.response = items;
        return response;
    } catch (error) {
        console.error('Error to get data')
        console.error(error.message)
        return {error: 'Error to get data', status: 500}
    }
}

const getLockRows = async (collection, items) => {
    const {db} = await adminConnect();
    const locksIds = await db.collection('update_collection_lock').find({collection}).map(updateLock => {
        return updateLock.source_id;
    }).toArray()
    return items.map(item => ({
        ...item,
        lock: locksIds.includes(item._id) ? true : item.lock
    }));

}

const getTableConfiguration = async (collection, context, utilsUri) => {
    const {response: tableConfiguration} = await getNovaItems({
        filters: {table_name: collection},
        collection: 'tables_configuration',
        query: {mapperDataMethods: 1, lock_on_modify: 1},
        context,
        dbName: "conversion_finder",
        utilsUri
    });
    return tableConfiguration;
}

const executeMapperMethods = async (items, collection, context, tableConfiguration) => {
    const mapperDataMethods = tableConfiguration && tableConfiguration.length > 0 ? tableConfiguration[0]?.mapperDataMethods : [];
    if (mapperDataMethods && mapperDataMethods.length > 0) {
        for (let method of mapperDataMethods) {
            const mapperFunc = mapperMethods[method];
            if (mapperFunc) {
                items = await mapperFunc(items)
            }
        }
    }
    return items;
}
