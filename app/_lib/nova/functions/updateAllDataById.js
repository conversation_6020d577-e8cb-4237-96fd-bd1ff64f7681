import * as beforeSubmitMethodsList from "../../utils/update/beforeSubmitMethods/index.js";
import _ from "lodash";
import {verifyIfItemIsNotLock} from "@/app/_lib/nova/functions/utils/verifyIfItemIsNotLock.js";
import {lockOnModify} from "@/app/_lib/nova/functions/utils/lockOnModify.js";
import {sendTaskToQueue} from "@/app/_lib/nova/functions/utils/sendTaskToQueue.js";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export default async function updateAllDataById(payload) {
    const {id, data, collection} = payload;
    const {db} = await adminConnect();
    if (!collection) {
        console.error("collection not found");
        return;
    }
    let {id: dataId, ...dataWithoutId} = data;
    const dateFields = ['date_created', 'completed_date', 'created_at', 'date_retained'];
    dateFields.forEach(field => {
        if (dataWithoutId[field]) {
            dataWithoutId[field] = new Date(dataWithoutId[field]);
        }
    });
    await verifyIfItemIsNotLock(id, collection)
    const tableConfiguration = await db.collection('tables_configuration').findOne({table_name: collection});
    const {beforeSubmitMethods, tasksAfterSubmit, lock_on_modify} = tableConfiguration;
    dataWithoutId = executeBeforeMethods(dataWithoutId, beforeSubmitMethods);
    const {modifiedFields, beforeUpdate, afterUpdate} = await update(id, collection, dataWithoutId);
    payload = {...payload, modifiedFields, beforeUpdate, afterUpdate}
    await lockOnModify(lock_on_modify, collection, id)
    await sendTaskToQueue(tasksAfterSubmit, payload, modifiedFields);
    return {message: "Data updated successfully"};
}


const executeBeforeMethods = (payload, beforeSubmitMethods) => {
    if (beforeSubmitMethods && beforeSubmitMethods.length > 0) {
        for (let beforeSubmitMethod of beforeSubmitMethods) {
            const func = beforeSubmitMethodsList[beforeSubmitMethod];
            if (func) {
                payload = func(payload);
            }
        }
    }
    return payload;
}

const update = async (id, collection, dataWithoutId) => {
    // TODO: check for divide the process for update data based on DB
    const {db} = await adminConnect();
    const beforeUpdate = await db.collection(collection).findOne({_id: id});
    const afterUpdate = await db.collection(collection).findOneAndUpdate(
        {_id: id},
        {$set: dataWithoutId},
        {returnDocument: "after"}
    );
    const modifiedFields = _.keys(dataWithoutId).filter(
        key => !_.isEqual(_.get(beforeUpdate, key), _.get(afterUpdate.value, key))
    );
    return {beforeUpdate, afterUpdate: afterUpdate.value, modifiedFields}

}
