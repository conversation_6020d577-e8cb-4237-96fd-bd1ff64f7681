import * as updateMethods from "../../rjsf/index.js";
import _ from "lodash";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

const {db} = await adminConnect();
export const getUpdateConfigurations = async (payload) => {
    const {jsonKeys, formData} = payload;
    const tableConfiguration = await db.collection("tables_configuration")
        .findOne({table_name: payload.collection})
    const {ui_schema, rjs_schema, show_help_data} = tableConfiguration;
    const {form_path, key_path, values} = show_help_data ?? {}
    const uiSchema = await generateUiSchema(jsonKeys, ui_schema, formData)
    const rjsSchema = await buildDynamicValues(rjs_schema, formData)
    const showHelpData = await getAdditionalData(values, formData);
    const getPathRjs = _.get(rjsSchema, form_path);
    if (getPathRjs && showHelpData && showHelpData.length > 0) {
        _.set(rjsSchema, `${form_path}.${key_path}`, formatAdditionalData(showHelpData));
    }
    return {uiSchema, rjsSchema, showHelpData};
}

const generateUiSchema = async (fields, uiSchema, formData) => {
    const uiSchemaResult = await buildDynamicValues(uiSchema, formData);
    fields.forEach(field => {
        if (field === "number" || field === "client") {
            uiSchemaResult[field] = {
                "ui:widget": "textarea"
            };
        }
    });
    return uiSchemaResult;
}

const buildDynamicValues = async (json, formData) => {
    async function traverse(obj, currentPath = []) {
        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }
        let modified = Array.isArray(obj) ? [...obj] : {...obj};
        const promises = Object.entries(obj).map(async ([key, value]) => {
            if (value && typeof value === 'object') {
                if (value.extractValueType === 'method' && value['extractPropertyReplace']) {
                    const extractValueMethod = value['extractValueMethod'];
                    const extractPropertyReplace = value['extractPropertyReplace'];
                    const func = updateMethods[extractValueMethod];
                    const methodResult = func ? await func(formData) : [];
                    modified[key] = {
                        ...value,
                        [extractPropertyReplace]: methodResult
                    };
                } else {
                    modified[key] = await traverse(value, [...currentPath, key]);
                }
            }
        });
        await Promise.all(promises);
        return modified;
    }

    return await traverse(json);
}

const getAdditionalData = async (valuesHelpData, formData) => {
    if (valuesHelpData && valuesHelpData.length > 0) {
        let returnData = null;
        for (const helpData of valuesHelpData) {
            const getData = await getDBDataProcess(helpData, formData);
            if (!_.isEmpty(getData)) returnData = getData
        }
        return returnData;
    }
}

const getDBDataProcess = async (config, allData) => {
    let finalArr = [];
    if (config) {
        const {external, local_key, foreign_key, get_data, data_key, name} = config;
        if (external) {
            const keyExists = Object.prototype.hasOwnProperty.call(allData, local_key);
            const localKey = allData[local_key] || null;
            const getExternalData = (localKey)
                ? await db.collection(external).findOne({[foreign_key]: localKey})
                : null;

            if (getExternalData && get_data && get_data.length > 0) {
                for (const gData of get_data) {
                    const nestedData = await getDBDataProcess(gData, getExternalData);
                    finalArr.push(...nestedData);
                }
            } else if (getExternalData && !get_data && data_key) {
                finalArr.push({[name]: getExternalData[data_key] || "N/A"});
            } else if (keyExists && name) {
                finalArr.push({[name]: "N/A"});
            }
        }
    }
    return finalArr;
}

const formatAdditionalData = (dataArray) => {
    if (_.isEmpty(dataArray)) return "";
    return dataArray
        .map(obj => {
            return _.map(obj, (value, key) => `${key}: ${value}`).join(", ");
        })
        .join(" | ");
};

