import {verifyIfItemIsNotLock} from "@/app/_lib/nova/functions/utils/verifyIfItemIsNotLock.js";
import {lockOnModify} from "@/app/_lib/nova/functions/utils/lockOnModify.js";
import {sendTaskToQueue} from "@/app/_lib/nova/functions/utils/sendTaskToQueue.js";
import {getMongoDB} from "@/app/_lib/mongo/data/getMongoDB.js";

export const deleteRow = async (payload) => {
    console.log(payload);
    const {row, tasksAfterSubmit, collection, lock_on_modify} = payload
    const rowId = row._id;
    await verifyIfItemIsNotLock(rowId, collection);
    await lockOnModify(lock_on_modify, collection, rowId);
    console.log(`Deleting item ${rowId} from ${collection}`);
    await deleteRowFromCollection(rowId, collection);
    console.log(`Sending ${tasksAfterSubmit.length} queues.`)
    await sendTaskToQueue(tasksAfterSubmit, payload)
    return {response: {}, status: 200}
}

const deleteRowFromCollection = async (id, collection) => {
    const {db} = await getMongoDB();
    await db.collection(collection).deleteOne({_id: id});
}
