import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export async function getConfNameFromRoute(route) {
    const {db} = await adminConnect();
    const getConfData = await db.collection('configurations').findOne({name: 'menu'});
    const menus = getConfData?.configuration;

    if (!menus) return null;
    for (const menu of menus) {
        if (menu?.route === route) return menu?.confName || null;

        if (Array.isArray(menu?.subMenus)) {
            const sub = menu?.subMenus.find(sub => sub.route === route);
            if (sub) return sub.confName || null;
        }
    }

    return null;
}
