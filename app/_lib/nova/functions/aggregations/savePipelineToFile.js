'use server'
import fs from 'fs';
import path from 'path';

export const savePipelineToFile = async (pipeline, name = 'pipeline') => {
    try {
        const baseDir = path.join(process.cwd(), 'pipeline_exports');
        if (!fs.existsSync(baseDir)) {
            fs.mkdirSync(baseDir, {recursive: true});
        }
        const filePath = path.join(baseDir, `${name}_${Date.now()}.json`);
        fs.writeFileSync(filePath, JSON.stringify(pipeline, null, 2), 'utf8');
        console.log("Pipeline saved to file:", filePath);
        return {success: true, filePath};
    } catch (error) {
        console.error("Error saving pipeline to file:", error);
        return {success: false, error: error.message};
    }
}
