'use server'
import _ from "lodash";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";
import {savePipelineToFile} from "@/app/_lib/nova/functions/aggregations/savePipelineToFile.js";

const saveFileAggregation = false;

export const aggregationSummaryReports = async (collection, filters, firstData, secondData, keySort) => {
    const {db} = await adminConnect();
    const pipeline = [{$match: filters}]
    if (!_.isEmpty(firstData?.project0)) pipeline.push({$project: firstData?.project0})
    if (!_.isEmpty(firstData?.group)) pipeline.push({$group: firstData?.group})
    if (!_.isEmpty(firstData?.lookup)) pipeline.push({$lookup: firstData?.lookup})
    if (!_.isEmpty(firstData?.unwind)) pipeline.push({$unwind: firstData?.unwind})
    if (!_.isEmpty(firstData?.project)) pipeline.push({$project: firstData?.project})
    if (!_.isEmpty(secondData)) {
        if (!_.isEmpty(secondData?.unwind2)) pipeline.push({$unwind: secondData?.unwind2})
        if (!_.isEmpty(secondData?.group2)) pipeline.push({$group: secondData?.group2})
        if (!_.isEmpty(secondData?.lookup2)) pipeline.push({$lookup: secondData?.lookup2})
        if (!_.isEmpty(secondData?.unwind3)) pipeline.push({$unwind: secondData?.unwind3})
        if (!_.isEmpty(secondData?.group3)) pipeline.push({$group: secondData?.group3})
        if (!_.isEmpty(secondData?.unwind4)) pipeline.push({$unwind: secondData?.unwind4})
        if (!_.isEmpty(secondData?.unwind5)) pipeline.push({$unwind: secondData?.unwind5})
        if (!_.isEmpty(secondData?.lookup3)) pipeline.push({$lookup: secondData?.lookup3})
        if (!_.isEmpty(secondData?.unwind6)) pipeline.push({$unwind: secondData?.unwind6})
        if (!_.isEmpty(secondData?.group4)) pipeline.push({$group: secondData?.group4})
        if (!_.isEmpty(secondData?.group5)) pipeline.push({$group: secondData?.group5})
        if (!_.isEmpty(secondData?.group6)) pipeline.push({$group: secondData?.group6})
        if (!_.isEmpty(secondData?.project2)) pipeline.push({$project: secondData?.project2})
    }

    pipeline.push({$sort: {[keySort]: 1}});
    // if (saveFileAggregation) {
    //     await savePipelineToFile(pipeline, `${collection}_pipeline`);
    // }
    let opts = {allowDiskUse: true};
    return await db.collection(collection).aggregate(pipeline, opts).toArray();
};
