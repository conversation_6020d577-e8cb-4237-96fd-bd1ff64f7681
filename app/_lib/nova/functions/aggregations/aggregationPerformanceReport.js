'use server'

import _ from "lodash";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";
import {savePipelineToFile} from "@/app/_lib/nova/functions/aggregations/savePipelineToFile.js";

const saveFileAggregation = false;
export const aggregationPerformanceReport = async (collectionName, aggregation, hint) => {
    const {db} = await adminConnect();
    let opts = {allowDiskUse: true}
    if (!_.isEmpty(hint)) opts.hint = hint
    // if (saveFileAggregation) {
    //     await savePipelineToFile(aggregation, `${collectionName}_pipeline`);
    // }
    return await db.collection(collectionName).aggregate(aggregation, opts).toArray();
}
