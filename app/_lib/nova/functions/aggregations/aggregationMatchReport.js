'use server'
import _ from "lodash";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";
import {savePipelineToFile} from "@/app/_lib/nova/functions/aggregations/savePipelineToFile.js";

const saveFileAggregation = false;
export const aggregationMatchReport = async (collection, dataAggregation, optionSelected) => {
    const {db} = await adminConnect();
    const pipeline = []
    if (!_.isEmpty(dataAggregation?.match)) pipeline.push({$match: dataAggregation?.match})
    if (!_.isEmpty(optionSelected?.project)) pipeline.push({$project: optionSelected?.project})
    if (!_.isEmpty(dataAggregation?.group)) pipeline.push({$group: dataAggregation?.group})
    if (!_.isEmpty(dataAggregation?.project)) pipeline.push({$project: dataAggregation?.project})
    if (!_.isEmpty(dataAggregation?.sort)) pipeline.push({$sort: dataAggregation?.sort})
    const opts = {allowDiskUse: true};
    // if (saveFileAggregation) {
    //     await savePipelineToFile(pipeline, `${collection}_pipeline`);
    // }
    return await db.collection(collection).aggregate(pipeline, opts).toArray();
}
