import _ from "lodash";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";
import {getConfiguration} from "@/app/_lib/utils/getConfiguration.js";

export async function getDataForAdminUsers({allData}) {
    try {
        const {db: dbConfig} = await adminConnect();
        const allCollObj = await dbConfig.collection('tables_configuration').find()
            .project({table_name: 1, configuration: 1, tools: 1}).sort({table_name: 1}).toArray();
        const allVendObj = await dbConfig.collection('vendors').find().sort({vendor: 1}).toArray();
        const allCliObj = await dbConfig.collection('clients').find().sort({client: 1}).toArray();
        const timezones = await dbConfig.collection('timezones').find().sort({abb: 1}).toArray();
        // TODO: fix roles for auth db
        // let roles = await dbConfig.collection('roles').find().sort({role: 1}).toArray();
        let roles = await dbConfig.collection('roles').find().sort({role: 1}).toArray();
        const menuConfiguration = await getConfiguration('menu');
        let noneRole = {}
        if (!allData) {
            noneRole = roles.find(rolInf => rolInf.role === 'none')
            roles = roles.filter(rolInf => rolInf.editable)
        }
        const collections = [{name: 'dashboard'}]
        const tabs = ["menus", "restrictions", "table_keys"]
        const keysCollections = {}
        allCollObj.forEach((item) => {
            const keysInfo = []
            if (!_.includes(item.table_name, "test")) {
                if (item.configuration && item.configuration.length > 0) {
                    item.configuration.forEach((confInf) => keysInfo.push({
                        show: confInf.header,
                        key: confInf.accessorKey
                    }));
                }
                keysCollections[item.table_name] = keysInfo
                const objCollPush = {name: item.table_name}
                if (!_.isEmpty(item?.tools)) {
                    const namesTools = []
                    for (const toolKey in item?.tools) {
                        if (item?.tools[toolKey]) {
                            namesTools.push(toolKey)
                        }
                    }
                    if (namesTools.length > 0) objCollPush.tools = namesTools
                }
                collections.push(objCollPush)
            }
        });
        const vendors = allVendObj.map((item) => ({
            id: item._id,
            name: item.vendor
        }));
        const clients = allCliObj.map((item) => ({
            id: item._id,
            name: item.client
        }));
        return {
            collections,
            menuConfiguration: menuConfiguration.configuration,
            keysCollections,
            vendors,
            clients,
            tabs,
            roles,
            timezones,
            noneRole
        };
    } catch (error) {
        console.error('Error get data for admin');
        console.error(error.message);
        return {error: error.message}
    }
}
