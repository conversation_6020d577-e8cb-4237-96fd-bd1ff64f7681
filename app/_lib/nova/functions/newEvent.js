import {buildFilters} from "@/app/_lib/nova/functions/utils/buildFilters";
import {generateId} from "@/app/_lib/utils/generateId";
import {DateTime} from "luxon";
import queueTasks from "../../../../lib/ampt/tasks/queueTasks.js";
import Decimal from "decimal.js";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export const newEvent = async (payload) => {
    const {db} = await adminConnect();
    console.log('Generating new event with payload: ', JSON.stringify(payload))
    let {
        filters,
        amount,
        sourceTable,
        event,
        amountPerEvent,
        totalEvents,
        label,
        eventAmount,
        user,
        userId,
        context
    } = payload
    const mongoFilters = buildFilters(filters, context);
    console.log('collection.find start ');
    let total = 0;
    let totalPerEvent = 0;
    if (eventAmount === 'all') {
        const amountDecimal = new Decimal(amount);
        const eventsDecimal = new Decimal(totalEvents)
        const result = amountDecimal.div(eventsDecimal).toDP(2);
        totalPerEvent = result.times(100).toNumber()
        total = result.mul(totalEvents).times(100).toNumber()
    } else if (eventAmount === 'perItem') {
        const amountDecimal = new Decimal(amount);
        const eventsDecimal = new Decimal(totalEvents)
        totalPerEvent = amountDecimal.times(100).toNumber()
        total = amountDecimal.mul(eventsDecimal).toDP(2).mul(100).toNumber();
    }
    const rvnExpId = generateId();
    const update = event === 'EXPENSE'
        ? {cost_accounting_id: rvnExpId, cost_accounting_done: false}
        : {price_accounting_id: rvnExpId, price_accounting_done: false}
    await db.collection(sourceTable).updateMany(mongoFilters, {$set: update});
    const utcDateTime = DateTime.utc()
    const rvnExpInsert = {
        _id: rvnExpId,
        total,
        amount_per_event: totalPerEvent,
        label,
        total_events: totalEvents,
        source_table: sourceTable,
        event,
        status_task: 'pending',
        date_created: utcDateTime,
        filters: mongoFilters,
        userId,
        user
    }
    console.log('updateMany end.. ')
    console.log('insertOne rvn_exp start...')
    await db.collection("rvn_exp").insertOne(rvnExpInsert)
    console.log('insertOne rvn_exp end...')
    const queueInsert = {
        _id: generateId(),
        payload: {rvnExpId},
        rvnExpId,
        status: 'pending',
        task: 'completeAccountingTask',
        created_at: utcDateTime
    }
    await db.collection("queues").insertOne(queueInsert)
    console.log('Executing queueTask')
    const queueTaskResult = await queueTasks.run()
    console.log('queue task result: ', queueTaskResult)
    return {rvnExpId};
}
