import {buildFilters} from "@/app/_lib/nova/functions/utils/buildFilters.js";
import _ from "lodash";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession.js";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export default async function getTotalEventsWithoutRvn(filters) {
    const {db} = await adminConnect()
    const user = await getUserSession()
    const {timezone} = user || {timezone: null};
    const getTimeZone = {timezone: timezone || "America/Chicago"}
    const mongoFilters = {
        $or: [
            {rvn_expense_id: {$exists: false}},
            {rvn_expense_id: null},
            {rvn_expense_id: ''}
        ]
    }
    const builtFilters = buildFilters(filters, getTimeZone)
    const mergedFilters = _.merge(mongoFilters, builtFilters);
    return await db.collection('accounting').countDocuments(mergedFilters);
}
