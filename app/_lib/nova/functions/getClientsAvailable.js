import _ from "lodash";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export async function getClientsAvailable(restrictions) {
    try {
        const {db} = await adminConnect()
        const {clients} = restrictions
        const queryFind = (!_.isEmpty(clients) && !_.includes(clients, "*")) ? {_id: {$in: clients}} : {};
        const allClients = await db.collection('clients').find(queryFind).sort({client: 1}).toArray();
        return {response: allClients}
    } catch (error) {
        console.error('Error get clients');
        console.error(error.message);
        return {response: []}
    }
}
