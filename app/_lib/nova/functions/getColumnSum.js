import {buildFilters} from "@/app/_lib/nova/functions/utils/buildFilters.js";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession.js";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export const getColumnSum = async (column, collection, filters = {}) => {
    const {db} = await adminConnect()
    const user = await getUserSession()
    const {timezone} = user || {timezone: null};
    const getTimeZone = {timezone: timezone || "America/Chicago"}
    const match = {$match: {}};
    const group = {
        $group: {
            _id: null,
            sum: {$sum: `$${column}`}
        }

    }
    const pipeline = [
        match,
        group
    ];
    pipeline[0]['$match'] = buildFilters(filters, getTimeZone)
    const result = await db.collection(collection).aggregate(pipeline).toArray();
    return result && result.length > 0 ? result[0].sum : 0;

}
