import {throwError} from "@/app/_lib/utils/throwError.js";
import {getMongoDB} from "@/app/_lib/mongo/data/getMongoDB.js";

export const generatePubId = async (payload) => {
    // TODO: change and check vendors for data DB or admin DB
    const {db} = await getMongoDB();
    const {vendorId, pubId, campaignKey, leadStrategy} = payload;
    if (!vendorId || !pubId || !campaignKey || !leadStrategy) {
        throwError(`Missing required fields: vendorId, pubId, campaignKey, leadStrategy`);
    }
    await db.collection('vendors').updateOne(
        {_id: vendorId},
        {
            $addToSet: {
                pub_ids: {
                    id: pubId,
                    leadStrategy: leadStrategy,
                    campaign_key: campaignKey
                }
            }
        }, {}
    );
}
