import {updateMetadataByUserId} from "@/app/_lib/nova/functions/better-auth/updateMetadataByUserId.js";
import {getUserMetadataById} from "@/app/_lib/nova/functions/better-auth/getUserMetadataById.js";

export async function updateChatData({chatData, userId}) {
    try {
        const getUser = await getUserMetadataById(userId);
        const {metadata} = getUser || {metadata: null};
        const jsonMetadata = metadata ? JSON.parse(metadata) : null;
        const {chat: oldChat = {}, ...restOfMetadata} = jsonMetadata;
        const newChat = {
            ...oldChat,
            ...chatData,
        };
        const newMetadata = {
            ...restOfMetadata,
            chat: newChat,
        };
        await updateMetadataByUserId(newMetadata, jsonMetadata, userId,)
        return {
            response: "Updated chat data",
            status: 200,
            newMetadata,
        };
    } catch (error) {
        console.error("Error updating user chat data:", error);
        return {
            error,
            status: 400,
        };
    }
}
