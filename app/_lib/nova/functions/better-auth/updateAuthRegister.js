import authConnect from "@/app/_lib/mongo/auth/authConnect.js";

export default async function updateAuthRegister(payload) {
    const {db} = await authConnect();
    const {collection, filter, update} = payload;
    try {
        if (!collection) {
            console.error("Auth collection not found");
            return;
        }
        if (!filter || !update) {
            console.error("Filter and update data are required");
            return;
        }
        return await db.collection(collection).updateOne(filter, update);
    } catch (error) {
        console.error('Error update auth data');
        console.error(error.message);
    }
}
