import {buildObjMetadata} from "./buildObjMetadata.js";
import _ from "lodash";

export function formDataToMetadata(formData, role, timezone) {
    const returnedObj = {
        menus: [],
        table_keys: {},
        restrictions: {}
    }
    if (!_.isEmpty(role)) returnedObj.role = role
    if (!_.isEmpty(timezone)) returnedObj.timezone = timezone
    if (!_.isEmpty(formData)) {
        for (const key in formData) {
            if (_.isNull(formData[key])) {
                returnedObj[key] = false;
            } else if (key === "restrictions" || key === "table_keys") {
                buildObjMetadata(formData[key], key, returnedObj)
            } else if (key === "menus") {
                returnedObj.menus = transformMenuItemsToOutput(formData[key]);
            } else {
                returnedObj[key] = buildObjMetadata(formData[key], false, {});
            }
        }
    }

    return returnedObj
}

const transformMenuItemsToOutput = (items) => {
    const result = [];

    if (!items || items.length === 0) return result;

    items.forEach((item) => {
        const menuEntry = {
            id: item.id,
            tools: [],
            routes: [],
        };

        if (item.visible) {
            menuEntry.routes.push(item.route);
        }

        if (item.tools && item.tools.length > 0) {
            item.tools.forEach((tool) => {
                if (tool.enabled) {
                    menuEntry.tools.push({route: item.route, tool: tool.id});
                }
            });
        }


        if (item.subMenus && item.subMenus.length > 0) {
            item.subMenus.forEach((subMenu) => {
                if (subMenu.enabled) {
                    menuEntry.routes.push(subMenu.route);
                    subMenu.tools.forEach((tool) => {
                        if (tool.enabled) {
                            menuEntry.tools.push({route: subMenu.route, tool: tool.id});
                        }
                    });
                }
            });
        }

        result.push(menuEntry);
    });

    return result;
};
