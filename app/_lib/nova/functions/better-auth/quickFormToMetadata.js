import {buildObjMetadata} from "./buildObjMetadata.js";
import {buildMenusMetadata} from "./buildMenusMetadata.js";
import _ from "lodash";

export function quickFormToMetadata(formData, keyAdded, allRoutesList, allRoles) {
    const restrictArray = ["clients", "vendors"]
    let returnedObj = {
        menus: [],
        table_keys: {}
    }
    if (!_.isEmpty(formData)) {
        if (_.includes(restrictArray, keyAdded)) returnedObj.restrictions = {}
        for (const key in formData) {
            switch (key) {
                case "roles":
                    const getRole = allRoles.find((rolInf) => rolInf.role === formData[key])
                    if (!getRole) return null
                    returnedObj.role = getRole ? getRole.role : null
                    returnedObj.menus = getRole ? getRole?.permissions?.menus : null
                    returnedObj.table_keys = getRole ? getRole?.permissions?.table_keys : null
                    returnedObj.restrictions = getRole ? getRole?.permissions?.restrictions : null
                    break;
                case "tools":
                    const objTools = {[keyAdded]: formData[key]}
                    returnedObj.menus = buildMenusMetadata(objTools, allRoutesList);
                    break;
                case "restrictions":
                    returnedObj.restrictions[keyAdded] = buildObjMetadata(formData[key], false, {});
                    break;
                case "table_keys":
                    returnedObj.table_keys[keyAdded] = buildObjMetadata(formData[key], false, {});
                    break;
            }
        }
    }
    return returnedObj
}
