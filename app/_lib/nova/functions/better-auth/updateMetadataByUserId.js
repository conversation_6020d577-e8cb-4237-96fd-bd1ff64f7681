import {ObjectId} from "mongodb";
import updateAuthRegister from "@/app/_lib/nova/functions/better-auth/updateAuthRegister.js";

export const updateMetadataByUserId = async (newMetadata, oldMetadata, userId) => {
    const objectMetadata = {}
    if (newMetadata?.role) {
        objectMetadata.role = newMetadata?.role
        objectMetadata.new = newMetadata?.role === "none"
    }
    if (newMetadata?.timezone) objectMetadata.timezone = newMetadata?.timezone
    if (newMetadata?.templateVersion) objectMetadata.templateVersion = newMetadata?.templateVersion
    if (newMetadata?.chat || newMetadata?.menus || newMetadata?.restrictions || newMetadata?.table_keys) {
        const metadataObj = {
            chat: newMetadata?.chat || oldMetadata?.chat || null,
            menus: newMetadata?.menus || oldMetadata?.menus || null,
            restrictions: newMetadata?.restrictions || oldMetadata?.restrictions || null,
            table_keys: newMetadata?.table_keys || oldMetadata?.table_keys || null
        }
        objectMetadata.metadata = JSON.stringify(metadataObj)
    }

    const payload = {
        collection: 'user',
        filter: {_id: new ObjectId(userId)},
        update: {$set: objectMetadata}

    };
    await updateAuthRegister(payload)
}
