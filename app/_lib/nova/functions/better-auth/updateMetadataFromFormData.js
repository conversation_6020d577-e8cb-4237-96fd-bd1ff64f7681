import {formDataToMetadata} from "./formDataToMetadata.js";
import _ from "lodash";
import {DateTime} from "luxon";
import {ObjectId} from "mongodb";
import {updateMetadataByUserId} from "@/app/_lib/nova/functions/better-auth/updateMetadataByUserId";
import authConnect from "@/app/_lib/mongo/auth/authConnect.js";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export async function updateMetadataFromFormData({data, userId, newRole, timezone, type}) {
    try {
        // TODO: change roles for CORE AUTH DB
        const {db: authDB} = await authConnect();
        const {db: adminDB} = await adminConnect();
        const collectionRoles = 'roles';
        const o_id = new ObjectId(userId);
        const user = await authDB.collection("user").findOne(o_id)

        if (!user) return ({error: "User not found", status: 404})

        const {metadata: oldMetadata, role: oldRole} = user || {metadata: null, role: null};
        const getPreviousRoleInf = await adminDB.collection(collectionRoles).findOne({role: oldRole})
        if (!_.isEmpty(getPreviousRoleInf)) {
            const updUsers = _.remove(getPreviousRoleInf?.users, (usersIds) => {
                return usersIds !== userId
            })
            await adminDB.collection(collectionRoles).updateOne(
                {_id: getPreviousRoleInf?._id},
                {$set: {users: updUsers}}
            );
        }
        const getNewRoleInf = await adminDB.collection(collectionRoles).findOne({role: newRole})
        const newUserForNewRole = getNewRoleInf?.users || []
        if (!_.includes(newUserForNewRole, userId)) {
            newUserForNewRole.push(userId)
            await adminDB.collection(collectionRoles).updateOne(
                {_id: getNewRoleInf?._id},
                {$set: {users: newUserForNewRole}}
            );
        }
        let metadataForm
        switch (type) {
            case "quick":
                metadataForm = {
                    ...getNewRoleInf?.permissions,
                    role: newRole,
                    timezone: timezone,
                    templateVersion: getNewRoleInf?.templateVersion
                }
                break;
            case "all":
                metadataForm = formDataToMetadata(data, newRole, timezone)
                break;
            default:
                metadataForm = formDataToMetadata(data, newRole, timezone)
                break;
        }

        let setTemplateText = "Default Template"
        if (getNewRoleInf?.editable) {
            const getTimeToFormat = DateTime.fromJSDate(getNewRoleInf?.updatedAt)
            setTemplateText = (getTimeToFormat.isValid) ?
                getTimeToFormat.toFormat('DD TTT') : DateTime.utc().toFormat('DD TTT')
        }
        await updateMetadataByUserId({...metadataForm, templateVersion: setTemplateText}, oldMetadata, userId)
        return ({response: 'Updated', status: 200})
    } catch (error) {
        console.error('Error getting user public metadata', error);
        return ({error, status: 400})
    }
}
