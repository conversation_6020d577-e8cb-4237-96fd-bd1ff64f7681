import {ObjectId} from "mongodb";
import authConnect from "@/app/_lib/mongo/auth/authConnect.js";

export async function getUserMetadataById(userId) {
    try {
        const {db} = await authConnect()
        const o_id = new ObjectId(userId);
        const user = await db.collection("user").findOne(o_id)
        return user || {};
    } catch (error) {
        console.error('Error getting user private metadata', error);
        return {error};
    }
}
