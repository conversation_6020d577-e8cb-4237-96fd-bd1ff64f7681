import _ from "lodash";

export function buildObjMetadata(getObjFD, keyLoop, formData) {
    if (!_.isEmpty(keyLoop)) {
        for (const keysDataObj in getObjFD) {
            formData[keyLoop][keysDataObj] = (!_.isNull(getObjFD[keysDataObj])) ? setKeysMetadata(getObjFD[keysDataObj]) : false
        }
    } else {
        return setKeysMetadata(getObjFD)
    }
}

function setKeysMetadata(object) {
    const keys = Object.keys(object);
    const trueKeys = keys.filter(key => object[key] === true);

    if (trueKeys.length === keys.length) {
        return ['*'];
    } else if (trueKeys.length === 0) {
        return false;
    }

    return trueKeys;
}