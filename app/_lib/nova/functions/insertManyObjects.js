import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect.js";

export default async function insertManyObjects(payload) {
    const {db} = await callCenterConnect();
    const {collection, documents} = payload;
    try {
        if (!collection) {
            console.error("Collection not specified");
            return {error: "Collection not specified", status: 400};
        }
        if (!documents || !Array.isArray(documents) || documents.length === 0) {
            console.error("Documents must be a non-empty array");
            return {error: "Documents must be a non-empty array", status: 400};
        }
        const result = await db.collection(collection).insertMany(documents);
        console.log(`${result.insertedCount} documents inserted successfully.`);
        return result;
    } catch (error) {
        console.error('Error inserting documents:', error.message);
        throw error;
    }
}
