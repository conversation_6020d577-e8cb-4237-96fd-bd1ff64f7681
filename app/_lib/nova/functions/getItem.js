import {getMongoDB} from "@/app/_lib/mongo/data/getMongoDB.js";

export default async function getItem(payload) {
    try {
        const {collectionName, tableName} = payload;
        if (!collectionName) {
            return {error: 'No "collectionName" provided', status: 400};
        }
        if (!tableName) {
            return {error: 'No "tableName" provided', status: 400};
        }
        const {db} = await getMongoDB();
        const foundItem = await db.collection(collectionName).findOne({table_name: tableName});

        if (!foundItem) {
            return {
                error: `Item with table_name "${tableName}" not found in collection "${collectionName}"`,
                status: 404,
            };
        }

        return {response: foundItem, status: 200};
    } catch (error) {
        console.error("Error in getItem:", error);
        return {error: "Internal server error", status: 500};
    }
}
