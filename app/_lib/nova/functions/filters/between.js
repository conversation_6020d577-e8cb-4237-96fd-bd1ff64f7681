import _ from "lodash";
import {DateTime} from "luxon";

export const between = (key, value, mongoFilters, {timezone}) => {
    const column = key.replace('-between', '');
    let temValue = typeof value === 'object' ? JSON.stringify(value) : value;
    if (!temValue.includes("NA_FILTER")) {
        const {start, end} = value
        if (timezone === 'UTC' || timezone === 'utc') {
            const startUTC = DateTime.fromISO(start).startOf('day').toJSDate()
            const endUTC = DateTime.fromISO(end).endOf('day').toJSDate()
            mongoFilters[column] = {$gte: startUTC, $lte: endUTC}
        } else {
            const startLocal = DateTime.fromISO(start, {zone: timezone}).startOf('day')
            const endLocal = DateTime.fromISO(end, {zone: timezone}).endOf('day')
            const startUTC = startLocal.toUTC().toJSDate()
            const endUTC = endLocal.toUTC().toJSDate()
            mongoFilters[column] = {$gte: startUTC, $lte: endUTC}
        }
    } else {
        _.omit(mongoFilters, column);
    }
    return mongoFilters
}
