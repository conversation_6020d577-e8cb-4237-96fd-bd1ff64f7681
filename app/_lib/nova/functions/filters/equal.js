import _ from "lodash";

export const equal = (key, value, mongoFilters, notFilter) => {
    const column = key.replace(`-eq`, '');
    if (value !== 'NA_FILTER') {
        if (notFilter && notFilter === 'not') {
            mongoFilters[column] = {$ne: value}
        } else {
            mongoFilters[column] = value;
        }
    } else {
        _.omit(mongoFilters, column);
    }
    return mongoFilters;
}
