import _ from "lodash";

export const size = (key, value, mongoFilters) => {
    const column = key.replace(`-size`, '');
    if (mongoFilters['tags']) {
        delete mongoFilters['tags'];
    }
    const actualValue = mongoFilters[column]
    if (value !== 'NA_FILTER') {
        mongoFilters[column] = {...actualValue, $size: 0}
    } else {
        _.omit(mongoFilters, column);
    }

    return mongoFilters
}
