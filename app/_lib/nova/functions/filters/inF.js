import _ from "lodash";

export const inF = (key, value, mongoFilters) => {
    const column = key.replace(`-inF`, '');
    if (mongoFilters['tags'] && mongoFilters['tags']['$size'] !== undefined) {
        if (mongoFilters['$or']) {
            delete mongoFilters['$or'];
        }
        if (mongoFilters['$size']) {
            delete mongoFilters['$size'];
        }
        if (_.keys(mongoFilters['tags']).length === 1) {
            delete mongoFilters['tags'];
        } else {
            delete mongoFilters['tags']['$size'];
        }
    }
    const actualValue = mongoFilters[column];
    if (value !== 'NA_FILTER' && value.length > 0) {
        mongoFilters[column] = { ...actualValue, $in: value };
    } else {
        _.omit(mongoFilters, column);
    }
    return mongoFilters;
};
