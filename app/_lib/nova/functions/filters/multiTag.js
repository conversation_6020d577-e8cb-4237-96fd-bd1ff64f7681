import _ from "lodash";

export const multiTag = (key, value, mongoFilters) => {
    const column = key.replace(`-multiTag`, '');
    if (mongoFilters['tags']) {
        delete mongoFilters['tags'];
    }
    if (value !== 'NA_FILTER') {
        mongoFilters.$or = [
            { [column]: { $size: 0 } },
            { [column]: { $in: [value] } }
        ];
    } else {
        _.omit(mongoFilters, column);
    }
    return mongoFilters;
};
