import _ from "lodash";

export const gloSearch = (key, value, mongoFilters) => {
    const uuidReg = /^(?=[a-f\d]{32}$)(\d+[a-f]|[a-f]+\d)/i
    if (value !== 'NA_FILTER') {
        if (uuidReg.test(value)) {
            const column = key.replace(`-globalS`, '');
            mongoFilters = {
                "$or": [
                    {[column]: value},
                    {"$text": {"$search": value}}
                ]
            };
        } else {
            mongoFilters.$text = {$search: value}
        }
    } else {
        _.omit(mongoFilters, '$text');
    }
    return mongoFilters;
}
