import _ from "lodash";

export const orF = (key, value, mongoFilters) => {
    const column = key.replace(`-orF`, '');
    if (value !== 'NA_FILTER' && _.isArray(value)) {
        const allOr = []
        value.forEach((item) => {
            const objOr = {
                [column]: item
            }
            allOr.push(objOr)
            if (_.isNull(item)) {
                const objOr = {
                    [column]: {$exists: false}
                }
                allOr.push(objOr)
            }
        })
        mongoFilters.$or = allOr
    } else {
        _.omit(mongoFilters, column);
    }

    return mongoFilters;
}