export {equal as eq} from "./equal"
export {between as between} from "./between"
export {matchText as match} from "./matchText"
export {gte as gte} from "./gte"
export {lte as lte} from "./lte"
export {not as not} from "./not"
export {gloSearch as globalS} from "./gloSearch.js"
export {inF as inF} from "./inF.js"
export {size as size} from "./size.js"
export {multiTag as multiTag} from "./multiTag.js"
export {orF as orF} from "./orF"
export {neF as neF} from "./neF.js"
