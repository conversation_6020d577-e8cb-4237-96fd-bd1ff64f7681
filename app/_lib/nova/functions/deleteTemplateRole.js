import _ from "lodash";
import {formDataToMetadata} from "@/app/_lib/nova/functions/better-auth/formDataToMetadata.js";
import {updateMetadataByUserId} from "@/app/_lib/nova/functions/better-auth/updateMetadataByUserId.js";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export async function deleteTemplateRole({roleId}) {
    try {
        // TODO: CHANGE ROLES FOR CORE AUTH DB
        const {db} = await adminConnect();
        const noneRoleId = "66a7c2c5756d5a8762b4c55e"
        const noneRoleInfo = await db.collection('roles').findOne({_id: noneRoleId})
        const getInfoRole = await db.collection('roles').findOne({_id: roleId})
        if (getInfoRole?.users && getInfoRole?.users.length > 0) {
            const noneUsersForUpd = noneRoleInfo?.users || []
            for (const userId of getInfoRole?.users) {
                if (!_.includes(noneUsersForUpd, userId)) noneUsersForUpd.push(userId)
                const userMetadata = formDataToMetadata(noneRoleInfo?.permissions, noneRoleInfo?.role, null)
                await updateMetadataByUserId(userMetadata, {}, userId)
            }
            await db.collection('roles').updateOne(
                {_id: noneRoleId},
                {$set: {users: noneUsersForUpd}}
            );
        }
        await db.collection('roles').deleteOne({_id: roleId})
        return {message: "Data updated successfully", status: 200};
    } catch (error) {
        console.error('Error to delete template role');
        console.error(error.message);
        return {message: error.message, status: 400}
    }
}
