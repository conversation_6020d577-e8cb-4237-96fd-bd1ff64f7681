import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export const getAccountingReport = async (payload) => {
    try {
        const {db} = await adminConnect();
        const {filters, accountingType, collection, isoWeekDateFilter} = payload;
        const pipeline = getPipeline(filters);
        return await db.collection('accounting').aggregate(pipeline).toArray();
    } catch (e) {
        console.log('Error');
        console.log(e);
        return [];
    }
}


const getPipeline = (filters) => {
    return [
        {
            $match: filters
        },
        {
            $group: {
                _id: {
                    labelAmount: "$labelAmount",
                    source_table: "$source_table",
                    type: "$type"
                },
                totalAmount: {$sum: "$amount"},
                totalItems: {$sum: 1},
                invoiceIds: {$addToSet: "$invoice_id"}
            }
        },
        {
            $project: {
                _id: 0,
                labelAmount: "$_id.labelAmount",
                source_table: "$_id.source_table",
                type: "$_id.type",
                amount: "$totalAmount",
                items: "$totalItems",
                invoice_ids: "$invoiceIds"
            }
        }
    ]
}



