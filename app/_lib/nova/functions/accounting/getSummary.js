import {getExpenseLeadsAccountingPipeline} from "@/app/_lib/utils/accounting/getExpenseLeadsAccountingPipeline.js";
import {getExpenseAccountingPipeline} from "@/app/_lib/utils/accounting/getExpenseAccountingPipeline.js";
import {getAccountingLock} from "@/app/_lib/nova/functions/accounting/getAccountingLock.js";
import _ from "lodash";
import {getRevenueLeadsAccountingPipeline} from "@/app/_lib/utils/accounting/getRevenueLeadsAccountingPipeline.js";
import {getRevenueAccountingPipeline} from "@/app/_lib/utils/accounting/getRevenueAccountingPipeline.js";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export const getSummary = async (payload) => {
    const {db} = await adminConnect();
    const {filters, accountingType, collection, isoWeekDateFilter} = payload;
    const {pipeline, hint} = getPipeline(accountingType, filters, collection);
    const accountingSummary = await db.collection(collection).aggregate(pipeline, {
        hint
    }).toArray();
    const accountingLocks = await getAccountingLock();
    const mappedData = mapData(collection, accountingType, accountingSummary, accountingLocks, isoWeekDateFilter);
    await getAverageAmount(mappedData, accountingType)
    return mappedData;
}

const processExpenseLeadsData = (data, accountingLocks, isoWeekDateFilter, collection, accountingType) => {
    return data
        .map(vendor => ({
            groupId: vendor.vendor_id,
            groupProperty: 'vendor',
            vendor_id: vendor.vendor_id,
            pubids: vendor.pubids.map(pubid => ({
                campaignKey: pubid.campaign_key,
                pubid: pubid.pubid || '',
                assigned: pubid.assign,
                unassigned: pubid.unassign,
                unitAmount: 0,
                expensed: 0,
                lock: lockColumn(accountingLocks, vendor.vendor_id, pubid.pubid || '', isoWeekDateFilter,
                    collection, accountingType, pubid.campaign_key, pubid.clientId),
                invoice_revenue_ids: pubid?.invoice_revenue_ids,
                invoice_expense_ids: pubid?.invoice_expense_ids
            })),
            vendor: vendor?.vendor
        }))
        .sort((a, b) => a.vendor?.localeCompare(b.vendor || ''));
};

const processExpenseData = (data, accountingLocks, isoWeekDateFilter, collection, accountingType) => {
    return data
        .map(vendor => ({
            vendor_id: vendor.vendor_id,
            pubids: vendor.result
                .flatMap(client =>
                    client.campaign_keys.flatMap(campaign =>
                        campaign.pubids.map(pubid => ({
                            campaignKey: campaign.campaign_key,
                            pubid: pubid.pubid || '',
                            assigned: pubid.assign,
                            unassigned: pubid.unassign,
                            unitAmount: 0,
                            expensed: 0,
                            client: client.client,
                            clientId: client.client_id,
                            lock: lockColumn(accountingLocks, vendor.vendor_id,
                                pubid.pubid || '', isoWeekDateFilter, collection, accountingType, campaign.campaign_key, client.client_id),
                            invoice_revenue_ids: pubid?.invoice_revenue_ids,
                            invoice_expense_ids: pubid?.invoice_expense_ids
                        }))
                    )
                )
                .sort((a, b) => a.pubid.localeCompare(b.pubid)), // Sorting pubids by pubid
            vendor: vendor.vendor,
            groupProperty: 'vendor',
        }))
        .sort((a, b) => a.vendor?.localeCompare(b.vendor || '')); // Sorting vendors by vendor name
};


const processLeadsRevenueData = (accountingSummary, accountingLocks, isoWeekDateFilter, collection, accountingType) => {
    return accountingSummary
        .map(item => ({
            client: item.client,
            clientId: item.clientId,
            pubids: item.campaign_keys.flatMap(campaign =>
                campaign.pubids
                    .map(pubid => ({
                        campaignKey: campaign.campaign_key,
                        pubid: pubid.pubid || '',
                        assigned: pubid.assign,
                        unassigned: pubid.unassign,
                        unitAmount: 0,
                        expensed: 0,
                        lock: lockColumn(accountingLocks, null, pubid.pubid || '',
                            isoWeekDateFilter, collection, accountingType, campaign.campaign_key, item.clientId),
                        invoice_revenue_ids: pubid?.invoice_revenue_ids,
                        invoice_expense_ids: pubid?.invoice_expense_ids
                    }))
                    .sort((a, b) => a.pubid.localeCompare(b.pubid))
            ),
            groupProperty: 'client',
        }))
        .sort((a, b) => a.client?.localeCompare(b.client || ''));
};

const processRevenueData = (accountingSummary, accountingLocks, isoWeekDateFilter, collection, accountingType) => {
    return accountingSummary
        .map(item => ({
            client: item.client,
            clientId: item.client_id,
            pubids: item.result.flatMap(client =>
                client.pubids
                    .map(pubid => ({
                        pubid: pubid.pubid || '',
                        assigned: pubid.assign,
                        unassigned: pubid.unassign,
                        unitAmount: 0,
                        expensed: 0,
                        lock: lockColumn(accountingLocks, null, pubid.pubid || '', isoWeekDateFilter,
                            collection, accountingType, client.campaign_key, item.client_id),
                        campaignKey: client.campaign_key,
                        invoice_revenue_ids: pubid?.invoice_revenue_ids,
                        invoice_expense_ids: pubid?.invoice_expense_ids
                    }))
                    .sort((a, b) => a.pubid.localeCompare(b.pubid))
            ),
            groupProperty: 'client',
        }))
        .sort((a, b) => a.client?.localeCompare(b.client || ''));
};


const lockColumn = (accountingLocks, vendorId, pubid, isoWeekDateFilter, collection, accountingType, campaignKey, clientId = 'unknown') => {
    const filterWeek = _.isArray(isoWeekDateFilter.week) ? isoWeekDateFilter.week : [isoWeekDateFilter.week];
    clientId = clientId || 'unknown';
    return !!_.find(accountingLocks, item => {
        const baseConditions =
            item.collection === collection &&
            item.client_id === clientId &&
            item.accounting_type === accountingType &&
            item.year === isoWeekDateFilter.year &&
            item.campaign_key === campaignKey &&
            item.pubid === pubid &&
            _.some(item.iso_week, week => _.includes(filterWeek, week));

        if (accountingType === 'EXPENSE') {
            return baseConditions && item.vendor_id === vendorId;
        }

        return baseConditions;
    });
};


const getPipeline = (accountingType, filters, collection) => {
    if (accountingType === 'EXPENSE') {
        return collection === 'leads'
            ? getExpenseLeadsAccountingPipeline(accountingType, filters, collection)
            : getExpenseAccountingPipeline(accountingType, filters, collection)
    } else {
        return collection === 'leads' ? getRevenueLeadsAccountingPipeline(filters, collection) : getRevenueAccountingPipeline(filters, collection)
    }
}


const mapData = (collection, accountingType, accountingSummary, accountingLocks, isoWeekDateFilter) => {
    if (collection === 'leads') {
        return accountingType === 'EXPENSE'
            ? processExpenseLeadsData(accountingSummary, accountingLocks, isoWeekDateFilter, collection, accountingType)
            : processLeadsRevenueData(accountingSummary, accountingLocks, isoWeekDateFilter, collection, accountingType)
    } else {
        return accountingType === 'EXPENSE'
            ? processExpenseData(accountingSummary, accountingLocks, isoWeekDateFilter, collection, accountingType)
            : processRevenueData(accountingSummary, accountingLocks, isoWeekDateFilter, collection, accountingType)
    }
}

const getAverageAmount = async (mappedData, accountingType) => {
    const key = accountingType === 'REVENUE' ? 'invoice_revenue_ids' : 'invoice_expense_ids';
    const {db} = await adminConnect();
    const allInvoiceIds = _.uniq(
        mappedData.flatMap(item =>
            item.pubids
                .filter(pubid => pubid[key] && pubid[key].length > 0 && pubid[key][0] != null)
                .flatMap(pubid => pubid[key])
        )
    );
    const invoices = await db.collection('invoices').find({_id: {$in: allInvoiceIds}}).toArray();
    const invoicesById = _.groupBy(invoices, '_id');
    for (let item of mappedData) {
        for (let pubid of item.pubids) {
            if (pubid[key] && pubid[key].length > 0 && pubid[key][0] != null) {
                const relatedInvoices = pubid[key].map(id => invoicesById[id]).flat();
                pubid.unitAmount = _.meanBy(relatedInvoices, 'amount_by_event') || 0;
            }
        }
    }
};


