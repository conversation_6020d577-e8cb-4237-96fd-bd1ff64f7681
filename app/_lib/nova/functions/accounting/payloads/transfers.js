import {DateTime} from "luxon";
import {generateId} from "../../../../../../app/_lib/utils/generateId";

export const transfers = (item, amount, rvnExpId, event) => {
    const utcDateTime = DateTime.utc()
    return {
        _id: generateId(),
        rvn_expense_id: rvnExpId,
        source_table: 'transfers',
        source_id: item._id,
        lead_id: item.lead_id,
        campaign_key: item.campaign_key,
        vendor_key: null,
        vendor: null,
        client: null,
        amount: amount,
        accounting_date: item.completed_date,
        transferred_to: item.transferred_to,
        created_at: utcDateTime,
        type: event
    }
}
