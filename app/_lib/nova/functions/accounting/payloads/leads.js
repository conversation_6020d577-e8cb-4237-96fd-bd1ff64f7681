import {DateTime} from "luxon";
import {generateId} from "../../../../../../app/_lib/utils/generateId";

export const leads = (item,amount,rvnExpId,event) => {
    const utcDateTime = DateTime.utc()
    return {
        _id: generateId(),
        rvn_expense_id: rvnExpId,
        source_table: 'leads',
        source_id: item._id,
        lead_id: item._id,
        campaign_key: item.campaign_key,
        vendor_key: item.vendor_key,
        vendor: item.vendor,
        client: item.client,
        amount: amount,
        accounting_date: item.date_created,
        created_at: utcDateTime,
        type: event
    }
}
