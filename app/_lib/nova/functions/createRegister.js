import {findSelectorsAndBuiltData} from "@/app/_lib/nova/functions/utils/findSelectorsAndBuiltData.js";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export default async function createRegister(payload) {
    const {db} = await adminConnect();
    const {data, collection, keysToReplace} = payload;
    try {
        if (!collection) {
            console.error("collection not found");
            return;
        }
        if (data.hasOwnProperty('pub_ids') && data.pub_ids === null) {
            data.pub_ids = [];
        }
        const builtData = findSelectorsAndBuiltData(keysToReplace, data);
        return await db.collection(collection).insertOne(builtData);
    } catch (error) {
        console.error('Error to create data');
        console.error(error.message);
    }
}
