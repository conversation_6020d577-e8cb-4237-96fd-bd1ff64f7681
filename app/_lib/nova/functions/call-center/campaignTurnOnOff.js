import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect.js";

export const campaignTurnOnOff = async (action, campaignId) => {
    const {db} = await callCenterConnect();
    if (!campaignId) {
        throw {error: `campaignId is required`}
    }
    const callCampaign = await db.collection('campaigns').findOne({_id: campaignId});
    if (!callCampaign) {
        throw {error: `call campaign with id: ${callCampaign} not found`}
    }
    const nobelBiz = callCampaign.nobelBiz;
    nobelBiz['concurrentCallsLimit'] = action === 'turnOn' ? 4 : 0;
    await db.collection('campaigns').updateOne({_id: campaignId}, {$set: {nobelBiz}})
    return {message: `call campaign with campaignId: ${campaignId} updated`}
}
