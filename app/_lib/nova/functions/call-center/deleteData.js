import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect.js";

export default async function deleteData(payload) {
    const {db} = await callCenterConnect();
    const {collection, filter, deleteMany} = payload;
    try {
        if (!collection) {
            console.error("Collection not found");
            return {error: "Collection not found", status: 400};
        }
        if (!filter) {
            console.error("Filter is required for deletion");
            return {error: "Filter is required for deletion", status: 400};
        }
        if (deleteMany) {
            return await db.collection(collection).deleteMany(filter);
        } else {
            return await db.collection(collection).deleteOne(filter);
        }
    } catch (error) {
        console.error('Error deleting data');
        console.error(error.message);
        return {error: "Internal server error", status: 500};
    }
}
