import {v4 as uuidv4} from "uuid";
import _ from "lodash";
import {params} from "@ampt/sdk";
import OpenAi from "openai";
import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect.js";

export default async function formatGumloopResponse(data, callCampaignId, existingConfig = null) {
    const actualData = data.data || data;
    if (
        !actualData ||
        !actualData.response ||
        !actualData.response.json_response ||
        actualData.response.json_response.length === 0
    ) {
        console.error("No data provided");
        return [];
    }

    const rawItems = actualData.response;
    const parsedItems = rawItems.json_response.map(itemStr => JSON.parse(itemStr));

    const formattedItems = parsedItems.map(item => {
        const originalId = item.ID ? String(item.ID) : uuidv4();
        return {
            id: originalId,
            topic: item.TOPIC,
            a: item.ANSWER
        };
    });

    async function getVector(textArray) {
        if (!Array.isArray(textArray)) {
            throw new Error("Input must be an array of text objects.");
        }
        const inputArray = textArray.map(text => JSON.stringify(_.omit(text, ["id"])));
        const openAiKey = params("openAiKey");
        if (!openAiKey) {
            return null;
        }
        const openAi = new OpenAi({apiKey: openAiKey});
        const response = await openAi.embeddings.create({
            model: "text-embedding-ada-002",
            input: inputArray,
        });
        return response.data.map(entry => entry.embedding);
    }

    const vectors = await getVector(formattedItems);
    let formattedItemsWithVectors = formattedItems.map((item, index) => {
        const vector = vectors[index];
        const norm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
        return {...item, vector, norm};
    });

    const {db} = await callCenterConnect();
    const collection = "vectra";


    const existingDocs = await db.collection(collection)
        .find({call_campaign_id: callCampaignId}, {projection: {_id: 1}})
        .toArray();
    const takenIds = new Set(existingDocs.map(d => d._id));

    const newDocs = formattedItemsWithVectors.map(it => {
        let newId = it.id;
        while (takenIds.has(newId)) {
            newId = `${it.id}-${uuidv4()}`;
        }
        takenIds.add(newId);
        return {
            _id: newId,
            topic: it.topic,
            a: it.a,
            norm: it.norm,
            call_campaign_id: callCampaignId,
            vector: it.vector
        };
    });

    if (newDocs.length > 0) {
        await db.collection(collection).insertMany(newDocs);
    }

    return newDocs;
}
