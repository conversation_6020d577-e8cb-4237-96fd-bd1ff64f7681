import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect.js";

export default async function getItemsById(payload) {
    try {
        const {db} = await callCenterConnect();
        const {collection, key, value} = payload;

        if (!collection) {
            return {error: 'No "collection" provided', status: 400};
        }

        if (!key || typeof value === 'undefined') {
            return {error: 'Missing "key" or "value" for query', status: 400};
        }

        const query = { [key]: value };
        const foundItems = await db.collection(collection).find(query).toArray();
        console.log(foundItems);

        if (!foundItems || foundItems.length === 0) {
            return {
                error: `No items found for ${key}: ${value}`,
                status: 404,
            };
        }

        return {response: foundItems};
    } catch (error) {
        console.error("Error in getItemsById:", error);
        return {error: "Internal server error", status: 500};
    }
}