import OpenAi from "openai";
import {params} from "@ampt/sdk";
import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect.js";

export default async function updateVectraObject(payload) {
    try {
        const {_id, item} = payload;
        if (!_id || !item) {
            return {error: "Missing _id or item", status: 400};
        }
        const {db} = await callCenterConnect();
        const openAiKey = params("openAiKey");
        if (!openAiKey) {
            return null;
        }
        const openAi = new OpenAi({apiKey: openAiKey});

        const inputText = JSON.stringify({topic: item.topic, a: item.a});
        const embeddingResponse = await openAi.embeddings.create({
            model: "text-embedding-ada-002",
            input: [inputText],
        });

        const vector = embeddingResponse.data[0].embedding;
        const norm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));

        const result = await db.collection('vectra').updateOne(
            {_id},
            {$set: {topic: item.topic, a: item.a, vector, norm}}
        );

        if (result.modifiedCount === 0) {
            return {error: "No item was updated", status: 404};
        }

        return {response: "Item updated successfully", status: 200};
    } catch (error) {
        console.error("Error in updateVectraObject:", error);
        return {error: "Internal server error", status: 500};
    }
}
