import {v4 as uuidv4} from "uuid";
import {params} from "@ampt/sdk";
import OpenAi from "openai";
import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect.js";

export default async function addKnowledgeItem(req) {
    try {
        const {db} = await callCenterConnect();
        const body = await req.json();

        const {item, callCampaignId} = body;
        if (!item || !item.topic || !item.a) {
            return new Response(JSON.stringify({error: "Invalid input data."}), {status: 400});
        }

        const collection = db.collection("vectra");
        const baseItem = {
            id: uuidv4(),
            topic: item.topic,
            a: item.a
        };

        const openAiKey = params("openAiKey");
        if (!openAiKey) {
            return null;
        }
        const openAi = new OpenAi({apiKey: openAiKey});

        const inputText = `${item.topic} ${item.a}`;
        const embeddingResponse = await openAi.embeddings.create({
            model: "text-embedding-ada-002",
            input: [inputText],
        });

        const vector = embeddingResponse.data[0].embedding;
        const norm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));

        const insertObject = {
            _id: baseItem.id,
            topic: item.topic,
            a: item.a,
            norm,
            call_campaign_id: callCampaignId,
            vector
        };

        const insertResult = await collection.insertOne(insertObject);
        return new Response(JSON.stringify({response: insertResult, item: insertObject}), {status: 200});

    } catch (error) {
        console.error("Error in addKnowledgeItem:", error);
        return new Response(JSON.stringify({error: "Internal server error"}), {status: 500});
    }
}
