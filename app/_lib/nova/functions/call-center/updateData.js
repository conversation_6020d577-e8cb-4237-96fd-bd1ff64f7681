import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect.js";

export default async function updateData(payload) {
    const {db} = await callCenterConnect();
    const {collection, filter, update, options} = payload;
    try {
        if (!collection) {
            console.error("collection not found");
            return {error: "Collection not found", status: 400};
        }
        if (!filter || !update) {
            console.error("Filter and update data are required");
            return {error: "Filter and update data are required", status: 400};
        }
        return await db.collection(collection).updateOne(filter, update, options);
    } catch (error) {
        console.error('Error updating data');
        console.error(error.message);
    }
}
