import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect.js";

export default async function deleteVectraItem(payload) {
    try {
        const {db} = await callCenterConnect();
        const {_id} = payload;
        if (!_id) {
            return {error: "Missing _id", status: 400};
        }

        const result = await db.collection('vectra').deleteOne({_id});

        if (result.deletedCount === 0) {
            return {error: "No item was deleted", status: 404};
        }

        return {response: "Item deleted successfully", status: 200};
    } catch (error) {
        console.error("Error in deleteVectraItem:", error);
        return {error: "Internal server error", status: 500};
    }
}
