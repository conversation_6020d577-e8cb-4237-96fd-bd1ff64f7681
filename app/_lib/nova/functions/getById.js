import {getMongoDB} from "@/app/_lib/mongo/data/getMongoDB.js";

export default async function getItemById(payload) {
    try {
        const {db} = await getMongoDB();
        const {id, collection} = payload;
        if (!id) {
            return {error: 'No "id" provided', status: 400};
        }
        if (!collection) {
            return {error: 'No "collection" provided', status: 400};
        }
        const foundItem = await db.collection(collection).findOne({call_campaign_id: id});

        if (!foundItem) {
            return {
                error: `Item not found"`,
                status: 404,
            };
        }

        return {response: foundItem};
    } catch (error) {
        console.error("Error in getItem:", error);
        return {error: "Internal server error", status: 500};
    }
}
