export function buildOptFilters(type, key, value) {
    switch (type) {
        case 'eq':
            return {
                [`${key}-eq`]: {
                    filterType: 'eq',
                    value
                }
            }
        case 'between':
            return {
                [`${key}-between`]: {
                    filterType: 'between',
                    value
                }
            }
        case 'match':
            return {
                [`${key}-match`]: {
                    filterType: 'match',
                    value
                }
            }
        case 'gte':
            return {
                [`${key}-gte`]: {
                    filterType: 'gte',
                    value
                }
            }
        case 'lte':
            return {
                [`${key}-lte`]: {
                    filterType: 'lte',
                    value
                }
            }
        case 'not':
            return {
                [`${key}-not`]: {
                    filterType: 'not',
                    value
                }
            }
        case 'inF':
            return {
                [`${key}-inF`]: {
                    filterType: 'inF',
                    value
                }
            }
        case 'orF':
            return {
                [`${key}-orF`]: {
                    filterType: 'orF',
                    value
                }
            }
        case 'neF':
            return {
                [`${key}-neF`]: {
                    filterType: 'neF',
                    value
                }
            }
    }
}