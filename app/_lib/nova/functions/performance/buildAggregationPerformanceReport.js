import * as funcCond from "@/app/_lib/utils/conditionals/index.js";
import _ from "lodash";

export function buildAggregationPerformanceReport(
    vendorId,
    mongoFilters,
    allKeys,
    additions
) {
    const {key1, key2, key3, key4, key5, key6} = allKeys;

    const $match = {
        ...mongoFilters,
        [key5]: vendorId
    }

    let $project = {_id: 1}
    let finalProject = {
        _id: 1,
        total: 1,
        infoSubId: 1
    }
    for (const key in allKeys) {
        if (allKeys[key]) $project = {...$project, [allKeys[key]]: 1}
    }

    let group1 = {
        _id: {[key4]: `$${key4}`, [key2]: `$${key2}`},
        count: {$sum: 1}
    }

    let group2 = {
        _id: {$ifNull: [`$_id.${key4}`, "Unknown"]},
        total: {$sum: "$count"},
        infoSubId: {
            $push: {
                _id: {$ifNull: [`$_id.${key2}`, "Unknown"]},
                total: "$count"
            }
        }
    }

    if (!_.isEmpty(additions)) {
        additions.map(item => {
            const cond = funcCond[item?.func];
            if (cond) {
                group1[item?.name] = {
                    "$sum": cond(key6, item?.value)
                }
                group2[item?.name] = {$sum: `$${item?.name}`}
                group2.infoSubId.$push[item?.name] = `$${item?.name}`
                finalProject[item?.name] = 1
            }
        })
    }

    return [
        {$match},
        {$project},
        {$group: group1},
        {$group: group2},
        {$project: finalProject}
    ];
}