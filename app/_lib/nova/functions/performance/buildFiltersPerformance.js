import {buildOptFilters} from "@/app/_lib/nova/functions/reports/builtOptFilters.js";
import {buildFilters} from "@/app/_lib/nova/functions/utils/buildFilters.js";

export function buildFiltersPerformance(filters, context) {
    let allFilters = {}
    if (filters.length > 0) {
        for (const filter of filters) {
            const {type, key, value} = filter;
            allFilters = {...allFilters, ...buildOptFilters(type, key, value)}
        }
    }
    return buildFilters(allFilters, context);
}
