export function buildTransfersAgingAggregation(leadsIds) {
    const $match = {
        lead_id: {$in: leadsIds},
        completed_date: {$ne: null}
    }
    const $lookup = {
        from: "leads",
        localField: "lead_id",
        foreignField: "_id",
        as: "relatedData"
    };
    const $unwind = "$relatedData";
    const addFields1 = {
        daysDifference: {
            $cond: {
                if: {
                    $and: [
                        {"$ne": ["$relatedData.date_created", null]},
                        {"$ne": ["$relatedData.date_created", ""]},
                        {"$ne": ["$completed_date", null]},
                        {"$ne": ["$completed_date", ""]}
                    ]
                },
                then: {
                    $dateDiff: {
                        startDate: "$relatedData.date_created",
                        endDate: "$completed_date",
                        unit: "day"
                    }
                },
                else: null
            }
        }
    }
    const $bucket = {
        groupBy: "$daysDifference",
        boundaries: [0, 30, 60, 90, 120],
        default: "Unknown",
        output: {
            count: {$sum: 1},
            ids: {$push: "$_id"},
            "transfers>120": {
                $sum: {$cond: [{$gte: ["$duration", 120]}, 1, 0]}
            },
            "transfers<120": {
                $sum: {$cond: [{$lte: ["$duration", 119]}, 1, 0]}
            }
        }
    };
    const addFields2 = {
        range: {
            $switch: {
                branches: [
                    {case: {$eq: ["$_id", 0]}, then: "0-30"},
                    {case: {$eq: ["$_id", 30]}, then: "31-60"},
                    {case: {$eq: ["$_id", 60]}, then: "61-90"},
                    {case: {$eq: ["$_id", 90]}, then: "91-120"},
                ],
                default: "120+"
            }
        }
    };

    return [
        {$match},
        {$lookup},
        {$unwind},
        {$addFields: addFields1},
        {$bucket},
        {$addFields: addFields2}
    ];
}