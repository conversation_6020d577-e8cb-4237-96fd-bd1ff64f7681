import {generateId} from "@/app/_lib/utils/generateId.js";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export const lockOnModify = async (lockOnModify, collection, id) => {
    const {db} = await adminConnect();
    if (lockOnModify && lockOnModify === true) {
        await db.collection('update_collection_lock').insertOne({
            _id: generateId(),
            collection: collection,
            source_id: id,
        })
    }
}
