import {params} from "@ampt/sdk";
import axios from "axios";

export const sendTaskToQueue = async (tasksAfterSubmit, payload) => {
    const dataBrokerHost = params('DATA_BROKER_HOST');
    const dataBrokerKey = params('DATA_BROKER_KEY');
    if (tasksAfterSubmit && tasksAfterSubmit.length > 0) {
        for (let task of tasksAfterSubmit) {
            console.log('Creating task: ', task);
            const response = await axios.post(`${dataBrokerHost}/api/secure/queue`,
                {payload, task}, {headers: {'x-api-key': dataBrokerKey}});
            console.log('Queue created successfully');
        }
    }
}
