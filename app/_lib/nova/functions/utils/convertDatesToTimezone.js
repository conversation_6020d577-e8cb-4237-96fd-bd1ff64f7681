import {DateTime} from "luxon";

export function convertDatesToTimezone(record, timezone) {
    const convertedRecord = { ...record };
    for (const key in convertedRecord) {
        const value = convertedRecord[key];
        if (value instanceof Date) {
            convertedRecord[key] = DateTime.fromJSDate(value, {zone: 'utc'}).setZone(timezone).toString()
        } else if (typeof value === 'object' && value !== null) {
            convertedRecord[key] = convertDatesToTimezone(value, timezone);
        }
    }
    return convertedRecord;
}