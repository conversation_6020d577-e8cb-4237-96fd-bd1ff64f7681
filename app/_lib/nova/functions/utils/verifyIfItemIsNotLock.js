import {throwError} from "@/app/_lib/utils/throwError.js";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export const verifyIfItemIsNotLock = async (id, collection) => {
    const {db} = await adminConnect();
    const isLock = await db.collection('update_collection_lock').findOne({collection, source_id: id});
    if (isLock) {
        throwError("A update task is running, please wait and try again")
    }
}
