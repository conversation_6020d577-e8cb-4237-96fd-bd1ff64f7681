import * as funcFilters from "../filters/index.js";
import _ from "lodash";

export const buildFilters = (filters, objTimeZone = {timezone: "America/Chicago"}) => {
    let mongoFilters = {};
    for (const [key, value] of Object.entries(filters)) {
        const {filterType, value: filterValue, notFilter} = value;
        const func = funcFilters[filterType];
        const setLastVal = !_.isEmpty(notFilter) ? notFilter : objTimeZone
        if (func) {
            mongoFilters = func(key, filterValue, mongoFilters, setLastVal)
        }
    }
    return mongoFilters;
}
