import _ from "lodash";
import stringToDate from "@/app/_lib/nova/functions/utils/stringToDate.js";

export function findSelectorsAndBuiltData(keysToRep, dataToFind) {
    keysToRep.forEach(key => {
        const findDataToRep = dataToFind[key?.keyRep]
        if (!_.isEmpty(findDataToRep)) {
            if (_.includes(key?.keyFind, "_id")) {
                dataToFind[key?.keyFind] = findDataToRep?.value;
            } else {
                dataToFind[key?.keyFind] = findDataToRep?.label;
            }
        }
    })
    const getOmitKeys = Object.keys(dataToFind).filter(key => {
        return _.includes(key, "Selector")
    })
    dataToFind = _.omit(dataToFind, getOmitKeys)

    for (const valData in dataToFind) {
        dataToFind[valData] = stringToDate(dataToFind[valData])
    }

    return dataToFind;
}