import _ from "lodash";
import {getMongoDB} from "@/app/_lib/mongo/data/getMongoDB.js";

export async function buildRestrictionsTables(table, vendorsRest, clientRest, filters) {
    const {db} = await getMongoDB();
    const doc = await db.collection(table).findOne();
    const keys = (doc) ? Object.keys(doc) : [];
    const restrictObj = {}
    let proceeded = true

    if (_.has(filters, 'vendor_id-inF') || _.has(filters, 'client_id-inF')) {
        const pickObjVendor = _.pick(filters, "vendor_id-inF")
        const pickObjClient = _.pick(filters, 'client_id-inF')
        const checkValuePicked = !_.isEmpty(pickObjVendor) ? pickObjVendor['vendor_id-inF'].value : pickObjClient['client_id-inF'].value
        if (!_.isEmpty(checkValuePicked) && checkValuePicked !== "NA_FILTER") {
            proceeded = (_.isArray(checkValuePicked) && _.includes(checkValuePicked, "NULL"))
        }
    }

    if (!_.includes(vendorsRest, "*") && _.includes(keys, 'vendor_id') && proceeded) {
        const nameOVendorId = 'vendor_id-inF'
        restrictObj[nameOVendorId] = {
            value: vendorsRest,
            filterType: 'inF',
            label: "vendor_id"
        }
    }

    if (!_.includes(clientRest, "*") && _.includes(keys, 'client_id') && proceeded) {
        const nameClientId = 'client_id-inF'
        restrictObj[nameClientId] = {
            value: clientRest,
            filterType: 'inF',
            label: "client_id"
        }
    }

    if (table === 'vendors') {
        if (!_.includes(vendorsRest, "*") && proceeded) {
            const nameOVendorId = '_id-inF'
            restrictObj[nameOVendorId] = {
                value: vendorsRest,
                filterType: 'inF',
                label: "_id"
            }
        }
    }

    if (table === 'clients') {
        if (!_.includes(clientRest, "*") && proceeded) {
            const nameClientId = '_id-inF'
            restrictObj[nameClientId] = {
                value: clientRest,
                filterType: 'inF',
                label: "_id"
            }
        }
    }

    // if (table === 'call-campaigns') {
    //     const nameKeyGroup = 'group-inF'
    //     if (!_.includes(vendorsRest, "*") && proceeded) {
    //         restrictObj[nameKeyGroup] = {
    //             value: vendorsRest,
    //             filterType: 'inF',
    //             label: "group"
    //         }
    //     }
    //     if (!_.includes(clientRest, "*") && proceeded) {
    //         restrictObj[nameKeyGroup] = {
    //             value: clientRest,
    //             filterType: 'inF',
    //             label: "group"
    //         }
    //     }
    //     console.log({restrictObj})
    //     console.log({vendorsRest})
    //     console.log({clientRest})
    // }

    return {...filters, ...restrictObj}
}
