import _ from "lodash";
import Decimal from "decimal.js";

export const invoiceMapper = async (items) => {
    return _.map(items, (item) => {
        item['line_items'] = _.map(item.line_items, lineItem => {
            const decimalAmount = new Decimal(lineItem.amount_by_event);
            const totalAmountDecimalAmount = lineItem.total_amount ? new Decimal(lineItem.total_amount) : new Decimal(0);
            lineItem['amount_by_event'] = decimalAmount && decimalAmount.toNumber() !== 0 ? Number(decimalAmount.div(100).toFixed(2)) : 0;
            lineItem['total_amount'] = totalAmountDecimalAmount && totalAmountDecimalAmount.toNumber() !== 0 ? Number(totalAmountDecimalAmount.div(100).toFixed(2)) : 0;
            return lineItem;
        })
        return item;
    })
}
