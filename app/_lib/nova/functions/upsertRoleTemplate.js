import {generateId} from "@/app/_lib/utils/generateId.js";
import {formDataToMetadata} from "@/app/_lib/nova/functions/better-auth/formDataToMetadata.js";
import {DateTime} from "luxon";
import _ from "lodash";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect.js";

export async function upsertRoleTemplate({role, permissions, userId}) {
    try {
        // TODO: change roles for CORE AUTH DB
        const {db} = await adminConnect();
        const checkIfExistRole = await db.collection('roles').findOne({role})
        const utcDateTime = DateTime.utc()
        const setDateTimeCopyName = utcDateTime.toFormat('DD TTT')
        const checkIfCopyRole = (!_.isEmpty(checkIfExistRole) && !checkIfExistRole?.editable)
        const setRoleName = (checkIfCopyRole) ?
            `${role} Copy: ${setDateTimeCopyName}` : role
        const builtDataTemplate = {
            permissions: formDataToMetadata(permissions, null, null),
            updatedAt: utcDateTime
        }
        if (_.isEmpty(checkIfExistRole) || checkIfCopyRole) {
            builtDataTemplate._id = generateId()
            builtDataTemplate.role = setRoleName
            builtDataTemplate.createdAt = utcDateTime
            builtDataTemplate.users = []
            builtDataTemplate.creator = userId
            builtDataTemplate.editable = true
        }
        const result = await db.collection('roles').updateOne(
            {role: setRoleName},
            {$set: builtDataTemplate},
            {upsert: true}
        );
        return {message: "Data updated successfully", status: 200};
    } catch (error) {
        console.error('Error upsert template role');
        console.error(error.message);
        return {message: error.message, status: 400}
    }
}
