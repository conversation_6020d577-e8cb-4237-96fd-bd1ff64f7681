import {getMongoDB} from "@/app/_lib/mongo/data/getMongoDB.js";

export default async function updateObjects(payload) {
    const {db} = await getMongoDB();
    const {collection, update} = payload;
    try {
        if (!collection) {
            console.error("collection not found");
            return;
        }
        return await db.collection(collection).bulkWrite(update);
    } catch (error) {
        console.error('Error updating data');
        console.error(error.message);
    }
}
