import _ from "lodash";
import {buildFilters} from "../../../_lib/nova/functions/utils/buildFilters.js";
import {formatCollectionName, getNovaSchema} from "@conversionfinder/conversion-finder-utils";
import {params} from "@ampt/sdk";
import {convertDateStringsToObjects} from "@/app/_lib/utils/convertDateStringsToObjects.js";

export const getCount = async (payload) => {
    const utilsUri = params('UTILS_URI');
    const {collection, filters, options, authMetadata, dbName, context, mongoFilters} = payload;
    const capitalizedCollection = formatCollectionName(collection);
    const mongoCollection = await getNovaSchema(capitalizedCollection, utilsUri, dbName);
    const getTimeZone = context ? context : {timezone: "America/Chicago"}
    const mongoFiltersWithDates = mongoFilters && !_.isEmpty(mongoFilters) ? convertDateStringsToObjects(mongoFilters) : buildFilters(filters, getTimeZone);
    let count = 0;
    if (_.isEmpty(mongoFiltersWithDates)) {
        count = await mongoCollection.estimatedDocumentCount();
    } else {
        count = await mongoCollection.countDocuments(mongoFiltersWithDates);
    }
    const totalPages = Math.ceil(count / options.limit) || 1
    return {response: {count, totalPages}, status: 200}
}
