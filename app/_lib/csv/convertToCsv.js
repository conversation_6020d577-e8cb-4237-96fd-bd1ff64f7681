import {Readable, Writable} from "node:stream";
import {format} from "@fast-csv/format";
import {mapHeaders} from "../../_lib/csv/mapHeaders.js";

export const convertToCsv = (data, headers) => {
    return new Promise((resolve, reject) => {
        const chunks = [];
        const writableStream = new Writable({
            write(chunk, encoding, callback) {
                chunks.push(chunk);
                callback();
            },
        });
        writableStream.on('finish', () => {
            resolve(Readable.from(chunks));
        });
        writableStream.on('error', reject);
        const csvStream = format({headers: true})
        const mappedData = mapHeaders(data, headers);
        csvStream.pipe(writableStream);
        mappedData.forEach(item => csvStream.write(item));
        csvStream.end();
    });
}
