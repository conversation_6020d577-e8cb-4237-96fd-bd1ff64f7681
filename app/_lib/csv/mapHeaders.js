import {DateTime} from "luxon";

export const mapHeaders = (data,headers) =>{
    return data.map((item,key) => {
        const newItem = {};
        headers.forEach(mapping => {
            if (item[mapping.original] instanceof Date) {
                const luxonDateTime = DateTime.fromJSDate(item[mapping.original]);
                newItem[mapping.label] = luxonDateTime.toFormat('yyyy-MM-dd HH:mm:ss');
            } else {
                newItem[mapping.label] = item[mapping.original];
            }
        });
        return newItem;
    });
}
