import {format} from "@fast-csv/format";
import {Readable, Writable} from "node:stream";
import {mapHeaders} from "../../../app/_lib/csv/mapHeaders.js";

export const convertToCsvStream = (data, headers) => {
    return new Promise((resolve, reject) => {
        const chunks = [];
        const writableStream = new Writable({
            write(chunk, encoding, callback) {
                chunks.push(chunk);
                callback();
            },
        });
        writableStream.on('finish', () => {
            resolve(Readable.from(chunks));
        });
        writableStream.on('error', reject);
        const csvStream = format({
            headers: true,
            writeHeaders: false
        })
        csvStream.pipe(writableStream);
        const mappedData = mapHeaders(data, headers);
        mappedData.forEach(item => csvStream.write(item));
        csvStream.end();
    });
}
