import { createSharedAuth } from "@conversionfinder/shared-auth/server";
import { admin, emailOTP, organization } from "better-auth/plugins";
import { params } from "@ampt/sdk";
import { additionalFieldsData } from "@/app/_lib/auth/utils/additionalFieldsData";
import { databaseHooksData } from "@/app/_lib/auth/utils/dataBaseHooksData";
import { InvitationMail } from "@/app/_lib/auth/mail_templates/InvitationMail";
import { sendMail } from "../mailgun/sendMail";
import { deleteUserConfig } from "@/app/_lib/auth/utils/deleteUserConfig";
import { OTPMail } from "@/app/_lib/auth/mail_templates/OTPMail";
import { stripe } from "@better-auth/stripe";
import Stripe from "stripe";
import _ from "lodash";

let baseURL =
  params("INSTANCE_TYPE") === "personal"
    ? "http://localhost:3001"
    : params("AMPT_URL");
baseURL = params("ENV_NAME") === "staging" ? params("PRODUCTION_URL") : baseURL;

let stripeSecretKey = params("STRIPE_SECRET_KEY");
console.log("stripeSecretKey", stripeSecretKey);
const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
console.log("stripeWebhookSecret", stripeWebhookSecret);
console.log("baseURL", baseURL);
console.log("�� Auth Configuration:", {
  baseURL,
  rpID: params("INSTANCE_TYPE") === "personal" ? "localhost" : "aurionx.ai",
  databaseName: "core_auth",
  sessionMode: "shared",
  environment:
    params("INSTANCE_TYPE") === "personal" ? "development" : "production",
});
// Debug

const stripeClient = new Stripe(stripeSecretKey, {
  apiVersion: "2025-06-30.basil",
  typescript: true,
});
console.log("betther-auth-secret", params("BETTER_AUTH_SECRET"));

export const auth = createSharedAuth({
  // Database configuration
  databaseUrl: params("MONGO_URI"),
  databaseName: "core_auth", // Especificar el nombre de la base de datos

  // Passkey configuration
  passkey: {
    rpName: "AurionX",
    rpID: params("INSTANCE_TYPE") === "personal" ? "localhost" : "aurionx.ai",
    origin: baseURL,
    authenticatorSelection: {
      authenticatorAttachment: "platform",
      userVerification: "preferred",
      requireResidentKey: false,
    },
  },

  // Project configuration
  project: {
    projectName: "aurionx",
    origin: baseURL,
    sessionMode: "shared", // Para mejor reconocimiento de sesiones
    secret: params("BETTER_AUTH_SECRET"),
  },

  // CORS configuration
  allowedOrigins: [
    "http://localhost:3000",
    "http://localhost:3001",
    "https://accounts.aurionx.ai",
    "https://dev.aurionx.ai",
    "https://inventive-app-3641b.ampt.app",
    "https://bright-hack-q4pqz.ampt.app",
  ],

  // Session configuration
  session: {
    expiresIn: 60 * 60 * 24 * 14,
    updateAge: 60 * 60 * 24,
  },

  // Environment
  environment:
    params("INSTANCE_TYPE") === "personal" ? "development" : "production",

  // Custom configuration
  custom: {
    appName: "AurionX",
    baseURL: baseURL,
    multiSession: {
      maximumSessions: 3,
    },
    cookieConfig: {
      name: "better-auth.session-token-reporting", // ← COOKIE ÚNICA
      domain: undefined, // ← NO compartir dominio
      sameSite: "lax",
      httpOnly: true,
      secure: params("INSTANCE_TYPE") !== "personal",
    },
    plugins: [
      admin({
        defaultRole: "none",
        adminRoles: "admin",
      }),
      organization({
        async sendInvitationEmail(data) {
          const inviteLink = `${baseURL}/accept-invitation/${data.id}`;
          const getData = {
            email: data.email,
            invitedByUsername: data.inviter.user.name,
            invitedByEmail: data.inviter.user.email,
            teamName: data.organization.name,
            inviteLink,
          };
          const otpEmail = InvitationMail(getData);
          await sendMail(
            getData?.email,
            "AurionX Invitation",
            otpEmail,
            null,
            "Organization Invitation"
          );
        },
      }),
      emailOTP({
        otpLength: 6,
        expiresIn: 600,
        async sendVerificationOTP({ email, otp, type }) {
          if (
            !_.includes(email, "@test.com") &&
            params("INSTANCE_TYPE") !== "personal"
          ) {
            const otpEmail = OTPMail(otp);
            await sendMail(
              email,
              "AurionX Log Code",
              otpEmail,
              null,
              "Sign In Code"
            );
          } else {
            console.log("sendVerificationOTP", { email, otp, type });
          }
        },
      }),
      stripe({
        stripeClient,
        stripeWebhookSecret,
        createCustomerOnSignUp: true,
      }),
    ],
    // Database hooks y configuración adicional
    databaseHooks: databaseHooksData,
    user: {
      deleteUser: deleteUserConfig,
      additionalFields: additionalFieldsData,
    },
  },
});
