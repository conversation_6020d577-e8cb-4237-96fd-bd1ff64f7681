import {UserDeletionData} from "@/app/_lib/auth/interfaces/UserDeletionData";

export const NotificationDeleteUser = (data: UserDeletionData): string => {
    const jsonString = JSON.stringify(data, null, 2);
    return `
        <div style="font-family: Arial, sans-serif;">
            <h2>A user has been deleted</h2>
            
            <h3>User Information</h3>
            <p>ID: ${data.user.id}</p>
            <p>Email: ${data.user.email}</p>
            ${data.user.name ? `<p>Name: ${data.user.name}</p>` : ''}
            <p>Organzations Database Names:</p>
            <ul>
                ${data.ownedOrganizations.map((org: any, index: number) => `
                    <li>${index + 1}: ${org.dbName}</li>
                `).join('')}
            </ul>
            
            <h3>Owned Organizations (Requiring Database Deletion)</h3>
            <ul>
                ${data.ownedOrganizations.map(org => `
                    <li>
                        <strong>Organization: ${org.name}</strong> (ID: ${org.id})
                        <br>Database Name: ${org.dbName}
                        <br>Members:
                        <ul>
                            ${org.members.map(member => `
                                <li>${member.email} (${member.role})</li>
                            `).join('')}
                        </ul>
                    </li>
                `).join('')}
            </ul>

            <h3>Member Organizations</h3>
            <ul>
                ${data.memberOrganizations.map(org => `
                    <li>${org.name} (Role: ${org.role})</li>
                `).join('')}
            </ul>

            <h3>Complete Data (JSON)</h3>
            <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;">
${jsonString}
            </pre>

            <p style="color: #ff0000;">
                Please ensure to delete the following databases:
                <ul>
                    ${data.ownedOrganizations.map(org => `
                        <li>Organization Database: ${org.dbName}</li>
                    `).join('')}
                </ul>
            </p>
        </div>
    `;
}
