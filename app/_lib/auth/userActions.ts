"use server";

import { authClient } from "./auth-client";
import { revalidatePath } from "next/cache";

export async function updateUserName(name: string) {
  try {
    const result = await authClient.updateUser({ name });
    if (result?.error) {
      return { error: result.error.message || "Error al actualizar el nombre" };
    }
    revalidatePath("/user");
    return { success: true };
  } catch (error) {
    console.error("Error updating user name:", error);
    return { error: "Error interno del servidor" };
  }
}

export async function revokeUserSession(token: string) {
  try {
    const result = await authClient.revokeSession({ token });
    if (result?.error) {
      return { error: result.error.message || "Error al revocar la sesión" };
    }
    revalidatePath("/user");
    return { success: true };
  } catch (error) {
    console.error("Error revoking session:", error);
    return { error: "Error interno del servidor" };
  }
}

export async function revokeAllUserSessions() {
  try {
    const result = await authClient.revokeOtherSessions();
    if (result?.error) {
      return {
        error: result.error.message || "Error al revocar todas las sesiones",
      };
    }
    revalidatePath("/user");
    return { success: true };
  } catch (error) {
    console.error("Error revoking all sessions:", error);
    return { error: "Error interno del servidor" };
  }
}

export async function registerUserPasskey() {
  try {
    const result = await authClient.passkey.addPasskey();
    if (result?.error) {
      return { error: result.error.message || "Error al registrar passkey" };
    }
    revalidatePath("/user");
    return { success: true };
  } catch (error) {
    console.error("Error registering passkey:", error);
    return { error: "Error interno del servidor" };
  }
}

export async function deleteUserPasskey(passkeyId: string) {
  try {
    const result = await authClient.passkey.deletePasskey({ id: passkeyId });
    if (result?.error) {
      return { error: result.error.message || "Error al eliminar passkey" };
    }
    revalidatePath("/user");
    return { success: true };
  } catch (error) {
    console.error("Error deleting passkey:", error);
    return { error: "Error interno del servidor" };
  }
}

export async function deleteUserAccount() {
  try {
    const result = await authClient.deleteUser();
    if (result?.error) {
      return { error: result.error.message || "Error al eliminar la cuenta" };
    }
    revalidatePath("/");
    return { success: true };
  } catch (error) {
    console.error("Error deleting account:", error);
    return { error: "Error interno del servidor" };
  }
}
