export const additionalFieldsData: any = {
    metadata: {
        type: "string",
        required: true,
        defaultValue: JSON.stringify({
            chat: null,
            menus: null,
            table_keys: null,
            restrictions: null,
        }),
        input: true,
    },
    new: {
        type: "boolean",
        required: true,
        defaultValue: true,
        input: true,
    },
    dev: {
        type: "boolean",
        required: true,
        defaultValue: false,
        input: false,
    },
    timezone: {
        type: "string",
        required: true,
        defaultValue: "America/Chicago",
        input: true,
    },
    activeOrgId: {
        type: "string",
        required: true,
        defaultValue: null,
        input: true,
    },
    templateVersion: {
        type: "string",
        required: true,
        defaultValue: "Default Template",
        input: true,
    }
}
