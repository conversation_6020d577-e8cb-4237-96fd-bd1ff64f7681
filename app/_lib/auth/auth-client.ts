'use client'

import {createAuth<PERSON>lient} from "better-auth/react"
import {adminClient, emailOTPClient, organizationClient, passkeyClient} from "better-auth/client/plugins";
import { stripeClient } from "@better-auth/stripe/client"
export const authClient = createAuthClient({
    plugins: [
        adminClient(),
        organizationClient(),
        emailOTPClient(),
        passkeyClient(),
        stripeClient({ subscription: true })
    ]
})

export const {useSession, updateUser} = createAuthClient()
