export interface UserDeletionData {
    user: {
        id: string;
        email: string;
        name?: string;
        createdAt?: Date;
        dbName?: string;
    };
    ownedOrganizations: {
        id: string;
        name: string;
        dbName: string;
        members: Array<{
            email: string;
            role: string;
            name?: string;
        }>;
    }[];
    memberOrganizations: {
        id: string;
        name: string;
        role: string;
    }[];
    deletionRequestedAt: Date;
}
