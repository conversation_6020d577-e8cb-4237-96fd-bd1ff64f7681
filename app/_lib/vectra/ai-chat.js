import {openAiAgent} from "@/app/_lib/vectra/actions/openAiAgent.js";
import {NextResponse} from "next/server";

export default async function aiChat(req) {
    try {
        console.log("aiChat function called");
        const {message, session, config} = req;

        if (!message || !session) {
            console.error("Missing required parameters in aiChat");
            return NextResponse.json({error: "Missing required parameters"}, {status: 400});
        }

        console.log("Calling openAiAgent with session and message");
        const emitter = await openAiAgent(session, message, config);

        const stream = new ReadableStream({
            start(controller) {
                emitter.on("content", (content) => {
                    controller.enqueue(`data: ${JSON.stringify({content})}\n\n`);
                });

                emitter.on("complete", (session) => {
                    controller.enqueue(`data: ${JSON.stringify({done: true, session})}\n\n`);
                    controller.close();
                });

                emitter.on("error", (error) => {
                    console.error("Stream error:", error);
                    controller.enqueue(`data: ${JSON.stringify({error: error.message})}\n\n`);
                    controller.close();
                });
            },
        });
        console.log("Returning stream response");
        return new Response(stream, {
            headers: {
                "Content-Type": "text/event-stream",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            },
        });
    } catch (error) {
        console.error("Error processing chat:", error);
        console.error("Error stack:", error.stack);

        // Return a more detailed error response
        return NextResponse.json({
            error: "Error processing chat request",
            message: error instanceof Error ? error.message : "Unknown error",
            stack: process.env.NODE_ENV !== 'production' ? (error instanceof Error ? error.stack : undefined) : undefined
        }, {status: 500});
    }
}
