import { testKnowledgeBase } from "@/app/_lib/vectra/actions/openAiAgent.js";

export default async function knowledgeBaseTest(req) {
    try {
        const { message, config } = req;
        const emitter = await testKnowledgeBase({}, message, config);

        if (Array.isArray(emitter)) {
            return new Response(JSON.stringify({ data: emitter }), {
                status: 200,
                headers: { "Content-Type": "application/json" },
            });
        }
        return new Response(
            JSON.stringify({
                info: "Emitter is not an array",
                type: typeof emitter,
            }),
            {
                status: 200,
                headers: { "Content-Type": "application/json" },
            }
        );
    } catch (error) {
        console.error("Error processing chat:", error);
        return new Response(
            JSON.stringify({ error: "Error processing chat request" }),
            {
                status: 500,
                headers: { "Content-Type": "application/json" },
            }
        );
    }
}