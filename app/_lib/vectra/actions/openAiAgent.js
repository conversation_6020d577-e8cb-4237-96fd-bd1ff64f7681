import { params } from "@ampt/sdk";
import { EventEmitter } from "events";
import fetch from "node-fetch";
import { dynamicKnowledgeBase } from "@/app/_lib/vectra/actions/dynamicPromptGenerator.js";

/**
 * Lightweight wrapper that delegates all LLM work to the aurion‑chat HTTP API
 * documented in chat-api.md. It keeps the same EventEmitter contract that the
 * previous implementation exposed (`content`, `complete`, `error`).
 *
 * @param {Object} session  Current session object – must contain an `id` field.
 * @param {String} transcript  Raw user message.
 * @param {Object} opts  Optional.  { stream?: boolean } (default true).
 * @returns {EventEmitter}
 */
export const openAiAgent = async (
    session,
    transcript,
    { stream = true, config } = {}
) => {
    const resolvedPrompt =
        session?.prompt && session.prompt.trim().length > 0
            ? session.prompt
            : config?.prompt;

    if (resolvedPrompt) {
        session.prompt = resolvedPrompt;
    }

    const apiBase = 'https://aurionx-agent-development-conversionfinder.svc-us5.zcloud.ws';
    const emitter = new EventEmitter();
    let sessionId = session?.sessionId;
    if (!sessionId) {
        const campaignId = session?.scripts?.[0]?.callCampaignId;
        const campaignPhone = session?.campaignPhone;
        if (campaignId) {
            const createRes = await fetch(`${apiBase}/aurion-chat/session`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ campaignId, campaignPhone, userId: session?.client?.phone, config: { prompt: resolvedPrompt } }),
            });
            if (createRes.ok) {
                const json = await createRes.json();
                sessionId = json.sessionId;
                session.sessionId = json.sessionId;
                console.log("openAiAgent: auto-created sessionId", sessionId);
            } else {
                const errText = await createRes.text();
                emitter.emit("error", new Error(`Failed to auto-create session: ${createRes.status} ${errText}`));
                return emitter;
            }
        }
    }

    if (!apiBase) {
        process.nextTick(() =>
            emitter.emit("error", new Error("Missing `aurionChatUrl` param"))
        );
        return emitter;
    }

    const endpoint = stream
        ? "/aurion-chat/message/stream"
        : "/aurion-chat/message";

    // Perform fetch and SSE parsing in background
    (async () => {
        try {
            // Health check: confirm the API base is reachable
            const healthRes = await fetch(apiBase, { method: "GET" });
            console.log("openAiAgent health check status:", healthRes.status, healthRes.statusText);
            if (!healthRes.ok) {
                emitter.emit(
                    "error",
                    new Error(`Cannot connect to Chat API at ${apiBase}: ${healthRes.status} ${healthRes.statusText}`)
                );
                return;
            }

            console.log("openAiAgent: will POST to", `${apiBase}${endpoint}`, "with body", { sessionId: session.sessionId, message: transcript });
            const response = await fetch(`${apiBase}${endpoint}`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    sessionId: session.sessionId,
                    message: transcript,
                    config: { ...config, prompt: resolvedPrompt }
                }),
            });

            if (!response.ok) {
                const text = await response.text();
                emitter.emit(
                    "error",
                    new Error(`Chat API error ${response.status}: ${text}`)
                );
                return;
            }

            if (stream) {
                // Parse Server‑Sent Events (text/event-stream)
                const decoder = new TextDecoder();
                let buffer = "";

                for await (const chunk of response.body) {
                    buffer += decoder.decode(chunk, { stream: true });

                    let boundary;
                    while ((boundary = buffer.indexOf("\n\n")) !== -1) {
                        const rawEvent = buffer.slice(0, boundary).trim();
                        buffer = buffer.slice(boundary + 2);

                        if (!rawEvent) continue;

                        const lines = rawEvent.split("\n");
                        const dataLine = lines.find((l) => l.startsWith("data:"));
                        const dataStr = dataLine ? dataLine.replace(/^data:\s*/, "") : "{}";
                        let data;
                        try {
                            data = JSON.parse(dataStr);
                        } catch {
                            data = { content: dataStr };
                        }

                        // Treat the special `{ done: true, session: {...} }` message as a
                        // completion event even when the server doesn’t send an explicit `type`.
                        if (data.done) {
                            if (data.session) {
                                const prevPrompt = resolvedPrompt;
                                // merge, giving priority to values returned by the server
                                session = { ...data.session, ...session };
                                if (!session.prompt) session.prompt = prevPrompt;
                            }
                            emitter.emit("complete", session);
                            continue; // skip further processing for this event
                        }

                        // Determine event type: prefer data.type
                        let eventType = data.type || "message";

                        if (eventType === "text" || eventType === "message") {
                            emitter.emit("content", data.content);
                        } else if (eventType === "complete") {
                            emitter.emit("complete", data.session || session);
                        } else if (eventType === "error") {
                            emitter.emit("error", new Error(data.content || "Unknown error from Chat API"));
                        }
                    }
                }
            } else {
                const data = await response.json();
                const prevPrompt = resolvedPrompt;
                if (data && data.session) {
                    session = { ...data.session, ...session };
                    if (!session.prompt) session.prompt = prevPrompt;
                }
                emitter.emit("complete", session);
            }
        } catch (err) {
            emitter.emit("error", err);
        }
    })();

    return emitter;
};

export const testKnowledgeBase = async (_session, transcript, config) => {
    try {
        return await dynamicKnowledgeBase(transcript, "index", config);
    } catch (err) {
        console.error("Error in testKnowledgeBase:", err);
        return [];
    }
};
