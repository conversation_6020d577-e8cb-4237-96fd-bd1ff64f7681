import _ from "lodash";
import {tryit} from "radash";
import OpenAi from "openai";
import {z} from "zod";
import {zodResponseFormat} from "openai/helpers/zod";
import {params} from "@ampt/sdk";

export const metaDataExtractionAgent = async (transcript, metadata, variables, dataMap, finalPrompt) => {

    const openAiKey = params('openAiKey') ?? null;

    if (!openAiKey) {
        return null;
    }

    const openAi = new OpenAi({
        apiKey: openAiKey
    });

    if (!openAi) {
        return  null
    }
    delete metadata._id;
    const system = finalPrompt;

    const transcriptClean = _.compact(
        _.map(transcript, (message) => {
            message = `${message.role === "assistant" ? "agent" : "client"}: ${
                message.content
            }`;
            return message;
        })
    );
    const transcriptString = JSON.stringify(transcriptClean);

    const ExtractionSchema = z.object({
        metadata: z
            .array(
                z.object({
                    key: z.string(),
                    value: z.string(),
                })
            )
            .optional(),
    });

    const [error, chatCompletion] = await tryit(async () => {
        return openAi.beta.chat.completions.parse({
            model: "gpt-4o-mini",
            messages: [
                {role: "system", content: system},
                {
                    role: "user",
                    content: `metadata:\n${JSON.stringify(metadata, null, 2)}
                    dataMap:\n${JSON.stringify(dataMap, null, 2)}
                    dynamicVariables:\n${JSON.stringify(
                        variables.filter((v) => v.type === "dynamic"),
                        null,
                        2
                    )}
                    transcript:\n${transcriptString}`,
                },
            ],
            response_format: zodResponseFormat(ExtractionSchema, "extractionSchema"),
        });
    })();

    if (error) {
        console.error("Error creating chat completion:", error);
        return metadata;
    }

    const {refusal, parsed} = chatCompletion.choices[0].message;

    // If the model refuses to respond, you will get a refusal message
    if (refusal) {
        console.error(refusal);
        return false;
    } else {
        return updateMetadata(parsed.metadata);
    }
}

const updateMetadata = (parsedArray) => {
    const metadata = {};
    parsedArray.forEach((item) => {
        metadata[item.key] = item.value;
    });
    return metadata;
};
