export async function endChatSession(sessionId) {
    if (!sessionId) {
        return {
            status: 400,
            body: { error: "Missing sessionId" },
        };
    }

    try {
        const response = await fetch(`https://aurionx-agent-development-conversionfinder.svc-us5.zcloud.ws/aurion-chat/session/${sessionId}`, {
            method: "DELETE",
        });

        if (!response.ok) {
            const errorText = await response.text();
            return {
                status: response.status,
                body: { error: `Failed to delete session: ${errorText}` },
            };
        }

        return {
            status: 200,
            body: { success: true },
        };
    } catch (err) {
        return {
            status: 500,
            body: { error: err.message },
        };
    }
}
