import {params} from "@ampt/sdk";
import jws from "jws";
import {throwError} from "@/app/_lib/utils/throwError.js";


export const validate = (token) => {
    const JWT_SECRET_KEY = params('JWT_SECRET_KEY') ? params('JWT_SECRET_KEY') : 'aurionX';
    const isValid = jws.verify(token, 'HS256', JWT_SECRET_KEY);
    if (isValid) {
        return jws.decode(token).payload;
    } else {
        throwError('invalid sign')
    }
}
