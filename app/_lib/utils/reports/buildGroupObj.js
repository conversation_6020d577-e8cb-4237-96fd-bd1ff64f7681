import * as funcCond from "../conditionals/index.js";
import _ from "lodash";

export const buildGroupObj = (keysProAgg, selObj, secKey, options) => {
    if (_.isEmpty(selObj)) return {}
    const project0 = createProjectStage(keysProAgg)
    const group = {}
    const lookup = (selObj?.foreign) ? {
        from: selObj?.foreign || "",
        localField: "_id",
        foreignField: "_id",
        as: "data_foreign"
    } : {}
    const unwind = (selObj?.foreign) ? "$data_foreign" : null
    const project = (selObj?.foreign) ? {
        [selObj?.key]: "$_id",
        [selObj?.show]: {
            "$ifNull": [`$data_foreign.${selObj?.f_key}`, `Unknown ${selObj?.show}`]
        },
        total: 1
    } : {
        [selObj?.key]: "$_id",
        total: 1
    }

    group._id = {
        $ifNull: [`$${selObj?.key}`, selObj?.replace],
    }
    options.map(item => {
        const cond = funcCond[item?.func];
        if (cond) {
            group[item?.name] = {
                "$sum": cond(secKey, item?.value)
            }
            project[item?.name] = 1
        }
    })
    group.total = {"$sum": 1}
    project._id = 1
    return {project0, group, lookup, unwind, project}
}

function createProjectStage(fields) {
    const projectStage = {};

    fields.forEach(field => {
        projectStage[field] = 1;
    });

    return projectStage;
}
