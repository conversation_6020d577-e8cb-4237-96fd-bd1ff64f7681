import _ from "lodash";
import * as funcCond from "../conditionals/index.js";

export function buildComplexGroupObj(builtAggData, dataRequired, dateOnlyMode) {
    const foreignNames = ["clients", "vendors", "transfers"]
    const objSelectDrillDown = dataRequired?.selectDetail
    const getDatePicker = dataRequired?.pickDate
    const valueDDSelect = objSelectDrillDown?.value
    const fKey = objSelectDrillDown?.fKey
    const addKey = objSelectDrillDown?.key
    const keyWOId = (fKey) ? fKey : _.replace(addKey, '_id', '');
    const keyReq2Cond = _.replace(dataRequired.requiredKey, '$', '');
    const firstGroup = dataRequired?.selectObj?.show
    const firstKey = dataRequired?.selectObj?.key
    const getThirdLevel = dataRequired?.thirdLevelDetail

    const pushObj = (valueDDSelect === "dates") ?
        {[addKey]: {$dateToString: {format: "%b %d, %Y", date: `$${getDatePicker}`}}} :
        {[addKey]: `$${addKey}`}

    const pushObj3L = (!_.isEmpty(getThirdLevel?.key)) ?
        {[getThirdLevel?.value]: `$${getThirdLevel?.value}`} :
        {}

    builtAggData.group[valueDDSelect] = {
        $push: {
            ...pushObj,
            [dataRequired.requiredKey]: `$${dataRequired.requiredKey}`,
            ...pushObj3L
        }
    }
    const unwind2 = {
        path: `$${valueDDSelect}`,
        preserveNullAndEmptyArrays: true
    }

    builtAggData.project[valueDDSelect] = 1
    let group2 = {
        _id: {
            [`${dataRequired?.selectObj?.key}`]: `$${dataRequired?.selectObj?.key}`,
            [addKey]: (fKey) ? `$data_foreign2.${fKey}` : `$${valueDDSelect}.${addKey}`
        },
        [firstGroup]: {$first: `$${firstGroup}`}
    }

    for (const grp1Key in builtAggData?.group) {
        if (grp1Key !== "_id" && grp1Key !== valueDDSelect) {
            group2[`${firstGroup}_${grp1Key}`] = {$first: `$${grp1Key}`}
        }
        if (grp1Key === "total") group2[`${keyWOId}_${grp1Key}`] = builtAggData?.group[grp1Key]
    }

    dataRequired.optionsCond.map(item => {
        const cond = funcCond[item?.func];
        if (cond) {
            group2[`${keyWOId}_${item?.name}`] = {
                "$sum": cond(`${valueDDSelect}.${keyReq2Cond}`, item?.value)
            }
        }
    })

    let lookup2 = (_.includes(foreignNames, valueDDSelect)) ? {
        from: valueDDSelect || "",
        localField: (fKey) ? `${valueDDSelect}.${addKey}` : `_id.${addKey}`,
        foreignField: "_id",
        as: "data_foreign2"
    } : {}

    let lookup3 = {}

    const unwind3 = (_.includes(foreignNames, valueDDSelect)) ? {
        path: "$data_foreign2",
        preserveNullAndEmptyArrays: true
    } : {}

    let unwind4 = {}
    let unwind5 = {}
    let unwind6 = {}

    let group3 = {
        "_id": `$_id.${dataRequired?.selectObj?.key}`
    }

    let group4 = {}
    let group5 = {}
    let group6 = {}

    let pushGrp3 = {}

    if (dateOnlyMode) {
        pushGrp3 = (_.includes(foreignNames, valueDDSelect)) ? {
            _id: (fKey) ? `$_id.${addKey}` : `$data_foreign2._id`,
            [keyWOId]: {
                $ifNull: [
                    (fKey) ? `$_id.${addKey}` : `$data_foreign2.${keyWOId}`,
                    "Unknown"
                ]
            }
        } : {
            [keyWOId]: {
                $ifNull: [
                    `$_id.${keyWOId}`,
                    "Unknown"
                ]
            }
        }
    } else {
        pushGrp3 = (_.includes(foreignNames, valueDDSelect)) ? {
            _id: (fKey) ? `$_id.${addKey}` : `$data_foreign2._id`,
            [keyWOId]: {
                $ifNull: [
                    (fKey) ? `$_id.${addKey}` : `$data_foreign2.${keyWOId}`,
                    "Unknown"
                ]
            },
            name: {
                $ifNull: [
                    `$data_foreign2.name`,
                    "Unknown"
                ]
            }
        } : {
            [keyWOId]: {
                $ifNull: [
                    `$_id.${keyWOId}`,
                    "Unknown"
                ]
            },
            name: {
                $ifNull: [
                    `$${valueDDSelect}.name`,
                    "Unknown"
                ]
            }
        }
    }


    for (const grp2Key in group2) {
        if (_.includes(grp2Key, firstGroup)) {
            group3[grp2Key] = {$first: `$${grp2Key}`}
        } else if (grp2Key !== "_id") {
            pushGrp3[grp2Key] = `$${grp2Key}`
        }
    }

    if (!_.isEmpty(getThirdLevel?.key)) {
        const pushGrp3rdLvl = (_.includes(foreignNames, valueDDSelect)) ? {
            [keyWOId]: {
                $ifNull: [
                    `$data_foreign2.${keyWOId}`,
                    "Unknown"
                ]
            }
        } : {}

        group2[keyWOId] = {
            $first: {
                $ifNull: [
                    `$${valueDDSelect}.${keyWOId}`,
                    "No available"
                ]
            }
        }

        group2[valueDDSelect] = {
            $push: {
                ...pushGrp3rdLvl,
                [dataRequired.requiredKey]: `$${valueDDSelect}.${dataRequired.requiredKey}`,
                [getThirdLevel?.value]: `$${valueDDSelect}.${getThirdLevel?.value}`
            }
        }

        if (_.includes(foreignNames, valueDDSelect)) {
            unwind4 = {
                path: `$${valueDDSelect}`,
                preserveNullAndEmptyArrays: true
            }
        }

        // .${getThirdLevel?.value}
        unwind5 = {
            path: `$${valueDDSelect}`,
            preserveNullAndEmptyArrays: true
        }

        lookup3 = {
            from: getThirdLevel?.value || "",
            localField: `${valueDDSelect}.${getThirdLevel?.value}`,
            foreignField: "_id",
            as: "data_foreign3"
        }

        unwind6 = {
            path: "$data_foreign3",
            preserveNullAndEmptyArrays: true
        }

        for (const grpKey2 in group2) {
            if (grpKey2 !== "_id") {
                if (grpKey2 !== objSelectDrillDown?.value) {
                    group4[grpKey2] = {$first: `$${grpKey2}`}
                }
            } else {
                group4._id = {
                    [firstKey]: `$_id.${firstKey}`,
                    [addKey]: (_.includes(foreignNames, valueDDSelect)) ? `$_id.${addKey}` : `$${addKey}`,
                    [`${getThirdLevel?.value}_id`]: '$data_foreign3._id'
                }
            }
        }

        group4[keyWOId] = {$first: (_.includes(foreignNames, valueDDSelect)) ? `$${valueDDSelect}.${keyWOId}` : `$${keyWOId}`}

        const getNameDF3 = `${getThirdLevel?.value}`
        const obj3rdLevel = {}

        obj3rdLevel[getNameDF3] = {
            $first: {
                $ifNull: [
                    `$data_foreign3.${getThirdLevel?.key}`,
                    `Unknown`
                ]
            }
        }
        obj3rdLevel[`${getThirdLevel?.value}_total`] = {$sum: 1}

        dataRequired.optionsCond.map(item => {
            const cond = funcCond[item?.func];
            if (cond) {
                obj3rdLevel[`${getThirdLevel?.value}_${item?.name}`] = {
                    "$sum": cond(`${valueDDSelect}.${keyReq2Cond}`, item?.value)
                }
            }
        })

        group4 = {
            ...group4,
            ...obj3rdLevel
        }

        for (const grpKey2 in group2) {
            if (grpKey2 !== "_id") {
                if (grpKey2 !== objSelectDrillDown?.value) {
                    group5[grpKey2] = {$first: `$${grpKey2}`}
                }
            } else {
                group5._id = {
                    [firstKey]: `$_id.${firstKey}`,
                    [addKey]: `$_id.${addKey}`
                }
            }
        }

        group5[keyWOId] = {$first: `$${keyWOId}`}

        const objPush = {}
        for (const key3rdLevel in obj3rdLevel) {
            objPush[key3rdLevel] = `$${key3rdLevel}`
        }

        group5[getThirdLevel?.value] = {
            $push: objPush
        }

        const pushObj4 = {}
        for (const grpKey4 in group4) {
            if (grpKey4 !== "_id") {
                if (!_.includes(grpKey4, keyWOId) && !_.includes(grpKey4, getThirdLevel?.value)) {
                    if (dateOnlyMode) {
                        group6[grpKey4] = {$first: `$${grpKey4}`}
                    } else {
                        group6["name"] = { $first: "$name" }
                    }
                } else if (!_.includes(grpKey4, getThirdLevel?.value)) {
                    pushObj4[grpKey4] = `$${grpKey4}`
                }
            } else {
                group6._id = `$_id.${firstKey}`
            }
        }
        pushObj4[getThirdLevel?.value] = `$${getThirdLevel?.value}`
        group6[valueDDSelect] = {$push: pushObj4}
    }

    let setGrpPro = {}
    if (_.isEmpty(getThirdLevel?.key)) {
        group3[valueDDSelect] = {$push: pushGrp3}
        setGrpPro = group3
        if (fKey) {
            group4 = group3
            group3 = group2
            group2 = {}
        }
    } else {
        if (_.includes(foreignNames, valueDDSelect)) lookup2.localField = `${valueDDSelect}.${addKey}`
        group3 = group2
        group2 = {}
        setGrpPro = group6
    }

    const project2 = {}

    for (const keyPro in setGrpPro) {
        project2[keyPro] = 1
    }

    const additionalObjAg = {
        unwind2,
        group2,
        lookup2,
        unwind3,
        group3,
        unwind4,
        unwind5,
        lookup3,
        unwind6,
        group4,
        group5,
        group6,
        project2
    }

    return {
        firstData: builtAggData,
        secondData: additionalObjAg
    }
}
