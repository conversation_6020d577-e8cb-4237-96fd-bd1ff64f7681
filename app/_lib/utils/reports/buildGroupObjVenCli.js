import * as funcCond from "../conditionals/index.js";
import _ from "lodash";

export const buildGroupObjVenCli = (
    keysProAgg,
    selObj,
    secKey,
    options,
    matchKey = null,
    nameMatch = null
) => {
    if (_.isEmpty(selObj)) return {}

    const project0 = createProjectStage(keysProAgg)
    const group = {}
    const project = {}

    const isMatchedKey = matchKey && selObj?.key === matchKey

    group._id = isMatchedKey
        ? null
        : { $ifNull: [`$${selObj?.key}`, selObj?.replace] }

    options.forEach(item => {
        const cond = funcCond[item?.func]
        if (cond) {
            group[item?.name] = {
                "$sum": cond(secKey, item?.value)
            }
            project[item?.name] = 1
        }
    })

    group.total = { "$sum": 1 }
    project.total = 1
    project._id = 0

    if (!isMatchedKey) {
        project[selObj?.key] = "$_id"
    }

    if (isMatchedKey && nameMatch) {
        project[selObj?.show] = { $literal: nameMatch }
    }

    return {
        project0,
        group,
        lookup: null,
        unwind: null,
        project
    }
}

function createProjectStage(fields) {
    const projectStage = {}
    fields.forEach(field => {
        projectStage[field] = 1
    })
    return projectStage
}
