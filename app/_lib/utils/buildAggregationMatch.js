import _ from "lodash";

export const buildAggregationMatch = (optSel, idToMatch) => {
    if (_.isEmpty(optSel)) return {}

    let match = _.isBoolean(idToMatch) ? {
        $or: [
            {[`${optSel?.f_key2}_id`]: ""},
            {[`${optSel?.f_key2}_id`]: null},
            {[`${optSel?.f_key2}_id`]: {$exists: false}}
        ]
    } : {
        [`${optSel?.f_key2}_id`]: idToMatch
    }

    if (_.includes(optSel?.local_key, "transferred_to") && _.isBoolean(idToMatch)) {
        match = {
            ...match,
            [optSel?.local_key]: { $ne: "" }
        }
    }

    const keyArray = (_.includes(optSel?.local_key, "_id")) ?
        _.replace(optSel?.local_key, '_id', '') :
        optSel?.local_key
    const group = {
        _id: {$ifNull: [`$${optSel?.local_key}`, "Unknown"]},
        [`${keyArray}_total`]: {$sum: 1}
    }

    const project = {}

    for (const grpKey in group) {
        if (grpKey === "_id") {
            project[grpKey] = 0
            project[keyArray] = "$_id"
        } else {
            project[grpKey] = 1
        }
    }

    const sort = {[`${keyArray}`]: 1}

    return {match, group, project, sort}
}