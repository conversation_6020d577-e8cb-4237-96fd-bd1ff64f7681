export const buildActionsTableColumn = (tableColumns, tableName, roleUser) => {
    const actionColumn = {
        accessorKey: 'actions',
        header: 'Actions',
        config: {
            editable: false,
            show: true,
            component: 'Edit'
        }
    }
    tableColumns.push(actionColumn)
    if (tableName === "clients" && roleUser === "admin") {
        tableColumns.push({
            accessorKey: 'rules',
            header: 'Rules Postback',
            config: {
                editable: false,
                show: true,
                component: 'ShowRules'
            }
        })
    }
    return tableColumns
}
