import {dateFilters} from "@/app/_lib/utils/filtersFunc/dateFilters.js";
import {buildNameObjFilter} from "@/app/_lib/utils/filtersFunc/buildNameObjFilter.js";
import {buildObjFilter} from "@/app/_lib/utils/filtersFunc/buildObjFilter.js";
import _ from "lodash";

export const buildDefaultFiltersTable = (configColumns) => {
    let defaultFilters = {}
    configColumns.forEach(objConf => {
        if (!_.isEmpty(objConf?.config?.defaultFilter)) {
            switch (objConf?.filterComponent) {
                case 'DateFilterComponent':
                    const valuesDate = dateFilters(objConf?.config?.defaultFilter)
                    Object.assign(defaultFilters, {
                        [buildNameObjFilter(objConf)]: buildObjFilter(valuesDate, objConf?.config?.defaultFilter)
                    })
                    break;
                case 'TextFilterComponent':
                case 'RangeNumberFilterComponent':
                case 'NullableComponent':
                case 'SelectFilterComponent':
                    Object.assign(defaultFilters, {
                        [buildNameObjFilter(objConf)]: objConf?.config?.defaultFilter
                    })
                    break;
            }
        }
    })

    return defaultFilters;
}
