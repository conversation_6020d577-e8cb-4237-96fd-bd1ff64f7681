import {getTableConfigurationByTableName} from "@/app/_lib/nova/functions/getTableConfigurationByTableName.js";
import {buildTableColumnsWithDistinctValues} from "@/app/_lib/utils/buildTableColumnsWithDistinctValues.js";
import {buildExternalSelectTableColumns} from "@/app/_lib/utils/buildExternalSelectTableColumns.js";
import {buildDefaultFiltersTable} from "@/app/_lib/utils/buildDefaultFiltersTable.js";
import {buildActionsTableColumn} from "@/app/_lib/utils/buildActionsTableColumn.js";
import {removeStyleString} from "@/app/_components/utils/removeStyleString.js";
import _ from "lodash";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession.js";
import {headers} from "next/headers";
import {removeLeadingSlash} from "@/app/_components/utils/removeLeadingSlash.js";
import {getToolsByRoute} from "@/app/_components/utils/getToolsByRoute.js";

export const getInitialConfigurationPage = async (tableName) => {
    const user = await getUserSession()
    const {metadata, role, timezone, dev} = user || {metadata: null, role: null, timezone: null}
    const jsonMetadata = metadata ? JSON.parse(metadata) : null
    const pathname = getPathname()

    const buildObjMetadata = {
        role: role ?? null,
        dev: dev ?? false,
        timezone: timezone ?? "America/Chicago",
        menus: jsonMetadata?.menus ?? [],
        table_keys: jsonMetadata?.table_keys ?? null,
        restrictions: jsonMetadata?.restrictions ?? null
    }

    const pageSize = 5
    const page = 0

    const tableConfiguration = await getTableConfigurationByTableName(tableName);
    let tableColumns = tableConfiguration?.configuration
    const globalSearchConfig = tableConfiguration?.global_search
    const summaryReportConf = tableConfiguration?.summary_conf
    const matchRepConf = tableConfiguration?.match_rep_conf
    const tools = tableConfiguration?.tools

    const nameUnStyleTable = removeStyleString(tableName)
    const restrictedKeys = buildObjMetadata?.table_keys?.[nameUnStyleTable] ?? []

    const toolsRestricted = buildObjMetadata.menus
        ?.find(menu => menu.id === nameUnStyleTable)
        ?.tools?.map(toolObj => toolObj.tool) ?? []

    if (nameUnStyleTable === 'call_campaigns') {
        toolsRestricted.push('update')
        toolsRestricted.push('update_concurrent_call')
        tableName = 'campaigns'
    }
    tableColumns = await buildTableColumnsWithDistinctValues(tableColumns, tableName)
    tableColumns = await buildExternalSelectTableColumns(tableColumns, tableName, buildObjMetadata)
    const pathWithoutSlash = removeLeadingSlash(pathname);
    const toolsAvailable = getToolsByRoute(pathWithoutSlash, jsonMetadata?.menus ?? []);
    const isEditable = tableConfiguration?.editable_data && (toolsAvailable.includes("update") || toolsAvailable.includes("edit") || toolsRestricted.includes("update") || toolsRestricted.includes("edit"));
    if (isEditable) {
        tableColumns = await buildActionsTableColumn(tableColumns, tableName, role)
    }
    if (!_.includes(restrictedKeys, "*") && restrictedKeys.length > 0) {
        tableColumns = tableColumns.filter(colData => {
            if (
                tableConfiguration?.check_keys_restricts &&
                (_.includes(restrictedKeys, "vendor") || _.includes(restrictedKeys, "client")) &&
                (_.includes(colData.accessorKey, "vendor") || _.includes(colData.accessorKey, "client"))
            ) {
                const getKeyName = _.includes(colData.accessorKey, "vendor") ? "vendor" : "client"
                return _.includes(restrictedKeys, getKeyName)
            } else {
                return _.includes(restrictedKeys, colData.accessorKey)
            }
        })
    }

    const defaultFilters = buildDefaultFiltersTable(tableColumns, buildObjMetadata)

    const data = []
    const tableConfig = {
        data: JSON.stringify(data),
        tableName,
        page,
        pageSize,
        totalPages: 0,
        count: 0,
        tableColumns,
        sort: {},
        summaryReportConf,
        matchRepConf,
        toolsRestricted,
        userMetadata: buildObjMetadata,
        tools,
    }

    return {globalSearchConfig, defaultFilters, tableConfig, restrictedKeys, tableConfiguration}
}


const getPathname = () => {
    const headersList = headers();
    const pathname = headersList.get('x-pathname');
    if (pathname) {
        return pathname;
    }
    const xUrl = headersList.get('x-url');
    const referer = headersList.get('referer');
    const url = xUrl || referer || '';

    try {
        const urlObj = new URL(url);
        return urlObj.pathname;
    } catch (e) {
        console.error('Error al parsear URL:', e);
        return '';
    }
}
