import { ObjectId } from "mongodb";
import authConnect from "@/app/_lib/mongo/auth/authConnect.js";
// import { getStripeClient } from "./getStripeClient";
import Stripe from 'stripe';

 function getStripeClient(): Stripe {
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    if (!stripeSecretKey) {
        throw new Error('STRIPE_SECRET_KEY environment variable is not configured');
    }

    return new Stripe(stripeSecretKey, {
        apiVersion: "2025-06-30.basil",
        typescript: true,
    });
}
export interface StripeCustomerResult {
    stripeCustomerId: string;
    customerCreated: boolean;
    error?: string;
}
const stripeClient = getStripeClient();
export async function getStripeCustomer(stripeCustomerId: string | undefined | null) {
    try {
        if (!stripeCustomerId) {
            console.log(":advertencia: No Stripe customer ID provided");
            return null;
        }
        const customer = await stripeClient.customers.retrieve(stripeCustomerId);
        return customer;
    } catch (error: any) {
        console.error(":x: Error retrieving Stripe customer:", error);
        return null;
    }
}
export async function ensureStripeCustomer(
    userId: string,
    email: string,
    name?: string
): Promise<StripeCustomerResult> {
    try {
        // Checa si el usuario ya tiene un customerId de Stripe
        const {db} = await authConnect();
        const user = await db.collection("user").findOne({_id: new ObjectId(userId)});

        if (user?.stripeCustomerId) {
            return {
                stripeCustomerId: user.stripeCustomerId,
                customerCreated: false
            };
        }
        // Crea un nuevo customerId de Stripe
        const customer = await stripeClient.customers.create({
            email: email,
            name: name || email.split('@')[0],
            metadata: {
                userId: userId,
                source: 'better-auth-utility'
            }
        });
        // Actualiza el usuario en la base de datos con el nuevo ID de Stripe
        await db.collection("user").updateOne(
            {_id: new ObjectId(userId)},
            {$set: {stripeCustomerId: customer.id}}
        );
        return {
            stripeCustomerId: customer.id,
            customerCreated: true
        };
    } catch (error: any) {
        console.error(':x: Error ensuring Stripe customer:', error);
        return {
            stripeCustomerId: '',
            customerCreated: false,
            error: error.message
        };
    }
}









