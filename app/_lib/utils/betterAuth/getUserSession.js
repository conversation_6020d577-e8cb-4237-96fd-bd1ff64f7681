import {auth} from "@/app/_lib/auth/auth";
import {headers} from "next/headers";

export const getUserSession = async () => {
    const rawHeaders = headers();
    const cookie = rawHeaders.get('cookie');

    if (!cookie || !cookie.includes('better-auth.session_token')) {
        return null;
    }

    const headerObj = {};
    for (const [key, value] of rawHeaders.entries()) {
        headerObj[key] = value;
    }

    try {
        const userData = await auth.api.getSession({headers: headerObj});
        return userData?.user ?? null;
    } catch (e) {
        console.error('[getUserSession] Error:', e);
        return null;
    }
}
