export function removeTaskNodes(originalItems, taskNodes, newNodeId) {
    const removedScriptIds = [];
    const idsToRemove = new Set(taskNodes.map(item => item._id));

    let filteredItems = originalItems.filter(item => {
        const shouldRemove =
            idsToRemove.has(item._id) ||
            (item.intent === 'endingCall' && item.origin === 'ending');

        if (shouldRemove && item.responses) {
            removedScriptIds.push(...item.responses.map(response => response.scriptId));
        }

        return !shouldRemove;
    });

    filteredItems = filteredItems.map(item => {
        const updatedResponses = item.responses?.filter(response => {
            if (idsToRemove.has(response.scriptId)) {
                removedScriptIds.push(response.scriptId);
            }
            return !idsToRemove.has(response.scriptId);
        }) || [];

        return {...item, responses: updatedResponses};
    });

    if (removedScriptIds.length > 0) {
        // TODO: check when is necessary to add the new node
        const firstNode = filteredItems.find(item => item.responses.length === 0);
        if (firstNode) {
            firstNode.responses.push({
                "match": "Match objective",
                "scriptId": newNodeId,
                "disposition": "Incomplete"
            });
        }
    }

    return filteredItems;
}
