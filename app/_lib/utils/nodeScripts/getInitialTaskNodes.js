import {generateId} from "@/app/_lib/utils/generateId.js";

export function getInitialTaskNodes(newTaskId, callCampaignId) {
    const endNodeId = generateId()
    return [
        {
            "_id": newTaskId,
            "callCampaignId": callCampaignId,
            "version": 1,
            "base": false,
            "match": false,
            "intent": "askingQualificationQuestion",
            "objective": "find out if client is do something for set transfer",
            "examples": "",
            "notes": "PUT NOTES HERE",
            "responses": [
                {
                    "match": "Math for DNC call",
                    "scriptId": endNodeId,
                    "disposition": "DNQ"
                }
            ],
            "disposition": "Incomplete",
            "origin": "parent"
        },
        {
            "_id": endNodeId,
            "callCampaignId": callCampaignId,
            "version": 1,
            "base": false,
            "match": false,
            "action": "endingCall",
            "intent": "endingCall",
            "objective": "Thank Client for their time and wish them a great day.",
            "examples": "",
            "notes": "",
            "disposition": "previous",
            "origin": "ending"
        }
    ]
}