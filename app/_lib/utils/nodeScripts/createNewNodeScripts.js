import {generateId} from "@/app/_lib/utils/generateId.js";

export function createNewNodeScripts(items, newCallCampaignId) {
    const idMap = {};
    items.forEach(item => {
        idMap[item._id] = generateId();
    });

    return items.map(item => {
        const newId = idMap[item._id];
        return {
            ...item,
            _id: newId,
            callCampaignId: newCallCampaignId,
            responses: item.responses?.map(response => ({
                ...response,
                scriptId: idMap[response.scriptId] || response.scriptId
            })) || []
        };
    });
}