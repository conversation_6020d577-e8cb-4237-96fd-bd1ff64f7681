export function findRequestNodes(campaignData, greetingsData, terminateData) {
    const campaignDataMap = new Map();
    campaignData.forEach(item => {
        campaignDataMap.set(String(item._id).trim(), item);
    });

    const disData = campaignData.filter((item) =>
        !greetingsData.some(greeting => greeting._id === item._id) &&
        !terminateData.some(terminate => terminate._id === item._id) &&
        item.base === true
    );

    const inheritedItems = [...disData];
    const processedIds = new Set(disData.map((item) => String(item._id).trim()));

    disData.forEach((item) => {
        if (item.responses && item.responses.length > 0) {
            item.responses.forEach((response) => {
                const scriptId = String(response.scriptId).trim();
                if (scriptId && scriptId !== "currentScript" && scriptId !== "previousScript") {
                    const inheritedItem = campaignDataMap.get(scriptId);
                    if (inheritedItem && !processedIds.has(scriptId)) {
                        inheritedItems.push(inheritedItem);
                        processedIds.add(scriptId);
                    }
                }
            });
        }
    });

    return inheritedItems;
}