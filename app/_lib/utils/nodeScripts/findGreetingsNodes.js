export const findGreetingsNodes = (data) => {
    const greetingNodes = data.filter(item =>
        item.match === 'greeting' ||
        item.match === 'greetingNoName' ||
        item.intent?.includes('greeting')
    );

    const processedGreetingIds = new Set();
    const greetingsWithChildren = [...greetingNodes];

    greetingNodes.forEach((greeting) => {
        const currentNodeId = String(greeting._id).trim();
        if (!processedGreetingIds.has(currentNodeId) && greeting.responses) {
            greeting.responses.forEach((response) => {
                const childNodeId = String(response.scriptId).trim();
                if (childNodeId && childNodeId !== "currentScript" && childNodeId !== "previousScript") {
                    const childNode = data.find((item) => item._id === childNodeId);
                    if (childNode && !processedGreetingIds.has(childNodeId)) {
                        greetingsWithChildren.push(childNode);
                        processedGreetingIds.add(childNodeId);
                    }
                }
            });
        }
        processedGreetingIds.add(currentNodeId);
    });

    return greetingsWithChildren;
}