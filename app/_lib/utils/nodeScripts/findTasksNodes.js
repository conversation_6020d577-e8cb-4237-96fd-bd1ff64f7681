import _ from "lodash";

export function findTasksNodes(campaignData, greetingsData, dispositionData) {
    const tasksData = campaignData.filter((item) =>
        !greetingsData.some((greeting) => greeting._id === item._id && !greeting.origin) &&
        !dispositionData.some((disposition) => disposition._id === item._id)
    )

    const endingCallNodes = _.filter(campaignData, (item) =>
        item?.intent === 'endingCall' && (item?.origin === 'ending' || item?.origin === 'orphan')
    );

    return [...tasksData, ...endingCallNodes];
}
