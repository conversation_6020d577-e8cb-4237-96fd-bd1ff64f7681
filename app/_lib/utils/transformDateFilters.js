import {DateTime} from "luxon";

export const transformDateFilters = (filters, timezone) => {
    const transformedFilters = {...filters};
    if (transformedFilters) {
        Object.keys(filters).forEach(key => {
            const filterValue = filters[key];
            if (filterValue['$gte'] && filterValue['$lte']) {
                try {
                    const start = DateTime.fromISO(filterValue['$gte'], {zone: timezone});
                    const end = DateTime.fromISO(filterValue['$lte'], {zone: timezone});
                    if (start.isValid && end.isValid) {
                        const startUTC = start.startOf('day').toUTC().toJSDate();
                        const endUTC = end.endOf('day').toUTC().toJSDate();
                        transformedFilters[key] = {
                            '$gte': startUTC,
                            '$lte': endUTC,
                        };
                    }
                } catch (error) {
                    console.error(`Error processing date for ${key}:`, error);
                }
            }
        });
    }
    return transformedFilters;
}
