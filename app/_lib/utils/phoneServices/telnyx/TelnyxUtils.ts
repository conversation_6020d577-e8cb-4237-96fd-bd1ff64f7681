import Telnyx from "telnyx";
import {retry, tryit} from "radash";
import {params} from "@ampt/sdk";

export class TelnyxUtils {

    private static apiKey: string = params('TELNYX_API_KEY') || '';
    private static connectionId: string = params('TELNYX_SIP_ID') || '';
    private static telnyx = new Telnyx(this.apiKey);


    static initialize(telnyx, connectionId) {
        this.telnyx = telnyx;
        this.connectionId = connectionId
    }

    static async searchAvailableNumbers() {
        const retryOptions = {times: 3, delay: 1000}
        const [error, response] = await tryit(async () => {
            return await retry(retryOptions, async () => {
                const result = await this.telnyx.availablePhoneNumbers.list({
                    "filter[country_code]": "US",
                    "filter[limit]": 1,
                    "filter[features]": ["voice"],
                    "filter[phone_number_type]": "local"
                });
                if (!result) {
                    console.error("No response received from Telnyx API");
                    throw new Error("No response received from Telnyx API");
                }
                if (!result.data || !Array.isArray(result.data)) {
                    console.error("Invalid data format in Telnyx response ", result);
                    throw new Error("Invalid data format in Telnyx response");
                }

                if (result.data.length === 0) {
                    console.error("No phone numbers found. ", result.data);
                    throw new Error("No phone numbers found matching the criteria");
                }

                if (!result.data[0].phone_number || result.data[0].phone_number === '') {
                    console.error("Phone number is missing or empty in the response ", result.data[0].phone_number);
                    throw new Error("Phone number is missing or empty in the response");
                }
                return result;
            });
        })();
        if (error) {
            console.error("Error searching for available numbers after retries:", error);
            throw error;
        }
        return response.data[0].phone_number;
    }

    static async purchaseNumber(phoneNumber: string) {
        if (!phoneNumber || phoneNumber.trim() === '') {
            throw new Error("Phone number is required for purchase");
        }
        const [error, response] = await tryit(async () => {
            return await retry({times: 3, delay: 1000}, async () => {
                const result = await this.telnyx.numberOrders.create({
                    phone_numbers: [{"phone_number": phoneNumber}],
                    connection_id: this.connectionId
                });
                if (!result) {
                    throw new Error("No response received from Telnyx API");
                }
                if (!result.data) {
                    console.error("No data found in the response ", result);
                    throw new Error(`Purchase order not found in the response. Response: ${JSON.stringify(result)}`);
                }
                if (!result.data.phone_numbers || result.data.phone_numbers.length === 0) {
                    console.error("No phone numbers found in the response ", result.data.phone_numbers);
                    throw new Error("No phone numbers found in the response");
                }
                return {
                    numberOrderId: result.data.id,
                    numberOrderPhone: result.data.phone_numbers[0].id,
                    status: result.data.status
                };
            });
        })();

        if (error) {
            console.error("Error purchasing number after retries:", error);
            throw error;
        }
        return response;
    }

    static async releasePhoneNumber(id: string) {
        if (!id || id.trim() === '') {
            throw new Error("Phone number id is required for release");
        }
        const [error, response] = await tryit(async () => {
            return await retry({times: 3, delay: 1000}, async () => {
                const result = await this.telnyx.phoneNumbers.del(id)
                if (!result) {
                    console.error("No response received from Telnyx API when releasing phone number");
                    throw new Error("No response received from Telnyx API when releasing phone number");
                }
                if (!result.data) {
                    console.error("No data found in the response ", result);
                    throw new Error(`Purchase order not found in the response. Response: ${JSON.stringify(result)}`);
                }
                if (!result.data.status || result.data.status !== 'deleted') {
                    console.error("Status is not deleted in the response ", result.data.status);
                    throw new Error("Status is not deleted in the response");
                }
                return {
                    operationId: result.data.id,
                };
            });
        })();

        if (error) {
            console.error("Error releasing number after retries:", error);
            throw error;
        }

        return response;
    }

    static async findPhoneNumber(phoneNumber: string) {
        if (!phoneNumber || phoneNumber.trim() === '') {
            throw new Error("Phone number is required for release");
        }
        const [error, response] = await tryit(async () => {
            return await retry({times: 3, delay: 1000}, async () => {
                const result = await this.telnyx.phoneNumbers.list({"filter[phone_number]": phoneNumber})
                if (!result || !result.data || result.data.length === 0) {
                    throw new Error("Phone number not found");
                }
                return {id: result.data[0].id, phoneNumber: result.data[0].phone_number};
            })
        })()
        if (error) {
            console.error("Error finding number after retries:", error);
            throw error;
        }
        return response;
    }

}


