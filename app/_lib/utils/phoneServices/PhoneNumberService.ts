import {TelnyxUtils} from "@/app/_lib/utils/phoneServices/telnyx/TelnyxUtils";

export class PhoneNumberService {

    /**
     * Searches and purchases an available phone number via Telnyx.
     *
     * @returns {Promise<{ phoneNumber: string }>} The purchased phone number object.
     * @throws {Error} If the search or purchase fails.
     */
    static async purchasePhoneNumber() {
        console.log(`purchasePhoneNumber#Searching available numbers...`);
        const phoneNumber = await TelnyxUtils.searchAvailableNumbers();
        console.log(`purchasePhoneNumber#Purchasing number ${phoneNumber}...`);
        const purchaseOrder = await TelnyxUtils.purchaseNumber(phoneNumber);
        console.log(`purchasePhoneNumber#Purchase order created: ${purchaseOrder.numberOrderId}`);
        return {phoneNumber};
    }

    /**
     * Releases a previously purchased phone number via Telnyx.
     *
     * @param {string} phoneNumber - The phone number to be released (e.g., "+17632989345").
     * @returns {Promise<{ operationId: string }>} The release operation result.
     * @throws {Error} If the number is not found or the release fails.
     */
    static async releasePhoneNumber(phoneNumber: string) {
        console.log(`releasePhoneNumber#Releasing number ${phoneNumber}...`);
        const {id} = await TelnyxUtils.findPhoneNumber(phoneNumber);
        const releaseOrder = await TelnyxUtils.releasePhoneNumber(id);
        console.log(`releasePhoneNumber#Release order created: ${releaseOrder.operationId}`);
        return releaseOrder;
    }
}
