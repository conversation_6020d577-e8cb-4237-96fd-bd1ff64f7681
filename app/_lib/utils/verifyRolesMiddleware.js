import {checkRestrictions} from "@/app/_lib/nova/functions/better-auth/checkRestrictions.js";
import {buildRestrictionsTables} from "@/app/_lib/nova/functions/utils/buildRestrictionsTables.js";

export const verifyRolesMiddleware = async (payload, method) => {
    let {collection, filters, authMetadata} = payload;
    if (!collection) return {error: 'Collection not found', status: 400}
    // console.log(collection)
    const {response, error, status} = await checkRestrictions(collection, authMetadata);
    if (status !== 200 && status !== 201) return {error, status}
    payload.filters = await buildRestrictionsTables(collection, response.restrictions.vendors, response.restrictions.clients, filters);
    return (method) ? await method(payload) : payload;
}
