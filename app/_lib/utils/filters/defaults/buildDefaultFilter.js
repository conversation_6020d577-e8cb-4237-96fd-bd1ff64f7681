import {getDefaultIsoWeek} from "@/app/_components/utils/getDefaultIsoWeek.js";

export const buildDefaultFilter = (component, hasSearchParams) => {
    if (hasSearchParams) return {};
    switch (component) {
        case  "InvoiceIsoWeekFilter":
            const currentDate = getDefaultIsoWeek(null);
            const currentWeek = currentDate.weekNumber.toString();
            return {
                "week-inF": {
                    "value": [currentWeek],
                    "filterType": "inF",
                    "label": "week"
                }
            }
        default:
            return null;
    }
}


