import {getDistinctValues} from "@/app/_lib/nova/functions/getDistinctValues";
import _ from "lodash";

export const buildTableColumnsWithDistinctValues = async (tableColumns, tableName) => {
    const filteredData = tableColumns.filter(item => (
        (item.filterComponent === "SelectFilterComponent" || item.filterComponent === "MultiSelectFilterComponent") &&
        item.config.select === "dynamic"
    ));
    const dynamicKeys = filteredData.map(item => item.accessorKey);
    const distinctPromises = dynamicKeys.map(key => getDistinctValues([key], tableName));
    const distinctResults = await Promise.all(distinctPromises);
    if (distinctResults && !_.isEmpty(distinctResults)) {
        distinctResults.forEach((values, index) => {
            const key = dynamicKeys[index];
            const selectOptions = values[key].map(item => ({label: item, value: item}));
            overrideSelectOptions(tableColumns, key, selectOptions);
        });
    }
    return tableColumns;

}

function overrideSelectOptions(tableColumns, key, selectOptions) {
    const objToUpdateIndex = tableColumns.findIndex(obj => obj.accessorKey === key);
    if (objToUpdateIndex !== -1) {
        tableColumns[objToUpdateIndex].config.selectOptions = selectOptions;
    }
}
