export const convertDateStringsToObjects = (obj) => {
    if (obj === null || obj === undefined) return obj;
    if (typeof obj === 'string') {
        const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
        if (isoDateRegex.test(obj)) {
            return new Date(obj);
        }
        return obj;
    }
    if (Array.isArray(obj)) {
        return obj.map(convertDateStringsToObjects);
    }
    if (typeof obj === 'object') {
        const converted = {};
        for (const [key, value] of Object.entries(obj)) {
            converted[key] = convertDateStringsToObjects(value);
        }
        return converted;
    }

    return obj;
};
