export const hasFilters = (filters) => {
    let hasFilter = false;
    for (const [key, value] of Object.entries(filters)) {
        const {filterType, value: filterValue} = value;
        let tempValue = filterValue;
        if (typeof filterValue === 'object') {
            tempValue = JSON.stringify(filterValue);
        } else if (typeof tempValue === 'number' && tempValue !== -1) {
            hasFilter = true;
        } else if (typeof tempValue === 'string' && !tempValue.includes('NA_FILTER')) {
            hasFilter = true;
            break;
        }
    }
    return hasFilter;
}
