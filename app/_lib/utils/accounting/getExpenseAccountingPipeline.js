import {transformDateFilters} from "@/app/_lib/utils/transformDateFilters.js";
import {getHintByCollection} from "@/app/_lib/utils/accounting/getHintByCollection.js";

export const getExpenseAccountingPipeline = (accountingType, filters, collection) => {
    const mongoFilters = transformDateFilters(filters)
    let hint = getHintByCollection(collection);
    hint = {...hint, invoice_expense_id: 1}
    const pipeline = [
            {
                $match: mongoFilters
            },
            {
                $group: {
                    _id: {
                        vendor_id: "$vendor_id",
                        "pubid": collection === 'transfers' ? "$pub_id" : "$pubid",
                        campaign_key: "$campaign_key",
                        client_id: "$client_id"
                    },
                    assign: {
                        $sum: {
                            $cond: [
                                {
                                    $or: [
                                        {$eq: ["$invoice_expense_id", null]},
                                        {$eq: ["$invoice_expense_id", ""]},
                                        {
                                            $not: {
                                                $ifNull: ["$invoice_expense_id", false]
                                            }
                                        }
                                    ]
                                },
                                0,
                                1
                            ]
                        }
                    },
                    unassign: {
                        $sum: {
                            $cond: [
                                {
                                    $or: [
                                        {$eq: ["$invoice_expense_id", null]},
                                        {$eq: ["$invoice_expense_id", ""]},
                                        {
                                            $not: {
                                                $ifNull: ["$invoice_expense_id", false]
                                            }
                                        }
                                    ]
                                },
                                1,
                                0
                            ]
                        }
                    },
                    invoice_expense_ids: {$addToSet: "$invoice_expense_id"}
                }
            },
            {
                $lookup: {
                    from: "vendors",
                    localField: "_id.vendor_id",
                    foreignField: "_id",
                    as: "vendorInfo"
                }
            },
            {$unwind: "$vendorInfo"},
            {
                $lookup: {
                    from: "clients",
                    localField: "_id.client_id",
                    foreignField: "_id",
                    as: "clientInfo"
                }
            },
            {$unwind: "$clientInfo"},
            {
                $group: {
                    _id: {
                        vendor_id: "$_id.vendor_id",
                        campaign_key: "$_id.campaign_key",
                        client_id: "$_id.client_id"
                    },
                    vendor: {$first: "$vendorInfo.vendor"},
                    client: {$first: "$clientInfo.client"},
                    pubids: {
                        $push: {
                            pubid: "$_id.pubid",
                            assign: "$assign",
                            unassign: "$unassign",
                            invoice_expense_ids: {
                                $cond: [
                                    {
                                        $or: [
                                            {$eq: [{$size: "$invoice_expense_ids"}, 0]},
                                            {$in: [null, "$invoice_expense_ids"]}
                                        ]
                                    },
                                    [],
                                    "$invoice_expense_ids"
                                ]
                            }
                        }
                    }
                }
            },
            {
                $group: {
                    _id: {
                        vendor_id: "$_id.vendor_id",
                        client: "$client",
                        client_id: "$_id.client_id",
                    },
                    vendor: {$first: "$vendor"},
                    campaign_keys: {
                        $push: {
                            campaign_key: "$_id.campaign_key",
                            pubids: "$pubids"
                        }
                    }
                }
            },
            {
                $group: {
                    _id: "$_id.vendor_id",
                    vendor: {$first: "$vendor"},
                    result: {
                        $push: {
                            client: "$_id.client",
                            campaign_keys: "$campaign_keys",
                            client_id: "$_id.client_id",
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    vendor_id: "$_id",
                    vendor: 1,
                    result: 1
                }
            }
        ]


    ;
    return {pipeline, hint}
}
