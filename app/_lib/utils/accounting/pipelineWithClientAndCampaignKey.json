[
  {
    $match: {
      completed_date: {
        $gte: ISODate("2024-10-28T06:00:00.000Z"),
        $lte: ISODate("2024-11-04T05:59:59.999Z")
      },
    }
  },
  {
    $group: {
      _id: {
        vendor_id: "$vendor_id",
        pub_id: "$pub_id",
        campaign_key: "$campaign_key",
        client_id: "$client_id"
      },
      assign: {
        $sum: {
          $cond: [
            {
              $or: [
                { $eq: ["$invoice_revenue_id", null] },
                { $eq: ["$invoice_revenue_id", ""] },
                { $not: { $ifNull: ["$invoice_revenue_id", false] } }
              ]
            },
            0,
            1
          ]
        }
      },
      unassign: {
        $sum: {
          $cond: [
            {
              $or: [
                { $eq: ["$invoice_revenue_id", null] },
                { $eq: ["$invoice_revenue_id", ""] },
                { $not: { $ifNull: ["$invoice_revenue_id", false] } }
              ]
            },
            1,
            0
          ]
        }
      }
    }
  },
  {
    $lookup: {
      from: "vendors",
      localField: "_id.vendor_id",
      foreignField: "_id",
      as: "vendorInfo"
    }
  },
  {
    $unwind: "$vendorInfo"
  },
  {
    $lookup: {
      from: "clients",
      localField: "_id.client_id",
      foreignField: "_id",
      as: "clientInfo"
    }
  },
  {
    $unwind: "$clientInfo"
  },
  {
    $group: {
      _id: {
        vendor_id: "$_id.vendor_id",
        campaign_key: "$_id.campaign_key",
        client: "$clientInfo.client"
      },
      vendor: { $first: "$vendorInfo.vendor" },
      pubids: {
        $push: {
          pubid: "$_id.pub_id",
          assign: "$assign",
          unassign: "$unassign"
        }
      }
    }
  },
  {
    $group: {
      _id: {
        vendor_id: "$_id.vendor_id",
        client: "$_id.client"
      },
      vendor: { $first: "$vendor" },
      campaign_keys: {
        $push: {
          campaign_key: "$_id.campaign_key",
          pubids: "$pubids"
        }
      }
    }
  },
  {
    $group: {
      _id: "$_id.vendor_id",
      vendor: { $first: "$vendor" },
      result: {
        $push: {
          client: "$_id.client",
          campaign_keys: "$campaign_keys"
        }
      }
    }
  },
  {
    $project: {
      _id: 0,
      vendor_id: "$_id",
      vendor: 1,
      result: 1
    }
  }
]
