import {getFilterByCollection} from "@/app/_lib/utils/accounting/getFilterByCollection.js";
import _ from "lodash";

export const getAccountingFilters = (isoWeekDateFilter, collection, majorDuration, selectFilters, accountingType) => {
    let filters = {};
    filters = getFilterByCollection(isoWeekDateFilter, collection, majorDuration);
    const selectResult = buildSelectFilters(selectFilters, collection, accountingType);
    filters = {...filters, ...selectResult};
    return _.omitBy(filters, (value) => value === 'NA_FILTER');
}

const buildSelectFilters = (selectFilters, collection, accountingType) => {
    const filterMap = {
        leads: ['campaign_key', 'pubid', 'vendor_id'],
        transfers: ['campaign_key', 'pubid', 'client_id', 'vendor_id'],
        postbacks: ['campaign_key', 'pubid', 'client_id', 'vendor_id'],
    };
    let filters = _.cloneDeep(selectFilters)
    if (accountingType === 'REVENUE') {
        filters = _.omit(selectFilters, ['vendor_id'])
    }
    const fields = filterMap[collection.toLowerCase()];
    return fields ? _.pick(filters, fields) : filters;
};
