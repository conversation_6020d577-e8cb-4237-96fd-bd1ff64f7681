import {transformDateFilters} from "@/app/_lib/utils/transformDateFilters.js";
import {getHintByCollection} from "@/app/_lib/utils/accounting/getHintByCollection.js";

export const getRevenueAccountingPipeline = (filters, collection) => {
    const mongoFilters = transformDateFilters(filters)
    let hint = getHintByCollection(collection);
    hint = {...hint, invoice_revenue_id: 1};
    const pipeline = [
        {
            $match: mongoFilters
        },
        {
            $group: {
                _id: {
                    vendor_id: "$vendor_id",
                    pubid: collection === 'transfers' ? "$pub_id" : "$pubid",
                    campaign_key: "$campaign_key",
                    client_id: "$client_id"
                },
                assign: {
                    $sum: {
                        $cond: [
                            {
                                $or: [
                                    {
                                        $eq: [
                                            "$invoice_revenue_id",
                                            null
                                        ]
                                    },
                                    {
                                        $eq: ["$invoice_revenue_id", ""]
                                    },
                                    {
                                        $not: {
                                            $ifNull: [
                                                "$invoice_revenue_id",
                                                false
                                            ]
                                        }
                                    }
                                ]
                            },
                            0,
                            1
                        ]
                    }
                },
                unassign: {
                    $sum: {
                        $cond: [
                            {
                                $or: [
                                    {
                                        $eq: [
                                            "$invoice_revenue_id",
                                            null
                                        ]
                                    },
                                    {
                                        $eq: ["$invoice_revenue_id", ""]
                                    },
                                    {
                                        $not: {
                                            $ifNull: [
                                                "$invoice_revenue_id",
                                                false
                                            ]
                                        }
                                    }
                                ]
                            },
                            1,
                            0
                        ]
                    }
                },
                invoice_revenue_ids: {
                    $addToSet: "$invoice_revenue_id"
                }
            }
        },
        {
            $lookup: {
                from: "vendors",
                localField: "_id.vendor_id",
                foreignField: "_id",
                as: "vendorInfo"
            }
        },
        { $unwind: "$vendorInfo" },
        {
            $lookup: {
                from: "clients",
                localField: "_id.client_id",
                foreignField: "_id",
                as: "clientInfo"
            }
        },
        { $unwind: "$clientInfo" },
        {
            $group: {
                _id: {
                    client: "$clientInfo.client",
                    client_id: "$_id.client_id",
                    campaign_key: "$_id.campaign_key",
                    pubid: "$_id.pubid"
                },
                assign: { $sum: "$assign" },
                unassign: { $sum: "$unassign" },
                invoice_revenue_ids: {
                    $push: "$invoice_revenue_ids"
                }
            }
        },
        {
            $group: {
                _id: {
                    client: "$_id.client",
                    client_id: "$_id.client_id",
                    campaign_key: "$_id.campaign_key"
                },
                pubids: {
                    $push: {
                        pubid: "$_id.pubid",
                        assign: "$assign",
                        unassign: "$unassign",
                        invoice_revenue_ids: {
                            $reduce: {
                                input: "$invoice_revenue_ids",
                                initialValue: [],
                                in: {
                                    $setUnion: [
                                        "$$value",
                                        {
                                            $filter: {
                                                input: "$$this",
                                                as: "id",
                                                cond: {
                                                    $ne: ["$$id", null]
                                                }
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    }
                }
            }
        },
        {
            $group: {
                _id: {
                    client: "$_id.client",
                    client_id: "$_id.client_id"
                },
                result: {
                    $push: {
                        campaign_key: "$_id.campaign_key",
                        pubids: "$pubids"
                    }
                }
            }
        },
        {
            $project: {
                _id: 0,
                client: "$_id.client",
                client_id: "$_id.client_id",
                result: 1
            }
        }
    ]


    return {pipeline, hint}
}
