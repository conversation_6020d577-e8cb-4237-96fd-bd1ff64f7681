import {transformDateFilters} from "@/app/_lib/utils/transformDateFilters.js";
import {getHintByCollection} from "@/app/_lib/utils/accounting/getHintByCollection.js";

export const getRevenueLeadsAccountingPipeline = (filters, collection) => {
    const mongoFilters = transformDateFilters(filters)
    let hint = getHintByCollection(collection);
    hint = {...hint, invoice_revenue_id: 1};
    const pipeline = [
        {
            $match: mongoFilters
        },
        {
            $addFields: {
                client_id: {
                    $ifNull: ["$client_id", "unknown"]
                }
            }
        },
        {
            $group: {
                _id: {
                    client_id: "$client_id",
                    campaign_key: "$campaign_key",
                    pubid: "$pubid"
                },
                assign: {
                    $sum: {
                        $cond: [
                            {
                                $or: [
                                    {$eq: ["$invoice_revenue_id", null]},
                                    {$eq: ["$invoice_revenue_id", ""]},
                                    {$not: {$ifNull: ["$invoice_revenue_id", false]}}
                                ]
                            },
                            0,
                            1
                        ]
                    }
                },
                unassign: {
                    $sum: {
                        $cond: [
                            {
                                $or: [
                                    {$eq: ["$invoice_revenue_id", null]},
                                    {$eq: ["$invoice_revenue_id", ""]},
                                    {$not: {$ifNull: ["$invoice_revenue_id", false]}}
                                ]
                            },
                            1,
                            0
                        ]
                    }
                },
                invoice_revenue_ids: {
                    $addToSet: {
                        $cond: [
                            {
                                $or: [
                                    {$eq: ["$invoice_revenue_id", null]},
                                    {$eq: ["$invoice_revenue_id", ""]}
                                ]
                            },
                            "$$REMOVE", // This removes null or empty values
                            "$invoice_revenue_id"
                        ]
                    }
                }
            }
        },
        {
            $group: {
                _id: {
                    client_id: "$_id.client_id",
                    campaign_key: "$_id.campaign_key"
                },
                pubids: {
                    $push: {
                        pubid: "$_id.pubid",
                        assign: "$assign",
                        unassign: "$unassign",
                        invoice_revenue_ids: "$invoice_revenue_ids"
                    }
                }
            }
        },
        {
            $group: {
                _id: "$_id.client_id",
                campaign_keys: {
                    $push: {
                        campaign_key: "$_id.campaign_key",
                        pubids: "$pubids"
                    }
                }
            }
        },
        {
            $lookup: {
                from: "clients",
                localField: "_id",
                foreignField: "_id",
                as: "client_info"
            }
        },
        {
            $addFields: {
                client: {
                    $ifNull: [
                        {
                            $arrayElemAt: ["$client_info.name", 0]
                        },
                        "unknown"
                    ]
                }
            }
        },
        {
            $project: {
                _id: 0,
                client: 1,
                clientId: "$_id",
                campaign_keys: 1
            }
        }
    ]


    return {pipeline, hint}
}
