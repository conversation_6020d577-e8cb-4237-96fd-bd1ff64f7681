export const getFilterByCollection = (dates, collection, isMajorDuration = false) => {
    const dateFieldMapping = {
        leads: "date_created",
        transfers: "completed_date",
        postbacks: "date_retained"
    };
    const dateField = dateFieldMapping[collection];
    let filters = dateField ? {[dateField]: {$gte: dates.start, $lte: dates.end}} : {};
    if (collection === 'leads') {
        filters = {...filters, disposition: 'new'}
    }
    if (collection === 'postbacks') {
        filters = {...filters, retained: true}
    }
    if (collection === 'transfers') {
        if (isMajorDuration) {
            filters = {...filters, duration: {$gte: 120}}
        } else {
            filters = {...filters, duration: {$lte: 120}}
        }
    }
    return filters;
}
