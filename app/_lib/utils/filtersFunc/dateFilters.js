import {DateTime} from "luxon";

export const dateFilters = (type) => {
    const myTimeZone = 'America/Chicago';
    switch (type?.value) {
        case 'today':
            const today = DateTime.now().setZone(myTimeZone).toISODate();
            return {start: today, end: today};
        case 'yesterday':
            const yesterdayDate = DateTime.now().setZone(myTimeZone).minus({days: 1}).toISODate();
            return {start: yesterdayDate, end: yesterdayDate};
        case 'lastWeek':
            const lastWeekStartDate = DateTime.now().minus({weeks: 1}).startOf('week').toISODate();
            const lastWeekEndDate = DateTime.now().startOf('week').minus({days: 1}).toISODate();
            return {start: lastWeekStartDate, end: lastWeekEndDate};
        case 'thisMonth':
            const thisMonthStartDate = DateTime.now().startOf('month').toISODate();
            const thisMonthEndDate = DateTime.now().toISODate();
            return {start: thisMonthStartDate, end: thisMonthEndDate};
        case 'lastMonth':
            const lastMonthStartDate = DateTime.now().minus({months: 1}).startOf('month').toISODate();
            const lastMonthEndDate = DateTime.now().minus({months: 1}).endOf('month').toISODate();
            return {start: lastMonthStartDate, end: lastMonthEndDate};
        case 'last3Month':
            const last3MonthsStartDate = DateTime.now().minus({months: 3}).startOf('month').toISODate();
            const last3MonthEndDate = DateTime.now().minus({months: 1}).endOf('month').toISODate();
            return {start: last3MonthsStartDate, end: last3MonthEndDate};
    }
}
