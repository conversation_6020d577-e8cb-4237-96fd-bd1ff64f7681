import {DateTime} from "luxon";

export const todayDate = () => {
    const myTimeZone = 'America/Chicago';
    const getToday = DateTime.now().setZone(myTimeZone).toISODate();
    let startDate = DateTime.fromFormat(getToday, 'yyyy-MM-dd HH:mm', {zone: 'utc'});
    let endDate = DateTime.fromFormat(getToday, 'yyyy-MM-dd HH:mm', {zone: 'utc'}).set({
        hour: 23,
        minute: 59,
        second: 59
    });
    if (!startDate.isValid) {
        startDate = DateTime.fromFormat(getToday, 'yyyy-MM-dd', {zone: 'utc'});
    }
    if (!endDate.isValid) {
        endDate = DateTime.fromFormat(getToday, 'yyyy-MM-dd', {zone: 'utc'}).set({
            hour: 23,
            minute: 59,
            second: 59
        });
    }
    const startUTC = startDate.toJSDate()
    const endUTC = endDate.toJSDate()
    return {startToday: startUTC, endToday: endUTC}
}