import Decimal from "decimal.js";
import _ from "lodash";

export const formatAmounts = (payload) => {
    payload = setTotalAmountLineItems(payload)
    payload = getGlobalAmounts(payload);
    payload = transformToCents(payload);
    return payload;
}

const transformToCents = (payload) => {
    payload['line_items'] = _.map(payload.line_items, lineItem => {
        const decimalAmount = new Decimal(lineItem.amount_by_event).mul(100).toNumber();
        const totalAmount = new Decimal(lineItem.total_amount).mul(100).toNumber();
        return {
            label: lineItem.label,
            amount_by_event: decimalAmount,
            total_amount: totalAmount
        }
    });
    return payload;
}
const setTotalAmountLineItems = (payload) => {
    const {line_items, total_events} = payload;
    payload['line_items'] = _.map(line_items, lineItem => {
        const decimalAmount = new Decimal(lineItem.amount_by_event);
        const totalAmount = decimalAmount.mul(total_events).toDP(2).toNumber()
        return {
            label: lineItem.label,
            amount_by_event: lineItem.amount_by_event,
            total_amount: totalAmount
        }
    });
    return payload;
}
const getGlobalAmounts = (payload) => {
    const {total_events} = payload;
    const totalSummary = _.sumBy(payload.line_items, item => Number(item.total_amount));
    let totalAmount = new Decimal(totalSummary).toDP(2);
    let amountByEvent = totalAmount.div(total_events).toDP(2);
    payload['total_amount'] = totalAmount.mul(100).toNumber();
    payload['amount_by_event'] = amountByEvent.mul(100).toNumber();
    return payload;
}

