import {getExternalDistinctValues} from "@/app/_lib/nova/functions/getExternalDistinctValues";
import _ from "lodash";

export const buildExternalSelectTableColumns = async (tableColumns, tableName, userMetadata = {}) => {
    const filteredData = tableColumns.filter(item => (
        (item.filterComponent === "SelectFilterComponent" || item.filterComponent === "MultiSelectFilterComponent")
        && item.config.select === "external"
    ));
    const dynamicConfig = filteredData.map(item => {
        return {
            id: item.config.foreignSelectId,
            label: item.config.foreignSelectLabel,
            table: item.config.selectTable,
            key: item.accessorKey
        }
    });
    const distinctPromises = dynamicConfig.map(key => getExternalDistinctValues([key], key.table));
    const distinctResults = await Promise.all(distinctPromises);
    if (distinctResults && !_.isEmpty(distinctResults)) {
        for (const values of distinctResults) {
            const keys = Object.keys(values);
            const key = keys[0];
            await overrideSelectOptions(tableName, tableColumns, key, values[key], userMetadata);
        }
    }
    return tableColumns;
}

async function overrideSelectOptions(tableName, tableColumns, key, selectOptions, userMetadata) {
    const objToUpdateIndex = tableColumns.findIndex(obj => obj.accessorKey === key);
    if (objToUpdateIndex !== -1) {
        const {restrictions} = userMetadata;
        let selectOptRestrict = selectOptions
        let arrayToFilter = []
        if (_.includes(key, "vendor") && !_.includes(restrictions?.vendors, "*")) {
            arrayToFilter = restrictions?.vendors
        } else if (_.includes(key, "client") && !_.includes(restrictions?.clients, "*")) {
            arrayToFilter = restrictions?.clients
        }
        if (arrayToFilter && arrayToFilter.length > 0) selectOptRestrict = selectOptRestrict.filter(item => _.includes(arrayToFilter, item.value));
        tableColumns[objToUpdateIndex].config.selectOptions = selectOptRestrict;
    }
}
