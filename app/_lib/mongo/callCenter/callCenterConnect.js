'use server'

import {MongoClient} from 'mongodb';
import {params} from "@ampt/sdk";

const uri = params("MONGO_URI")

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
};

let cachedClient = null;
let cachedDb = null;

export default async function callCenterConnect() {
    if (cachedClient && cachedDb) {
        return {client: cachedClient, db: cachedDb};
    }

    try {
        const client = new MongoClient(uri, options);
        await client.connect();
        const db = client.db('community_call_center');
        cachedClient = client;
        cachedDb = db;
        return {client: cachedClient, db: cachedDb};
    } catch (error) {
        // console.error("Mongo connection error")
        // console.error(error)
        return {client: null, db: null};
    }
}
