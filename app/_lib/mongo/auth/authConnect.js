'use server'

import {MongoClient} from 'mongodb';
import {params} from "@ampt/sdk";

const uri = params("MONGO_URI");
const AUTH_DB_NAME = 'core_auth';

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
};

let cachedClient = null;
let cachedDb = null;

export default async function authConnect() {
    if (cachedClient && cachedDb) {
        return {client: cachedClient, db: cachedDb};
    }

    try {
        const client = new MongoClient(uri, options);
        await client.connect();
        const db = client.db(AUTH_DB_NAME);

        cachedClient = client;
        cachedDb = db;

        return {client: cachedClient, db: cachedDb};
    } catch (error) {
        console.error("Auth database connection error:", error);
        return {client: null, db: null};
    }
}
