import {NextResponse} from "next/server";
import {updateMetadataFromFormData} from "@/app/_lib/nova/functions/better-auth/updateMetadataFromFormData";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const respUpdData = await updateMetadataFromFormData(payload)
        const response = (respUpdData.status === 200) ? {response: respUpdData.response} : {error: respUpdData.error}
        return NextResponse.json(response, {status: respUpdData.status});
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(error, {status: 500})
    }
}
