import {NextResponse} from 'next/server';
import {getUserMetadataById} from "@/app/_lib/nova/functions/better-auth/getUserMetadataById";
import _ from "lodash";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const userData = await getUserMetadataById(payload);
        if (_.isEmpty(userData?.error)) {
            return NextResponse.json(userData, {status: 200});
        } else {
            return NextResponse.json(userData, {status: 400});
        }
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(error, {status: 500})
    }
}
