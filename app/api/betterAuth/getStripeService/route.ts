import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/app/_lib/auth/auth";
import { ensureStripeCustomer } from "@/app/_lib/utils/betterAuth/ensureStripeCustomer";
export async function GET(request: NextRequest) {
    try {
        const session = await auth.api.getSession({ headers: request.headers });
        if (!session) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }
        let stripeCustomerId = session.user.stripeCustomerId;
        let customerCreated = false;
        if (!stripeCustomerId) {
            console.log(':wrench: User does not have Stripe customer ID, creating one...');

            const result = await ensureStripeCustomer(
                session.user.id,
                session.user.email,
                session.user.name
            );
            if (result.error) {
                console.error(':x: Error creating Stripe customer:', result.error);
                return NextResponse.json({
                    status: 'error',
                    message: 'Failed to create Stripe customer',
                    error: result.error,
                    timestamp: new Date().toISOString()
                }, { status: 500 });
            }
            stripeCustomerId = result.stripeCustomerId;
            customerCreated = result.customerCreated;
        }
        return NextResponse.json({
            status: 'success',
            user: {
                id: session.user.id,
                email: session.user.email,
                stripeCustomerId: stripeCustomerId,
                role: session.user.role,
                hasStripeCustomer: !!stripeCustomerId,
                customerCreated: customerCreated
            },
            timestamp: new Date().toISOString()
        });
    } catch (error: any) {
        console.error(':x: Error getting user Stripe info:', error);

        return NextResponse.json({
            status: 'error',
            message: 'Failed to get user Stripe info',
            error: error.message,
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
}
