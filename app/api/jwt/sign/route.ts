import {NextResponse} from "next/server";
import {sign} from "@/app/_lib/jwt/sign";

export async function POST(request: Request) {
    try {
        const requestToSign = await request.json()
        const {payload} = requestToSign;
        const token = sign(payload);
        return NextResponse.json({token}, {status: 200});
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(error, {status: 500})
    }
}
