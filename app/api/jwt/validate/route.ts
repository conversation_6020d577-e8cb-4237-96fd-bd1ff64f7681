import {NextResponse} from "next/server";
import {validate} from "@/app/_lib/jwt/validate";

export async function POST(request: Request) {
    try {
        const {token} = await request.json()
        const payload = validate(token);
        return NextResponse.json(payload, {status: 200});
    } catch (error) {
        console.error('Error processing request:', error);
        const message = error.conversionFinderError ? error.conversionFinderError : 'Unknown error'
        const status = error.conversionFinderError ? 400 : 500
        return NextResponse.json(message, {status: status})
    }
}
