import {NextResponse} from 'next/server';
import adminConnect from "@/app/_lib/mongo/admin/adminConnect";

export async function GET() {
    try {
        const {db} = await adminConnect();
        const timezones = await db.collection('timezones').find().sort({abb: 1}).toArray();
        return NextResponse.json({response: timezones}, {status: 200});
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(error, {status: 500})
    }
}
