import {NextResponse} from "next/server";
import authConnect from "@/app/_lib/mongo/auth/authConnect";
// import {clerkClient} from "@clerk/nextjs/server";

export async function POST(request: Request) {
    try {
        const {db} = await authConnect();
        const payload = await request.json();
        // const data = await db.collection('calls').findOne();
        // const users = await clerkClient.users.getUserList({
        //     orderBy: '-created_at',
        //     limit: 100,
        // })
        // const response = await migrateClerkUsers(users)
        // console.log({response})
        const data = {data: 'test'}
        return NextResponse.json({response: data}, {status: 200});
    } catch (error) {
        console.error('Error fetching calls data:', error);
        return NextResponse.json(
            {error: 'Error fetching calls data'},
            {status: 500}
        );
    }
}

async function migrateClerkUsers(clerkUsers: any) {
    const transformedUsers = clerkUsers.map((user: any) => {
        const publicMetadata = user.publicMetadata || {};

        const metadata = {
            chat: publicMetadata.chat ?? null,
            menus: publicMetadata.menus ?? null,
            table_keys: publicMetadata.table_keys ?? null,
            restrictions: publicMetadata.restrictions ?? null,
        };

        return {
            name: user.firstName ?? '',
            email: user.emailAddresses?.[0]?.emailAddress ?? '',
            emailVerified: user.emailAddresses?.[0]?.verification?.status === 'verified',
            createdAt: new Date(user.createdAt),
            updatedAt: new Date(user.updatedAt),
            role: publicMetadata.rol ?? 'normal_user',
            metadata: JSON.stringify(metadata),
            dev: publicMetadata.dev ?? false,
            new: false,
            timezone: publicMetadata.timezone ?? 'America/Chicago',
            templateVersion: publicMetadata.templateVersion ?? 'Default Template',
            activeOrgId: user.organizationMemberships?.[0]?.organization?.id ?? null,
            stripeCustomerId: publicMetadata.stripeCustomerId ?? null,
        };
    });
    const results = [];
    const {db} = await authConnect();
    const collection = db.collection('user');

    for (const user of transformedUsers) {
        const result = await collection.updateOne(
            {email: user.email},
            {$set: user},
            {upsert: true}
        );
        results.push(result);
    }

    return results;
}
