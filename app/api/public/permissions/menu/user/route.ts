import {NextResponse} from 'next/server';
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect";

const adminSubMenu = [
    {
        "route": "admin/databrokerQueues",
        "label": "Databroker queues"
    },
    {
        "route": "admin/permissions",
        "label": "permissions"
    }
]
const adminMenu = {
    "route": "admin",
    "label": "Admin",
    "nested": true,
    "subitems": adminSubMenu
}

export async function GET(request: Request) {
    try {
        const {db} = await adminConnect();
        const user: any = await getUserSession()
        const {metadata, role} = user || {metadata: null, role: null};
        const jsonMetadata = metadata ? JSON.parse(metadata) : null;
        const permissions = jsonMetadata?.menus;
        const configuration = await db.collection('configurations').findOne({name: 'menu'});
        const menu = buildMenuFromPermissions(permissions, configuration);
        const hasAdminMenu = menu.some(item => item.route === 'admin');
        if (role === 'admin') {
            if (!hasAdminMenu) {
                menu.push(adminMenu);
            } else {
                menu[menu.findIndex(item => item.route === 'admin')].subitems = adminSubMenu
            }
        }
        return NextResponse.json(menu, {status: 200});
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(error, {status: 500})
    }
}


function buildMenuFromPermissions(metadata, menu) {
    if (!metadata) return [];
    const allowedRoutes = new Set(
        metadata.flatMap(section => section.routes)
    );

    return menu.configuration
        .map(menuItem => {
            const validSubMenus = (menuItem.subMenus || []).filter(sub => allowedRoutes.has(sub.route));
            const isMainRouteAllowed = allowedRoutes.has(menuItem.route);

            if (!isMainRouteAllowed && validSubMenus.length === 0) return null;

            const result: any = {
                route: menuItem.route,
                label: menuItem.name,
            };

            if (validSubMenus.length > 0) {
                result.nested = true;
                result.subitems = validSubMenus.map(sub => ({
                    route: sub.route,
                    label: sub.name,
                }));
            }

            return result;
        })
        .filter(Boolean);
}

