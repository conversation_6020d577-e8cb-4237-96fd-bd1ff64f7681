import {NextResponse} from "next/server";
import {params} from "@ampt/sdk";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const {key} = payload;
        const getParam = params(key);
        return NextResponse.json({key, value: getParam}, {status: 200});
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(error, {status: 500})
    }
}
