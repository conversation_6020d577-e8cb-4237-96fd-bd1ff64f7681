import {NextResponse} from "next/server";
import {createClient} from "@deepgram/sdk";
import fs from "fs";
import path from "path";
import {promisify} from "util";
import {params} from "@ampt/sdk";

const writeFile = promisify(fs.writeFile);
const unlink = promisify(fs.unlink);
const readFile = promisify(fs.readFile);

export async function POST(request: Request) {
    try {
        const deepGramApiKey = params('DEEPGRAM_APIKEY') || null;
        if (!deepGramApiKey) {
            return NextResponse.json({error: "Transcription service is not available now, try again later "}, {status: 400});
        }

        const deepgram = createClient(deepGramApiKey);
        if (!deepgram) {
            return NextResponse.json({error: "Transcription service is not available now, try again later "}, {status: 400});
        }

        const audioBuffer = await request.arrayBuffer();
        const tempFilePath = path.join("/tmp", `audio-${Date.now()}.webm`);

        await writeFile(tempFilePath, Buffer.from(audioBuffer));
        const fileBuffer = await readFile(tempFilePath);

        const {result, error} = await deepgram.listen.prerecorded.transcribeFile(
            fileBuffer,
            {model: "nova", language: "en"}
        );

        await unlink(tempFilePath);

        if (error) return NextResponse.json({error: error.message}, {status: 500});

        const transcript = result?.results?.channels[0]?.alternatives[0]?.transcript || "";

        return NextResponse.json({text: transcript});
    } catch (error) {
        return NextResponse.json({error: error.message}, {status: 500});
    }
}
