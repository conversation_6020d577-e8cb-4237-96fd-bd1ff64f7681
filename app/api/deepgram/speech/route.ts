import { NextResponse } from 'next/server';
import { createClient } from '@deepgram/sdk';
import {params} from "@ampt/sdk";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const { text } = payload;

        const deepGramApiKey = params('DEEPGRAM_APIKEY') || null;
        if (!deepGramApiKey) {
            return NextResponse.json({ error: "Speech service is not available now, try again later " }, { status: 400 });
        }

        const deepgram = createClient(deepGramApiKey);
        if (!deepgram) {
            return NextResponse.json({ error: "Speech service is not available now, try again later " }, { status: 400 });
        }

        const response = await deepgram.speak.request(
            { text: text ?? "Hello, how can I help you today?" },
            {
                model: "aura-asteria-en",
                encoding: "linear16",
                container: "wav",
            }
        );

        const stream = await response.getStream();
        if (!stream) {
            return NextResponse.json({ error: "Error generating audio" }, { status: 500 });
        }

        const chunks = [];
        const reader = stream.getReader();

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            chunks.push(value);
        }

        const audioBuffer = Buffer.concat(chunks);

        return new Response(audioBuffer, {
            headers: {
                'Content-Type': 'audio/wav',
            },
        });
    } catch (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
}
