import {NextResponse} from "next/server";
import {ObjectId} from "mongodb";
import {findManyAuthData} from "@/app/_lib/mongo/auth/functions/findManyAuthData";

export async function POST(request: Request) {
    try {
        const payload = await request.json();
        const {organizationId} = payload;

        const pendingInvitations = await findManyAuthData(
            'invitation',
            {
                organizationId: new ObjectId(organizationId),
                status: 'pending'
            },
            {
                email: 1,
                expiresAt: 1,
                status: 1
            }
        );

        return NextResponse.json({response: pendingInvitations}, {status: 200});
    } catch (error) {
        console.error('Error getting pending invitations:', error);
        return NextResponse.json({error: 'Error getting pending invitations'}, {status: 500});
    }
}
