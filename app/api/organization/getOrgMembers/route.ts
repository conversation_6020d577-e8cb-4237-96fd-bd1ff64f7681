import {NextResponse} from "next/server";
import {ObjectId} from "mongodb";
import _ from "lodash";
import {findManyAuthData} from "@/app/_lib/mongo/auth/functions/findManyAuthData";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const {orgId} = payload;
        const getMembers = await findManyAuthData(
            'member',
            {organizationId: new ObjectId(orgId)},
            {userId: 1, role: 1}
        )

        if (getMembers.length > 0) {
            const getUsers = await findManyAuthData(
                'user',
                {_id: {$in: getMembers.map((itm: any) => itm.userId)}},
                {_id: 1, email: 1}
            )

            const usersWithRoles = getUsers.map((user: any) => {
                const memberData = _.find(getMembers, (member: any) =>
                    member.userId.toString() === user._id.toString()
                );
                return {
                    ...user,
                    role: memberData?.role || 'member'
                };
            });

            const sortedUsers = _.orderBy(usersWithRoles,
                [(user) => user.role === 'owner'],
                ['desc']
            );

            return NextResponse.json({response: sortedUsers}, {status: 200});
        }

        return NextResponse.json({response: []}, {status: 200});
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json({error: 'Error processing request'}, {status: 500})
    }
}
