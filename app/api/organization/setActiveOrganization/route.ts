import {NextResponse} from "next/server";
import {ObjectId} from "mongodb";
import updateAuthRegister from "@/app/_lib/nova/functions/better-auth/updateAuthRegister";
import {setOrgCookieFromUser} from "@/app/_lib/utils/betterAuth/setOrgCookieFromUser";

export async function POST(request: Request) {
    try {
        const {userId, orgId} = await request.json();

        if (!userId || !orgId) {
            return NextResponse.json(
                {error: "Missing required fields"},
                {status: 400}
            );
        }

        const dataUpdate = {
            collection: 'user',
            filter: {_id: new ObjectId(userId)},
            update: {$set: {activeOrgId: new ObjectId(orgId)}}
        }

        await setOrgCookieFromUser(orgId);
        await updateAuthRegister(dataUpdate)

        return NextResponse.json(
            {message: "Active organization updated successfully"},
            {status: 200}
        );
    } catch (error) {
        console.error('Error setting active organization:', error);
        return NextResponse.json(
            {error: "Failed to update active organization"},
            {status: 500}
        );
    }
}
