import {NextResponse} from "next/server";
import axios from "axios";
import {params} from "@ampt/sdk";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
    try {
        const dataBrokerHost = params('DATA_BROKER_HOST') || null;
        if (!dataBrokerHost) {
            return NextResponse.json({error: "Data broker is not available now, try again later "}, {status: 400});
        }
        let payload = await request.json()
        const user: any = await getUserSession()
        const {email} = user || {email: null};
        const response = await axios.post(`${dataBrokerHost}/api/upload/url`, {
            ...payload,
            userEmail: email
        })
        return NextResponse.json(response.data);
    } catch (error) {
        console.error('Error to generate report:', error.response.data);
        const message = error?.response?.data?.message ? error.response.data.message : 'Error on generate Url';
        return NextResponse.json({error: message},
            {status: 400})

    }
}
