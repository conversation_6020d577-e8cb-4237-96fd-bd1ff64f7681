import {NextResponse} from "next/server";
import axios from "axios";
import {params} from "@ampt/sdk";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
    try {
        const dataBrokerHost = params('DATA_BROKER_HOST');
        let payload = await request.json();
        const user: any = await getUserSession()
        const {email} = user || {email: null, role: null};
        const response = await axios.post(`${dataBrokerHost}/api/files/validate`,
            {...payload, userEmail: email})
        return NextResponse.json(response.data);
    } catch (error) {
        const message = error?.response?.data?.message ? error.response.data.message : 'Error on generate Url';
        return NextResponse.json({error: message},
            {status: 400})
    }
}
