import insertManyObjects from "@/app/_lib/nova/functions/insertManyObjects";
import {NextResponse} from "next/server";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const response = await insertManyObjects(payload)
        return NextResponse.json(response)
    } catch (error) {
        console.log('Error processing create register request:', error)
        return NextResponse.error()
    }
}
