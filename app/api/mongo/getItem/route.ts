import getItem from "@/app/_lib/nova/functions/getItem";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
    try {
        const payload = await request.json();
        const getItmResp = await getItem(payload);
        const response =
            getItmResp.status === 200
                ? { response: getItmResp.response }
                : { error: getItmResp.error };

        return NextResponse.json(response, { status: getItmResp.status });
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Error processing request" },
            { status: 500 }
        );
    }
}