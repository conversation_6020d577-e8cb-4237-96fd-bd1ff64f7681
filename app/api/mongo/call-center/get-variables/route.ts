import {NextResponse} from "next/server";
import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect";
import _ from "lodash";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
    try {
        const {db} = await callCenterConnect();
        const payload = await request.json();
        const {campaignId} = payload;
        const getDataResponse = await db.collection('campaigns').findOne({_id: campaignId});
        if (!getDataResponse) return NextResponse.json({error: "No script found"}, {status: 404});

        const {variables} = getDataResponse;
        if (!variables) return NextResponse.json({response: []}, {status: 200});
        const compound = variables.filter((item: any) => item.group === "compound");
        const client = variables.filter((item: any) => item.group === "client");
        const company = variables.filter((item: any) => item.group === "company");
        const script = variables.filter((item: any) => item.group === "script");

        const response = {
            compound,
            client,
            company,
            script,
            all: variables
        }
        return NextResponse.json({response}, {status: 200});
    } catch (error) {
        console.error('Error: ', error)
        return NextResponse.json({error: "Internal Server Error"}, {status: 500});
    }
}
