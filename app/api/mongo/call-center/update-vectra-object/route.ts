import {NextResponse} from "next/server";
import updateVectraObject from "@/app/_lib/nova/functions/call-center/updateVectraObject";

export async function POST(request: Request) {
    try {
        const payload = await request.json();
        const updateObject = await updateVectraObject(payload);
        const response =
            updateObject.status === 200
                ? { response: updateObject.response }
                : { error: updateObject.error };

        return NextResponse.json(response, { status: updateObject.status });
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Error processing request" },
            { status: 500 }
        );
    }
}
