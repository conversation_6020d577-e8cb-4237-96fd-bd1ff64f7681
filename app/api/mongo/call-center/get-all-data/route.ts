import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect";
import {NextResponse} from "next/server";

export async function POST(request: Request) {
    try {
        const {db} = await callCenterConnect();
        const payload = await request.json();
        const {collection, filter = {}, project = {}, sort = {}, skip = 0, limit = 0} = payload;
        const getAllData = await db.collection(collection).find(filter).project(project).sort(sort).skip(skip).limit(limit).toArray();
        const response = getAllData ? {response: getAllData} : {response: []};
        return NextResponse.json(response, {status: 200});
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {error: "Error processing request"},
            {status: 500}
        );
    }
}
