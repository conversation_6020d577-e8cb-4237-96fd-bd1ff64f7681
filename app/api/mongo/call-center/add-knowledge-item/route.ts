import { NextResponse } from "next/server";
import addKnowledgeItem from "@/app/_lib/nova/functions/call-center/addKnowledgeItem";

export async function POST(request: Request) {
    try {
        return await addKnowledgeItem(request);
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Error processing request" },
            { status: 500 }
        );
    }
}
