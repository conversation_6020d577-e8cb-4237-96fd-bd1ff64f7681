import {NextResponse} from "next/server";
import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect";
import updateData from "@/app/_lib/nova/functions/call-center/updateData";

export async function POST(request: Request) {
    try {
        const {db} = await callCenterConnect();
        const payload = await request.json();
        const {formData, campaignId} = payload;
        const getDataResponse = await db.collection('campaigns').findOne({_id: campaignId});
        if (!getDataResponse) return NextResponse.json({error: "No script found"}, {status: 404});
        const getConfScriptVar = await db.collection('configuration').findOne({configuration: 'scriptVariables'});
        const configScriptVariables = getConfScriptVar?.data || []
        const findScriptData = configScriptVariables.find((itm: any) => itm.name === formData.name);
        if (!findScriptData) return NextResponse.json({error: "No script variable found"}, {status: 404});
        const payloadUpdate = {
            collection: 'campaigns',
            filter: {_id: campaignId},
            update: {$set: {[findScriptData.value]: formData.details}},
            options: {upsert: false}
        };
        await updateData(payloadUpdate)
        return NextResponse.json({response: "Updated successfully"}, {status: 200});
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {error: "Error processing request"},
            {status: 500}
        );
    }
}
