import {NextResponse} from "next/server";
import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect";

export async function POST(request: Request) {
    try {
        const {db} = await callCenterConnect();
        const payload = await request.json();
        const {collection, filter} = payload;
        const getDataResponse = await db.collection(collection).findOne(filter);
        const response = getDataResponse ? {response: getDataResponse} : {response: {}};
        return NextResponse.json(response, {status: 200});
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {error: "Error processing request"},
            {status: 500}
        );
    }
}
