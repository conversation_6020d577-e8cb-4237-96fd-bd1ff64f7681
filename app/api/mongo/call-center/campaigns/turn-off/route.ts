import {NextResponse} from "next/server";
import {campaignTurnOnOff} from "@/app/_lib/nova/functions/call-center/campaignTurnOnOff";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const response = await campaignTurnOnOff('turnOff', payload.campaignId)
        return NextResponse.json(response)
    } catch (error) {
        console.log('Error processing create register request:', error)
        return NextResponse.error()
    }
}
