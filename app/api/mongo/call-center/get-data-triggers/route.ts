import {NextResponse} from "next/server";
import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";

export async function POST(request: Request) {
    try {
        const {db} = await callCenterConnect();
        const payload = await request.json();
        const user: any = await getUserSession()
        const {campaignId} = payload;
        // const carrierData = await db.collection('carriers').findOne({"user_id": user?.id})
        const getTriggerEvents = await db.collection('configuration').findOne({configuration: "triggerEvents"})
        const getDataResponse = await db.collection('campaigns').findOne({_id: campaignId});
        const {variables} = getDataResponse;

        if (!variables) return NextResponse.json({
            response: {
                carrierId: user?.id,
                subscriptions: [],
                mapVariables: [],
                variables: [],
                events: getTriggerEvents?.data ?? {}
            }
        }, {status: 200});

        if (!variables) return NextResponse.json({response: []}, {status: 200});
        const allVariables = variables.filter((item: any) => item.group === "compound" || item.group === "client");
        // TODO: Change carrier and check how to get the carrier id
        const carrier = "6656d740e9a5f8e91f70a999"
        const subscriptions = await db.collection('subscriptions').find({
            campaignId
        }).toArray()
        const mapVariables = allVariables.map((item: any) => ({field: item.name, key: item.name}));

        const response = {
            carrierId: carrier,
            mapVariables: mapVariables ?? [],
            subscriptions: subscriptions ?? [],
            events: getTriggerEvents?.data ?? {}
        }
        return NextResponse.json({response}, {status: 200});
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {error: "Error processing request"},
            {status: 500}
        );
    }
}
