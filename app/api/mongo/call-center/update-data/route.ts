import updateData from "@/app/_lib/nova/functions/call-center/updateData";
import {NextResponse} from "next/server";

export async function PUT(request: Request) {
    try {
        const payload = await request.json()
        const response = await updateData(payload)
        return NextResponse.json(response)
    } catch (error) {
        console.log('Error processing create register request:', error)
        return NextResponse.error()
    }
}
