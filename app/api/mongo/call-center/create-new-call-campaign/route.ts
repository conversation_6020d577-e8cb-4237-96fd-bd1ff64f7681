import {NextResponse} from "next/server";
import {findGreetingsNodes} from "@/app/_lib/utils/nodeScripts/findGreetingsNodes";
import {findTerminatesNodes} from "@/app/_lib/utils/nodeScripts/findTerminatesNodes";
import {createNewNodeScripts} from "@/app/_lib/utils/nodeScripts/createNewNodeScripts";
import {findRequestNodes} from "@/app/_lib/utils/nodeScripts/findRequestNodes";
import {findTasksNodes} from "@/app/_lib/utils/nodeScripts/findTasksNodes";
import createData from "@/app/_lib/nova/functions/call-center/createData";
import _ from "lodash";
import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect";

export async function POST(request: Request) {
    try {
        const {db} = await callCenterConnect();
        const payload = await request.json();
        const {data} = payload;

        if (!data || !data._id || !data.name) {
            return NextResponse.json({error: "Invalid payload data"}, {status: 400});
        }
        const callCampaignId = data._id;
        const emptyCampaign = await db.collection('new_campaign').findOne();
        const getInitialsVars = await db.collection('configuration').findOne({configuration: 'initialVariables'});

        if (_.isEmpty(emptyCampaign)) {
            return NextResponse.json({error: "No data found in emptyCampaign"}, {status: 404});
        }

        const lastPhoneDoc = await db
            .collection('campaigns')
            .find({ phone: { $regex: '^0005\\d{6}$' } })
            .sort({ phone: -1 })
            .limit(1)
            .toArray();

        let nextPhoneSeq = 1;
        if (lastPhoneDoc.length && lastPhoneDoc[0]?.phone) {
            const lastSeq = parseInt(lastPhoneDoc[0].phone.slice(-6), 10);
            nextPhoneSeq = (lastSeq + 1) > 999999 ? 1 : lastSeq + 1;
        }

        const phone = `0005${String(nextPhoneSeq).padStart(6, '0')}`;

        const updatedCampaign = {
            ...emptyCampaign,
            variables: getInitialsVars?.data,
            name: data.name,
            _id: callCampaignId,
            phone: phone,
        };

        const payloadChild = {
            collection: 'campaigns',
            keysToReplace: [],
            data: updatedCampaign
        };

        try {
            const campaignCreated = await createData(payloadChild);

            const emptyCallCampaign = await db.collection('new_script').find().toArray();

            const campaignData = createNewNodeScripts(emptyCallCampaign, callCampaignId);
            const greetingsData = findGreetingsNodes(campaignData);
            const terminateData = findTerminatesNodes(campaignData);
            const dispositionData = findRequestNodes(campaignData, greetingsData, terminateData);
            const tasksData = findTasksNodes(campaignData, greetingsData, dispositionData);

            await db.collection('scripts').insertMany(campaignData);

            const response = {
                campaignData,
                greetingsData,
                terminateData,
                dispositionData,
                tasksData,
            };

            return NextResponse.json({response}, {status: 200});
        } catch (error) {
            console.error("Error creating campaign:", error);
            return NextResponse.json({error: "Failed to create campaign"}, {status: 500});
        }
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json({error: "Error processing request"}, {status: 500});
    }
}
