import { NextResponse } from "next/server";
import deleteVectraItem from "@/app/_lib/nova/functions/call-center/deleteVectraItem";

export async function POST(request: Request) {
    try {
        const payload = await request.json();
        const result = await deleteVectraItem(payload);
        const response = result.status === 200 ? { response: result.response } : { error: result.error };
        return NextResponse.json(response, { status: result.status });
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Error processing request" },
            { status: 500 }
        );
    }
}
