import formatGumloopResponse from "@/app/_lib/nova/functions/call-center/formatGumloopResponse";

export async function POST(req: Request) {
    try {
        const { callCampaignId, data } = await req.json();
        if (!callCampaignId || !data) {
            return new Response(JSON.stringify({ error: "Missing callCampaignId or data" }), {
                status: 400,
                headers: { "Content-Type": "application/json" },
            });
        }

        const result = await formatGumloopResponse(data, callCampaignId);
        return new Response(JSON.stringify(result), {
            status: 200,
            headers: { "Content-Type": "application/json" },
        });
    } catch (err) {
        console.error("Create Knowledge Config error:", err);
        return new Response(JSON.stringify({ error: "Internal server error" }), {
            status: 500,
            headers: { "Content-Type": "application/json" },
        });
    }
}
