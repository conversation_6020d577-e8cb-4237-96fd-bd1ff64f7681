import deleteData from "@/app/_lib/nova/functions/call-center/deleteData";
import {NextResponse} from "next/server";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const response = await deleteData(payload)
        return NextResponse.json(response)
    } catch (error) {
        console.log('Error processing create register request:', error)
        return NextResponse.error()
    }
}
