import {NextResponse} from "next/server";
import {findGreetingsNodes} from "@/app/_lib/utils/nodeScripts/findGreetingsNodes";
import {findTerminatesNodes} from "@/app/_lib/utils/nodeScripts/findTerminatesNodes";
import {findRequestNodes} from "@/app/_lib/utils/nodeScripts/findRequestNodes";
import {findTasksNodes} from "@/app/_lib/utils/nodeScripts/findTasksNodes";
import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect";

export async function POST(request: Request) {
    try {
        const {db} = await callCenterConnect()
        const payload = await request.json()
        const {callCampaignId} = payload

        const campaignData = await db.collection('scripts').find({callCampaignId}).toArray()
        if (!campaignData || campaignData.length === 0) {
            return NextResponse.json({error: "No script found"}, {status: 404});
        }

        const greetingsData = findGreetingsNodes(campaignData)
        const terminateData = findTerminatesNodes(campaignData)
        const dispositionData = findRequestNodes(campaignData, greetingsData, terminateData)
        const tasksData = findTasksNodes(campaignData, greetingsData, dispositionData);
        const vectraConfig = await db.collection('vectra').find({call_campaign_id: callCampaignId}).toArray()

        const response = {
            campaignData,
            greetingsData,
            terminateData,
            dispositionData,
            tasksData,
            vectraConfig
        }
        return NextResponse.json({response}, {status: 200});
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json({error: 'Error processing request'}, {status: 500})
    }
}
