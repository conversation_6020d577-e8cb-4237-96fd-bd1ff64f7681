import {NextResponse} from "next/server";
import updateAllDataById from "@/app/_lib/nova/functions/updateAllDataById";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const response = await updateAllDataById(payload)
        return NextResponse.json(response)
    } catch (error) {
        let message = 'Error processing updateAllDataById request'
        if (error.conversionFinderError && error.conversionFinderError !== '') {
            message = error.conversionFinderError;
        }
        return NextResponse.json({conversionFinderError: message}, {status: 400})
    }
}
