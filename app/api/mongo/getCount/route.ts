import {NextResponse} from "next/server";
import {getCount} from "@/app/_lib/nova/functions/getCount";
import {verifyRolesMiddleware} from "@/app/_lib/utils/verifyRolesMiddleware";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const user: any = await getUserSession()
        const {metadata} = user || {metadata: null};
        const jsonMetadata = metadata ? JSON.parse(metadata) : null;
        const authMetadata = jsonMetadata || {};
        const getCountResponse = await verifyRolesMiddleware({...payload, authMetadata}, getCount)
        const response = (getCountResponse.status === 200) ? {response: getCountResponse.response} : {error: getCountResponse.error}
        return NextResponse.json(response, {status: getCountResponse.status})
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json({})

    }
}
