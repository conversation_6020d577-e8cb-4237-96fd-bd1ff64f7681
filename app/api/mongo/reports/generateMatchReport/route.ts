import _ from "lodash";
import {buildAggregationMatch} from "@/app/_lib/utils/buildAggregationMatch";
import {aggregationMatchReport} from "@/app/_lib/nova/functions/aggregations/aggregationMatchReport";
import {NextResponse} from "next/server";

export async function POST(request: Request) {
    try {
        const {
            optionSelected,
            valueToMatch,
            table
        } = await request.json();

        if (_.isEmpty(optionSelected)) {
            return NextResponse.json({error: "No value selected"}, {status: 400});
        }

        const getResp = [];

        if (!_.isEmpty(valueToMatch)) {
            const dataAggregation = buildAggregationMatch(optionSelected, valueToMatch?._id);
            const getRespAgg = await aggregationMatchReport(table, dataAggregation, optionSelected) ?? [];

            if (!_.isEmpty(getRespAgg)) {
                const keyArray = (_.includes(optionSelected?.local_key, "_id")) ?
                    _.replace(optionSelected?.local_key, '_id', '') :
                    optionSelected?.local_key;

                let finalCountTotal = 0;
                const infToPush = [];

                for (const respInf of getRespAgg) {
                    finalCountTotal += respInf[`${keyArray}_total`];
                    infToPush.push(respInf);
                }

                const objToPush = {
                    _id: valueToMatch._id,
                    [optionSelected?.f_key2]: valueToMatch[optionSelected?.f_key2],
                    [`${optionSelected?.f_key2}_total`]: finalCountTotal,
                    [optionSelected?.local_key]: infToPush
                };

                getResp.push(objToPush);
            }
        }

        return NextResponse.json({response: getResp}, {status: 200});
    } catch (error) {
        console.error('Error to generate report:', error);
        return NextResponse.json({error: 'Error to get data'}, {status: 500});
    }
}
