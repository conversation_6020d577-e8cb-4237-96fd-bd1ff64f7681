import {NextResponse} from "next/server";
import _ from "lodash";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect";

export async function POST(request: Request) {
    try {
        const {
            vendor,
            typeReport,
            weeksNumbers,
            years
        } = await request.json();
        if (_.isEmpty(vendor)) {
            return NextResponse.json({response: "No vendor selected"}, {status: 400})
        }
        const {db} = await adminConnect();
        const queryFind = {
            type: typeReport,
            vendor: vendor?._id,
            week: {$in: weeksNumbers},
            year: {$in: years}
        }
        const findReport = await db.collection('performance_reports').find(queryFind).project({
            _id: 0,
            report: 1,
            counts: 1
        }).toArray();
        const respRet = {response: (findReport.length > 0) ? findReport : []}
        return NextResponse.json(respRet, {status: 200});
    } catch (error) {
        console.error('Error to generate performance report:', error);
        const errorRet = {error: 'Error to get data'}
        return NextResponse.json(errorRet, {status: 500})
    }
}
