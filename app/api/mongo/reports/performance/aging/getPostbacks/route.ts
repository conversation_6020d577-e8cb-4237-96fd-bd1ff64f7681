import {NextResponse} from "next/server";
import {buildPostbacksAgingAggregation} from "@/app/_lib/nova/functions/performance/buildPostbacksAgingAggregation";
import {aggregationPerformanceReport} from "@/app/_lib/nova/functions/aggregations/aggregationPerformanceReport";

export async function POST(request: Request) {
    try {
        const {
            transfers,
            bucket,
            optionsReport
        } = await request.json();
        const getAggregation = buildPostbacksAgingAggregation(transfers);
        const finalResponse = await aggregationPerformanceReport("postbacks", getAggregation, {});
        const response = {
            bucket,
            total: finalResponse[0]?.total ?? 0
        };
        const respRet = {response}
        return NextResponse.json(respRet, {status: 200});
    } catch (error) {
        console.error('Error to get postbacks aging report:', error);
        const errorRet = {error: 'Error to get data'}
        return NextResponse.json(errorRet, {status: 500})
    }
}
