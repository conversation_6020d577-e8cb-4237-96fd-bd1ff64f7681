import {NextResponse} from "next/server";
import {buildTransfersAgingAggregation} from "@/app/_lib/nova/functions/performance/buildTransfersAgingAggregation";
import {aggregationPerformanceReport} from "@/app/_lib/nova/functions/aggregations/aggregationPerformanceReport";

export async function POST(request: Request) {
    try {
        const {
            leads,
            optionsReport
        } = await request.json();
        const valueReport = optionsReport.find((itm: any) => itm.collection === "transfers");
        const {collection, key3, key5} = valueReport;
        const getAggregation = buildTransfersAgingAggregation(leads);
        const finalResponse = await aggregationPerformanceReport(collection, getAggregation, {});
        const respRet = {response: finalResponse}
        return NextResponse.json(respRet, {status: 200});
    } catch (error) {
        console.error('Error to get transfers aging report:', error);
        const errorRet = {error: 'Error to get data'}
        return NextResponse.json(errorRet, {status: 500})
    }
}
