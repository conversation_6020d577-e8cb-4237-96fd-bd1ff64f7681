import {NextResponse} from "next/server";
import _ from "lodash";
import {buildFiltersPerformance} from "@/app/_lib/nova/functions/performance/buildFiltersPerformance";
import {aggregationPerformanceReport} from "@/app/_lib/nova/functions/aggregations/aggregationPerformanceReport";
import {buildLeadsAgingAggregation} from "@/app/_lib/nova/functions/performance/buildLeadsAgingAggregation";
import {buildFilters} from "@/app/_lib/nova/functions/utils/buildFilters";
import {DateTime} from "luxon";

export async function POST(request: Request) {
    try {
        const {
            vendor,
            optionsReport,
            rangeDate,
            context
        } = await request.json();
        if (_.isEmpty(vendor)) {
            return NextResponse.json({response: "No vendor selected"}, {status: 400})
        }
        if (_.isEmpty(vendor.pub_ids)) {
            return NextResponse.json({response: []}, {status: 200});
        }
        const valueReport = optionsReport.find((itm: any) => itm.collection === "leads");
        const {collection, hint, filters, key3, key5} = valueReport;
        const mongoFilters = buildFiltersPerformance(filters, context);
        const startUTC = DateTime.fromISO(rangeDate?.startDate).toUTC().toJSDate();
        const endUTC = DateTime.fromISO(rangeDate?.endDate).toUTC().toJSDate();
        mongoFilters[key3] = {$gte: startUTC, $lte: endUTC}

        const getAggregation = buildLeadsAgingAggregation(key5, vendor._id, mongoFilters);
        const finalResponse = await aggregationPerformanceReport(collection, getAggregation, hint);
        const respRet = {response: finalResponse}
        return NextResponse.json(respRet, {status: 200});
    } catch (error) {
        console.error('Error to get leads aging report:', error);
        const errorRet = {error: 'Error to get data'}
        return NextResponse.json(errorRet, {status: 500})
    }
}
