import {NextResponse} from "next/server";
import _ from "lodash";

export async function POST(request: Request) {
    try {
        const {vendor, optionsReport, campaigns, results} = await request.json();

        if (_.isEmpty(vendor)) {
            return NextResponse.json({response: "No vendor selected"}, {status: 400});
        }
        if (_.isEmpty(vendor.pub_ids)) {
            return NextResponse.json({response: []}, {status: 200});
        }
        if (_.isEmpty(results)) {
            return NextResponse.json({response: "No results available"}, {status: 400});
        }

        const finalResponse: any[] = [];
        const collectionCounts: any = {};

        for (const valueReport of optionsReport) {
            const {collection} = valueReport;
            const resultsCollection = results.find((itm: any) => itm.collection === collection);

            if (!_.isEmpty(resultsCollection?.response)) {
                if (!collectionCounts[collection]) {
                    collectionCounts[collection] = 0;
                }

                for (const campaignInfo of resultsCollection?.response) {
                    let campaignIndex = _.findIndex(finalResponse, (itm: any) => itm.campaignKey === campaignInfo?._id);

                    if (campaignIndex === -1) {
                        const objCampaign = {
                            campaignKey: campaignInfo?._id,
                            [`campaignKey_${collection}`]: campaignInfo?.total ?? 0,
                            subIds: []
                        }
                        additionalOpts(valueReport?.additions, objCampaign, "campaignKey", collection, campaignInfo);
                        finalResponse.push(objCampaign);
                        campaignIndex = finalResponse.length - 1;
                    } else {
                        additionalOpts(valueReport?.additions, finalResponse[campaignIndex], "campaignKey", collection, campaignInfo);
                        finalResponse[campaignIndex][`campaignKey_${collection}`] = campaignInfo?.total ?? 0;
                    }

                    const campaignKeyInfoToEdit: any = finalResponse[campaignIndex];

                    if (!_.isEmpty(campaignInfo?.infoSubId)) {
                        for (const subIdInfo of campaignInfo.infoSubId) {
                            const subIdIndex = _.findIndex(campaignKeyInfoToEdit.subIds, (itm: any) => itm.subId === subIdInfo._id);

                            if (subIdIndex === -1) {
                                const objSubId = {
                                    subId: subIdInfo._id,
                                    [`subId_${collection}`]: subIdInfo?.total ?? 0,
                                }
                                additionalOpts(valueReport?.additions, objSubId, "subId", collection, subIdInfo);
                                campaignKeyInfoToEdit.subIds.push(objSubId);
                            } else {
                                additionalOpts(valueReport?.additions, campaignKeyInfoToEdit.subIds[subIdIndex], "subId", collection, subIdInfo);
                                campaignKeyInfoToEdit.subIds[subIdIndex][`subId_${collection}`] = subIdInfo?.total ?? 0;
                            }
                        }
                    }

                    collectionCounts[collection] += campaignInfo?.total ?? 0;
                }
            }
        }

        const finalResult = {report: finalResponse, counts: collectionCounts};
        const respRet = {response: finalResult};
        return NextResponse.json(respRet, {status: 200});
    } catch (error) {
        console.error('Error to generate performance report:', error);
        const errorRet = {error: 'Error to get data'};
        return NextResponse.json(errorRet, {status: 500});
    }
}

function additionalOpts(additions: any, object: any, type: string, collection: string, response: any) {
    if (additions) {
        additions.map((item: any) => {
            if (item?.name) {
                object[`${type}_${collection}${item?.name}`] = response[item?.name] || 0;
            }
        })
    }
}

function buildArrayReport(array: Array<any>, name: string, collection: string, subIds: any) {
    for (const infoArray of array) {
        const subIdName = !_.isEmpty(infoArray?._id) ? infoArray._id : "Unknown";
        const subIdIndex = _.findIndex(subIds, (itm: any) => itm[name] === subIdName);
        let subIdInfoToEdit: any;

        if (subIdIndex !== -1) {
            subIdInfoToEdit = subIds[subIdIndex];
        } else {
            subIdInfoToEdit = {[name]: subIdName};
            subIds.push(subIdInfoToEdit);
        }

        subIdInfoToEdit[`${name}_${collection}`] = infoArray.total || 0;
    }
}