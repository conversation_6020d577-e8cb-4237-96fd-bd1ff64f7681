import _ from "lodash";
import {aggregationPerformanceReport} from "@/app/_lib/nova/functions/aggregations/aggregationPerformanceReport";
import {
    buildAggregationPerformanceReport
} from "@/app/_lib/nova/functions/performance/buildAggregationPerformanceReport";
import {buildFiltersPerformance} from "@/app/_lib/nova/functions/performance/buildFiltersPerformance";
import {DateTime} from "luxon";
import {NextResponse} from "next/server";

export async function POST(request: Request) {
    try {
        const {
            vendor,
            optionsReport,
            optSelect,
            dayRange,
            context
        } = await request.json();

        if (_.isEmpty(vendor)) {
            return NextResponse.json({error: "No vendor selected"}, {status: 400});
        }

        const valueReport = optionsReport.find((itm: any) => itm.collection === optSelect);
        const {
            collection,
            hint,
            additions,
            filters,
            key1,
            key2,
            key3,
            key4,
            key5,
            key6
        } = valueReport;

        const allKeys: any = {key1, key2, key3, key4, key5, key6};

        const mongoFilters = buildFiltersPerformance(filters, context);
        const startUTC = DateTime.fromISO(dayRange?.startDate).toUTC().toJSDate();
        const endUTC = DateTime.fromISO(dayRange?.endDate).toUTC().toJSDate();
        mongoFilters[key3] = {$gte: startUTC, $lte: endUTC};

        const getAggregation = buildAggregationPerformanceReport(
            vendor._id,
            mongoFilters,
            allKeys,
            additions
        );

        const partialResponse = await aggregationPerformanceReport(collection, getAggregation, hint);

        return NextResponse.json({data: partialResponse}, {status: 200});

    } catch (error) {
        console.error('Error generating performance report:', error);
        return NextResponse.json({error: 'Internal error generating report'}, {status: 500});
    }
}
