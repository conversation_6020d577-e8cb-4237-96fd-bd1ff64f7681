import {NextResponse} from "next/server";
import adminConnect from "@/app/_lib/mongo/admin/adminConnect";

export async function GET() {
    try {
        const {db} = await adminConnect()
        let vendors = []
        let clients = []

        vendors = await db.collection("vendors").find()
            .sort({vendor: 1})
            .project({
                _id: 1,
                vendor: 1
            }).toArray();

        clients = await db.collection("clients").find()
            .sort({client: 1})
            .project({
                _id: 1,
                client: 1
            }).toArray();

        vendors.push({
            _id: true,
            vendor: `No assign vendor`
        })

        clients.push({
            _id: true,
            client: `No assign client`
        })
        return NextResponse.json({response: {vendors, clients}}, {status: 200});
    } catch (error) {
        console.error('Error to generate report:', error);
        const errorRet = {error: 'Error to get data'}
        return NextResponse.json(errorRet, {status: 500})
    }
}
