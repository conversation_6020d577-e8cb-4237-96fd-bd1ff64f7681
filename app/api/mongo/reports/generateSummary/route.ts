import {buildFilters} from "@/app/_lib/nova/functions/utils/buildFilters";
import _ from "lodash";
import {buildRestrictionsTables} from "@/app/_lib/nova/functions/utils/buildRestrictionsTables";
import {checkRestrictions} from "@/app/_lib/nova/functions/better-auth/checkRestrictions";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";
import {DateTime} from "luxon";
import {buildComplexGroupObj} from "@/app/_lib/utils/reports/buildComplexGroupObj";
import {aggregationSummaryReports} from "@/app/_lib/nova/functions/aggregations/aggregationSummaryReports";
import {buildGroupObj} from "@/app/_lib/utils/reports/buildGroupObj";
import {buildGroupObjVenCli} from "@/app/_lib/utils/reports/buildGroupObjVenCli";
import {NextResponse} from "next/server";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
    try {
        const {
            dateOnlyMode,
            filters,
            table,
            requiredKey,
            keysProAgg,
            selectObj,
            selectDetail,
            thirdLevelDetail,
            optionsCond,
            pickFil,
            context = {timezone: "America/Chicago"},
            chunkDates,
            matchKey,
            matchValue,
            nameMatch
        } = await request.json();

        if (!chunkDates?.[0]) {
            return NextResponse.json({error: "No date range selected"}, {status: 400});
        }

        const dayRange = chunkDates[0];

        const user: any = await getUserSession();
        const {metadata} = user || {metadata: null};
        const jsonMetadata = metadata ? JSON.parse(metadata) : null;

        const {response, error, status} = await checkRestrictions(table, jsonMetadata ?? {});
        if (status !== 200) {
            return NextResponse.json({error: error}, {status: status});
        }

        let matchFilters: any = {};
        if (dateOnlyMode) {
            const startUTC = DateTime.fromISO(dayRange?.startDate).toUTC().toJSDate();
            const endUTC = DateTime.fromISO(dayRange?.endDate).toUTC().toJSDate();
            matchFilters[pickFil] = {$gte: startUTC, $lte: endUTC};
        } else {
            const dayFilters = {
                ...filters,
                [`${pickFil}-between`]: {
                    filterType: 'between',
                    value: {start: dayRange?.startDate, end: dayRange?.endDate}
                }
            };

            matchFilters = buildFilters(dayFilters, context);
            if (matchKey && matchValue) {
                if (_.toLower(matchKey) === "unknown") {
                    matchFilters["$or"] = [
                        {[matchKey]: ""},
                        {[matchKey]: null},
                        {[matchKey]: {$exists: false}},
                        {[matchKey]: matchValue}
                    ];
                } else {
                    matchFilters[matchKey] = matchValue;
                }
            }
        }

        const restrictFilters = await buildRestrictionsTables(
            table,
            response.restrictions.vendors,
            response.restrictions.clients,
            {}
        );

        matchFilters = {
            ...matchFilters,
            ...buildFilters(restrictFilters, context)
        };

        const buildObjFunc = dateOnlyMode ? buildGroupObj : buildGroupObjVenCli;
        const {project0, group, lookup, unwind, project} = buildObjFunc(
            keysProAgg,
            selectObj,
            requiredKey,
            optionsCond,
            matchKey,
            nameMatch
        );

        let getResp = [];

        if (selectDetail?.value === "none") {
            getResp = (_.isEmpty(group)) ? [] :
                await aggregationSummaryReports(table, matchFilters, {
                    project0,
                    group,
                    lookup,
                    unwind,
                    project
                }, {}, selectObj?.show);
        } else {
            const {firstData, secondData} = buildComplexGroupObj(
                {project0, group, lookup, unwind, project},
                {
                    selectObj,
                    selectDetail,
                    requiredKey,
                    optionsCond,
                    thirdLevelDetail,
                    pickDate: pickFil
                },
                dateOnlyMode
            );

            getResp = await aggregationSummaryReports(
                table,
                matchFilters,
                firstData,
                secondData,
                selectObj?.show
            );
        }

        return NextResponse.json({data: getResp}, {status: 200});
    } catch (error) {
        console.error("Summary API error:", error);
        return NextResponse.json({error: "Error processing request"}, {status: 500});
    }
}
