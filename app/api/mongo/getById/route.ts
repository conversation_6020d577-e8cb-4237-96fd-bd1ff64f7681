import { NextResponse } from "next/server";
import getItemById from "@/app/_lib/nova/functions/getById";

export async function POST(request: Request) {
    try {
        const payload = await request.json();
        const getItmResp = await getItemById(payload);
        const response =
            getItmResp.status === 200
                ? { response: getItmResp.response }
                : { error: getItmResp.error };

        return NextResponse.json(response, { status: getItmResp.status });
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Error processing request" },
            { status: 500 }
        );
    }
}