import {NextResponse} from "next/server";
import {deleteTemplateRole} from "@/app/_lib/nova/functions/deleteTemplateRole";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const response = await deleteTemplateRole(payload)
        return NextResponse.json({message: response.message}, {status: response.status})
    } catch (error) {
        console.log('Error processing delete template role request:', error)
        return NextResponse.json(error, {status: 500})
    }
}
