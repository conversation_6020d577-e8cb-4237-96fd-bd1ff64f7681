import {NextResponse} from 'next/server';
import _ from "lodash";
import {getDataForAdminUsers} from "@/app/_lib/nova/functions/getDataForAdminUsers";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const getData = await getDataForAdminUsers(payload)
        const status = (_.isEmpty(getData?.error)) ? 200 : 400
        return NextResponse.json({response: getData}, {status});
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(error, {status: 500})
    }
}
