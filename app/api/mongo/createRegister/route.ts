import {NextResponse} from "next/server";
import createRegister from "@/app/_lib/nova/functions/createRegister";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const response = await createRegister(payload)
        return NextResponse.json(response)
    } catch (error) {
        console.log('Error processing create register request:', error)
        return NextResponse.error()
    }
}
