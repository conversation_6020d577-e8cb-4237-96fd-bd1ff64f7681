import updateObjects from "@/app/_lib/nova/functions/updateObjects";
import {NextResponse} from "next/server";

export async function PUT(request: Request) {
    try {
        const payload = await request.json()
        const response = await updateObjects(payload)
        return NextResponse.json(response)
    } catch (error) {
        console.log('Error processing create register request:', error)
        return NextResponse.error()
    }
}
