import {NextResponse} from "next/server";
import {getUpdateConfigurations} from "@/app/_lib/nova/functions/getUpdateConfigurations.js";

export async function POST(request) {
    try {
        let payload = await request.json()
        const response = await getUpdateConfigurations(payload);
        return NextResponse.json(response)
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.error()
    }
}
