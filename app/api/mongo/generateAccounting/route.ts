import {NextResponse} from "next/server";
import generateAccounting from "@/app/_lib/nova/functions/accounting/generateAccounting";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const response = await generateAccounting(payload)
        return NextResponse.json(response)
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.error()
    }
}
