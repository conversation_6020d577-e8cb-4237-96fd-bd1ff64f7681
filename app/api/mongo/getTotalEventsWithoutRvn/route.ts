import {NextResponse} from "next/server";
import getTotalEventsWithoutRvn from "@/app/_lib/nova/functions/getTotalEventsWithoutRvn";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const getItemsResponse = await getTotalEventsWithoutRvn(payload)
        return NextResponse.json(getItemsResponse)
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json({})
    }
}
