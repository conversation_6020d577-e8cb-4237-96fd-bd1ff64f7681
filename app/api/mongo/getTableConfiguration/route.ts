import {NextResponse} from "next/server";
import {getTableConfigurationByTableName} from "@/app/_lib/nova/functions/getTableConfigurationByTableName";
import _ from "lodash";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const dataTable = await getTableConfigurationByTableName(payload)
        const response = (!_.isEmpty(dataTable)) ? {response: dataTable} : {}
        return NextResponse.json(response, {status: 200})
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json({error: 'Error processing request'}, {status: 500})
    }
}
