import { NextResponse } from "next/server";
import getItemsById from "@/app/_lib/nova/functions/call-center/getItemsById";

export async function POST(request: Request) {
    try {
        const payload = await request.json();
        const itemsResp = await getItemsById(payload);
        const isSuccess = itemsResp.status === 200 || itemsResp.status === undefined;

        const responseBody = isSuccess
            ? { response: itemsResp.response }
            : { error: itemsResp.error };

        return NextResponse.json(responseBody, {
            status: itemsResp.status ?? 200,
        });
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Error processing request" },
            { status: 500 }
        );
    }
}