import {NextRequest, NextResponse} from "next/server";
import {getDistinctValues} from "@/app/_lib/nova/functions/getDistinctValues";
import {getExternalDistinctValues} from "@/app/_lib/nova/functions/getExternalDistinctValues";
import _ from "lodash";

export async function GET(req: NextRequest, {params}: { params: { accountingType: string; collection: string } }) {
    try {
        const optionsSelect = await getOptionsForCollection();
        return NextResponse.json(optionsSelect);
    } catch (error) {
        if (error?.response?.data?.errorMessage) {
            return NextResponse.json({errorMessage: error.response.data.errorMessage}, {status: 400});
        }
        return NextResponse.error();
    }
}

const getOptionsSelect = async (collection: string, key: string) => {
    const distinctValues = await getDistinctValues([key], collection);
    let items= distinctValues[key].map((item: string) => ({
        label: item !== "" ? item : "Empty",
        value: item,
    }));
    return _.orderBy(items, ['label'], ['asc']);
};

const getDynamicOptionsSelect = async (collection: string, distinctConfig: any[]) => {
    const result = await getExternalDistinctValues(distinctConfig, collection);
    const key = distinctConfig[0]?.key;
    let items= result[key] || [];
    return _.orderBy(items, ['label'], ['asc']);
};

const getOptionsForCollection = async () => {
    const optionsSelect: Record<string, any> = {};
    const commonOptions = [
        {key: "campaign_key", table: "leads", column: "campaign_key"},
        {key: "pub_id", table: "leads", column: "pubid"},
    ];

    for (const option of commonOptions) {
        optionsSelect[option.key] = await getOptionsSelect(option.table, option.column);
    }

    const configurations = [
        {id: "_id", label: "vendor", table: "vendors", key: "vendor"},
        {id: "label", label: "label", table: "accounting_labels", key: "label"},
        {id: "_id", label: "client", table: "clients", key: "client_name"}
    ]
    for (const config of configurations) {
        optionsSelect[config.label] = await getDynamicOptionsSelect(config.table, [config]);
    }

    return optionsSelect;
};
