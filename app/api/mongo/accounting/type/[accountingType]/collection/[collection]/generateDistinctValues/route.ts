import {NextRequest, NextResponse} from "next/server";
import {getDistinctValues} from "@/app/_lib/nova/functions/getDistinctValues";
import {getExternalDistinctValues} from "@/app/_lib/nova/functions/getExternalDistinctValues";
import _ from "lodash";

export async function GET(req: NextRequest, {params}: { params: { accountingType: string; collection: string } }) {
    try {
        const {accountingType, collection} = params;
        const optionsSelect = await getOptionsForCollection(collection, accountingType);
        return NextResponse.json(optionsSelect);
    } catch (error) {
        if (error?.response?.data?.errorMessage) {
            return NextResponse.json({errorMessage: error.response.data.errorMessage}, {status: 400});
        }
        return NextResponse.error();
    }
}

const getOptionsSelect = async (collection: string, key: string) => {
    const distinctValues = await getDistinctValues([key], collection);
    let items = distinctValues[key].map((item: string) => ({
        label: item !== "" ? item : "Empty",
        value: item,
    }));
    return _.orderBy(items, ['label'], ['asc']);
};

const getDynamicOptionsSelect = async (collection: string, distinctConfig: any[]) => {
    const result = await getExternalDistinctValues(distinctConfig, collection);
    const key = distinctConfig[0]?.key;
    const items= result[key] || [];
    return _.orderBy(items, ['label'], ['asc']);
};

const getOptionsForCollection = async (collection: string, accountingType: string) => {
    const optionsSelect: Record<string, any> = {};
    const commonOptions = [
        {key: "campaignKey", table: "leads", column: "campaign_key"},
        {key: "pubId", table: "leads", column: "pubid"},
    ];

    for (const option of commonOptions) {
        optionsSelect[option.key] = await getOptionsSelect(option.table, option.column);
    }
    const dynamicConfigurations: Record<string, any[]> = {
        leads: accountingType === "EXPENSE"
            ? [{id: "_id", label: "vendor", table: "vendors", key: "vendor"}]
            : [],
        transfers: [
            {id: "_id", label: "client", table: "clients", key: "client_name"},
            ...(accountingType === "REVENUE"
                ? [{id: "_id", label: "client", table: "clients", key: "client_name"}]
                : []),
            ...(accountingType === "EXPENSE"
                ? [{id: "_id", label: "vendor", table: "vendors", key: "vendor"}]
                : []),
        ],
        postbacks: [
            {id: "_id", label: "client", table: "clients", key: "client"},
            ...(accountingType === "REVENUE"
                ? [{id: "_id", label: "client", table: "clients", key: "client_name"}]
                : []),
            ...(accountingType === "EXPENSE"
                ? [{id: "_id", label: "vendor", table: "vendors", key: "vendor"}]
                : []),
        ],
    };

    const configurations = dynamicConfigurations[collection] || [];
    for (const config of configurations) {
        optionsSelect[config.label] = await getDynamicOptionsSelect(config.table, [config]);
    }

    return optionsSelect;
};
