import {NextResponse} from "next/server";
import {getSummary} from "@/app/_lib/nova/functions/accounting/getSummary";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const user: any = await getUserSession()
        const {metadata} = user || {metadata: null};
        const jsonMetadata = metadata ? JSON.parse(metadata) : null;
        // const summaryResponse = await verifyRolesMiddleware({...payload, authMetadata}, getSummary)
        const summaryResponse = await getSummary(payload);
        // const response = (summaryResponse.status === 200) ? {response: summaryResponse.response} : {error: summaryResponse.error}
        return NextResponse.json(summaryResponse, {status: 200})
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json({error: 'Error processing request'}, {status: 500})
    }
}
