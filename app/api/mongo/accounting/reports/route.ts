import {NextResponse} from "next/server";
import {getAccountingReport} from "@/app/_lib/nova/functions/accounting/getAccountingReport";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const user: any = await getUserSession()
        const {metadata, role} = user || {metadata: null, role: null};
        const jsonMetadata = metadata ? JSON.parse(metadata) : null;
        const accountingResponse = await getAccountingReport(payload);
        return NextResponse.json(accountingResponse, {status: 200})
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json({error: 'Error processing request'}, {status: 500})
    }
}
