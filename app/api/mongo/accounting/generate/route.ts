import {NextResponse} from "next/server";
import {params} from "@ampt/sdk";
import axios from "axios";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
    try {
        let payload = await request.json();
        const user: any = await getUserSession()
        const {email} = user || {email: null};
        if (!user || !email) {
            return NextResponse.json({error: 'User mail not found'}, {status: 400})
        }
        const dataBrokerHost = params('DATA_BROKER_HOST');
        const dataBrokerKey = params('DATA_BROKER_KEY');
        payload['userEmail'] = email;
        const task = 'generateAccountingTask'
        const response = await axios.post(`${dataBrokerHost}/api/secure/accounting`,
            {payload, task}, {headers: {'x-api-key': dataBroker<PERSON>ey}})
        return NextResponse.json(response.data);
    } catch (error) {
        if (error && error.response && error.response.data && error.response.data.errorMessage) {
            return NextResponse.json({errorMessage: error.response.data.errorMessage}, {status: 400})
        }
        return NextResponse.error()
    }
}
