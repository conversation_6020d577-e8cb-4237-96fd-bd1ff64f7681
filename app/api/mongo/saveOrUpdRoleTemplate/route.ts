import {NextResponse} from "next/server";
import {upsertRoleTemplate} from "@/app/_lib/nova/functions/upsertRoleTemplate";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const response = await upsertRoleTemplate(payload)
        return NextResponse.json({message: response.message}, {status: response.status})
    } catch (error) {
        console.error('Error processing upsertRoleTemplate request:', error);
        return NextResponse.json(error, {status: 500})
    }
}
