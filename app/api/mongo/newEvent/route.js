import {NextResponse} from "next/server";
import {newEvent} from "@/app/_lib/nova/functions/newEvent";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession.js";

export const dynamic = 'force-dynamic';

export async function POST(request) {
    try {
        const user = await getUserSession()
        const {email, name} = user || {email: null, name: null};
        let payload = await request.json()
        payload = {...payload, userId: user?.id, user: name || 'N/A', userEmail: email, task: 'completeAccountingTask'}
        await newEvent(payload)
        return NextResponse.json({})
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.error()
    }
}
