import {NextResponse} from "next/server";
import {getCampaignKeys} from "@/app/_lib/nova/functions/getCampaignKeys";
import _ from "lodash";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";

export const dynamic = 'force-dynamic';

export async function GET() {
    try {
        const user: any = await getUserSession()
        const {email} = user || {email: null};
        if (!user || !email) {
            return NextResponse.json({error: 'User mail not found'}, {status: 400})
        }
        let response = await getCampaignKeys();
        response = _.orderBy(response, ['name'], ['asc']);
        return NextResponse.json(response);
    } catch (error) {
        console.log('Error: ', error)
        if (error && error.response && error.response.data && error.response.data.errorMessage) {
            return NextResponse.json({errorMessage: error.response.data.errorMessage}, {status: 400})
        }
        return NextResponse.error()
    }
}
