import {NextResponse} from 'next/server';
import _ from "lodash";
import {getDataForQuickForm} from "@/app/_lib/nova/functions/getDataForQuickForm";

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const getData = await getDataForQuickForm(payload)
        const status = (_.isEmpty(getData?.error)) ? 200 : 400
        const response = (status === 200) ? {response: getData} : {error: getData.error}
        return NextResponse.json(response, {status});
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(error, {status: 500})
    }
}
