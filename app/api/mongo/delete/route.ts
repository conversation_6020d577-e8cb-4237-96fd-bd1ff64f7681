import {verifyRolesMiddleware} from "@/app/_lib/utils/verifyRolesMiddleware";
import {NextResponse} from "next/server";
import {deleteRow} from "@/app/_lib/nova/functions/deleteRow";
import {getUserSession} from "@/app/_lib/utils/betterAuth/getUserSession";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const user: any = await getUserSession()
        const {metadata} = user || {metadata: null};
        const jsonMetadata = metadata ? JSON.parse(metadata) : null;
        const authMetadata = jsonMetadata || {};
        const getItmResp = await verifyRolesMiddleware({...payload, authMetadata}, deleteRow)
        const response = (getItmResp.status === 200) ? {response: getItmResp.response} : {error: getItmResp.error}
        return NextResponse.json(response, {status: getItmResp.status})
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json({error: 'Error processing request'}, {status: 500})
    }
}
