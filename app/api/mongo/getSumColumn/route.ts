import {NextResponse} from "next/server";
import {getColumnSum} from "@/app/_lib/nova/functions/getColumnSum";

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
    try {
        const payload = await request.json()
        const {column, collection, filters} = payload;
        const getColumnSumResponse = await getColumnSum(column, collection, filters)
        return NextResponse.json(getColumnSumResponse)
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json({})

    }
}
