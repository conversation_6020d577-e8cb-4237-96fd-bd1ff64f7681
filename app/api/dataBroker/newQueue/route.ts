import {NextResponse} from "next/server";
import axios from "axios";
import {params} from "@ampt/sdk";

export async function POST(request: Request) {
    try {
        const dataBrokerHost = params('DATA_BROKER_HOST');
        const payload = await request.json();
        // const response = await axios.post(`${dataBrokerHost}/api/secure/executeEvent`,
        //     {config: {task: payload.table}, event: 'MATCH_UNKNOWN_DATA_EVENT'},
        //     {headers: {'x-api-key': 123123, 'Content-Type': 'application/json'}}
        // );
        // return NextResponse.json({message: 'ok'});
        const response = await axios.post(`${dataBrokerHost}/api/matchTask`,
            {task: payload.table});
        return NextResponse.json(response.data);
    } catch (error) {
        const message = error?.response?.data?.message ? error.response.data.message :
            error?.response?.data ? error?.response?.data : 'Error on generate task';
        return NextResponse.json({error: message},
            {status: 400})
    }
}
