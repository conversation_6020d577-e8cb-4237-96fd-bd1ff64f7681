import { NextResponse } from "next/server";
import {endChatSession} from "@/app/_lib/vectra/endChatSession";

export async function POST(req) {
    try {
        const { sessionId } = await req.json();
        const result = await endChatSession(sessionId);
        return NextResponse.json(result.body, { status: result.status });
    } catch (err) {
        return NextResponse.json({ error: err.message }, { status: 500 });
    }
}