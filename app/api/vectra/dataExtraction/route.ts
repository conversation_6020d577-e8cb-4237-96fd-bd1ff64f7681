import {NextResponse} from "next/server";
import {metaDataExtractionAgent} from "@/app/_lib/vectra/actions/metaDataExtractionAgent.js";
import callCenterConnect from "@/app/_lib/mongo/callCenter/callCenterConnect";

export const runtime = 'nodejs';

export async function POST(request: Request) {
    try {
        const {db} = await callCenterConnect();

        const payload = await request.json();
        const {campaignId, transcript, metadata, finalIntent} = payload;
        const callCampaignData = await db.collection('campaigns').findOne({_id: campaignId});
        const scriptDataMap = await db.collection('data_map').findOne({callCampaignId: campaignId});
        const dataPromptConfig = await db.collection('configuration').findOne({configuration: 'promptDataExtraction'});
        const variables = callCampaignData?.variables ?? [];
        const dataMap = scriptDataMap?.data ?? {};
        const finalPrompt = dataPromptConfig?.data ?? "";

        let response = ""
        if (finalIntent === 'transferringCall') {
            response = await metaDataExtractionAgent(transcript, metadata, variables, dataMap, finalPrompt);
        } else {
            response = null
        }
        return NextResponse.json({response}, {status: 200});
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json({error: "Internal Server Error"}, {status: 500});
    }
}
