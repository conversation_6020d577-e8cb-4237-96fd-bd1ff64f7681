import { NextRequest, NextResponse } from 'next/server';
import uploadFile from "@/app/api/gumloop/uploadFile";
import uploadPdfFile from "@/app/api/gumloop/upload-pdf";

export async function POST(req: NextRequest) {
    try {
        const formData = await req.formData();
        const originalFile = formData.get('pdf') as Blob;
        const callCampaignId = formData.get('callCampaignId') as string;

        if (!originalFile) {
            return NextResponse.json({ error: 'No file uploaded.' }, { status: 400 });
        }

        const fileBuffer = Buffer.from(await originalFile.arrayBuffer());

        const publicUrl = await uploadFile(fileBuffer, callCampaignId);
        const gumloopResponse = await uploadPdfFile(publicUrl);

        return NextResponse.json({ message: 'File uploaded successfully.', response: gumloopResponse });

    } catch (error: any) {
        console.error('Server error:', error);
        return NextResponse.json({ error: 'Internal server error.', details: error.message }, { status: 500 });
    }
}