import { params } from "@ampt/sdk";

export default async function uploadPdfFile(publicUrl) {
    const gumloopKey = params("gumloopKey");
    const gumloopUser = params("gumloopUser");
    const gumloopItem = params("gumloopItem");
    const options = {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${gumloopKey}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            "user_id": `${gumloopUser}`,
            "saved_item_id": `${gumloopItem}`,
            "pipeline_inputs": [{ "input_name": "file url", "value": publicUrl }]
        })
    };

    try {
        const response = await fetch('https://api.gumloop.com/api/v1/start_pipeline', options);
        const jsonResponse = await response.json();

        if (jsonResponse.run_id) {
            await new Promise(resolve => setTimeout(resolve, 5000));
            return await getGumloopResult(jsonResponse.run_id);
        }

        return jsonResponse;
    } catch (err) {
        console.error("Error connecting to Gumloop:", err);
        throw new Error("Error starting the process in Gumloop");
    }
}

async function getGumloopResult(runId) {
    const options = {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer a52a4775d3d9400b99de5fb3987e6d83',
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await fetch(`https://api.gumloop.com/api/v1/get_pl_run?run_id=${runId}&user_id=****************************`, options);
        const result = await response.json();
        return result.outputs;
    } catch (err) {
        console.error("Error retrieving Gumloop result:", err);
        throw new Error("Error retrieving Gumloop result");
    }
}