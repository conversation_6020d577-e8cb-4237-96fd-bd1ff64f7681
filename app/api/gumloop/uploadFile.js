import { storage } from '@ampt/sdk';

export default async function uploadFile(fileBuffer, callCampaignId) {
    try {
        const publicBucket = storage('public');
        const filePath = `/script_configuration/${callCampaignId}/configuration.pdf`;

        await publicBucket.write(filePath, fileBuffer, {
            type: 'application/pdf',
        });

        const amptRegionUrl = process.env.AMPT_URL;
        const publicUrl = `${amptRegionUrl}/public${filePath}`;

        console.log(`File uploaded to Ampt Storage at: ${filePath}`);
        return publicUrl;
    } catch (error) {
        console.error('Error uploading file:', error);
        throw new Error('Error uploading file');
    }
}