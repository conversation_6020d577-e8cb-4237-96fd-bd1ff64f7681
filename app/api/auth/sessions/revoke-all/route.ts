import { auth } from "@/app/_lib/auth/auth";
import { NextRequest, NextResponse } from "next/server";
import { MongoClient } from "mongodb";

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Conectar a MongoDB y revocar todas las sesiones excepto la actual
    const client = new MongoClient(process.env.MONGO_URI!);
    await client.connect();

    const db = client.db("core_auth");
    const sessionsCollection = db.collection("sessions");

    // Obtener el token de la sesión actual
    const currentSession = await sessionsCollection.findOne({
      userId: session.user.id,
      expires: { $gt: new Date() },
    });

    if (!currentSession) {
      await client.close();
      return NextResponse.json(
        { error: "Current session not found" },
        { status: 404 }
      );
    }

    // Revocar todas las sesiones excepto la actual
    const result = await sessionsCollection.deleteMany({
      userId: session.user.id,
      sessionToken: { $ne: currentSession.sessionToken }, // No eliminar la sesión actual
    });

    await client.close();

    return NextResponse.json({
      success: true,
      message: `${result.deletedCount} sessions revoked successfully`,
      revokedCount: result.deletedCount,
    });
  } catch (error) {
    console.error("Error revoking all sessions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
