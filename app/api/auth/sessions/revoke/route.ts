import { auth } from "@/app/_lib/auth/auth";
import { NextRequest, NextResponse } from "next/server";
import { MongoClient } from "mongodb";

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { token } = body;

    if (!token) {
      return NextResponse.json({ error: "Token is required" }, { status: 400 });
    }

    // Conectar a MongoDB y revocar la sesión
    const client = new MongoClient(process.env.MONGO_URI!);
    await client.connect();

    const db = client.db("core_auth");
    const sessionsCollection = db.collection("sessions");

    // Verificar que la sesión pertenece al usuario actual
    const sessionToRevoke = await sessionsCollection.findOne({
      sessionToken: token,
      userId: session.user.id,
    });

    if (!sessionToRevoke) {
      await client.close();
      return NextResponse.json({ error: "Session not found" }, { status: 404 });
    }

    // Revocar la sesión (eliminarla de la base de datos)
    await sessionsCollection.deleteOne({
      sessionToken: token,
      userId: session.user.id,
    });

    await client.close();

    return NextResponse.json({
      success: true,
      message: "Session revoked successfully",
    });
  } catch (error) {
    console.error("Error revoking session:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
