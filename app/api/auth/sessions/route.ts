import { auth } from "@/app/_lib/auth/auth";
import { NextRequest, NextResponse } from "next/server";
import { MongoClient } from "mongodb";

export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Conectar a MongoDB y obtener sesiones
    const client = new MongoClient(process.env.MONGO_URI!);
    await client.connect();

    const db = client.db("core_auth");
    const sessionsCollection = db.collection("sessions");

    // Obtener todas las sesiones del usuario
    const sessions = await sessionsCollection
      .find({
        userId: session.user.id,
        expires: { $gt: new Date() }, // Solo sesiones activas
      })
      .sort({ createdAt: -1 })
      .toArray();

    await client.close();

    // Transformar las sesiones al formato esperado
    const formattedSessions = sessions.map((session) => ({
      id: session._id.toString(),
      sessionToken: session.sessionToken,
      userId: session.userId,
      userAgent: session.userAgent || "Unknown Browser",
      createdAt: session.createdAt,
      expires: session.expires,
      updatedAt: session.updatedAt,
    }));

    return NextResponse.json(formattedSessions);
  } catch (error) {
    console.error("Error getting sessions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
