# Guía de Uso - Librería Shared Auth

## Implementación de la Librería Shared Auth

Esta guía explica cómo usar la librería `@conversionfinder/shared-auth/ui` en tu proyecto Next.js con BetterAuth.

## Implementación Principal

### UserSettingsProviderReal

Usa React Query para las mutaciones y authClient directamente.

**Archivo:** `app/(private)/user/[...alll]/_components/UserSettingsProviderReal.tsx`

**Características:**

- ✅ Mutaciones optimistas con React Query
- ✅ Invalidación automática de cache
- ✅ Manejo de errores robusto
- ✅ Estados de carga individuales
- ✅ Integración completa con BetterAuth

## Configuración Requerida

### 1. Dependencias

```bash
npm install @tanstack/react-query
npm link @conversionfinder/shared-auth
```

### 2. Provider de React Query

Asegúrate de tener el QueryClient configurado en tu layout:

```tsx
// app/layout.tsx
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient();

export default function RootLayout({ children }) {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}
```

### 3. Funciones de Auth

Las funciones de autenticación están en `app/_lib/auth/userActions.ts`:

- `updateUserName(name: string)`
- `revokeUserSession(token: string)`
- `revokeAllUserSessions()`
- `registerUserPasskey()`
- `deleteUserPasskey(passkeyId: string)`
- `deleteUserAccount()`

## Uso Básico

### 1. Importar el Componente

```tsx
import { UserSettingsProviderReal } from "./_components/UserSettingsProviderReal";
```

### 2. Usar en la Página

```tsx
export default async function UserPage() {
  const user = await getUserSession();
  return <UserSettingsProviderReal user={user} />;
}
```

## Configuración Personalizable

El componente acepta una configuración flexible:

```tsx
config={{
  showDeleteAccount: true,        // Mostrar sección de eliminar cuenta
  maxNameLength: 32,             // Longitud máxima del nombre
  dateFormat: "MM/dd/yyyy hh:mm a", // Formato de fecha
  timezone: "America/Chicago"    // Zona horaria
}}
```

## Estilos CSS

Los estilos están **incluidos automáticamente** en la librería. No es necesario importar CSS adicional.

**Características de los estilos:**

- ✅ Diseño responsive
- ✅ Soporte para modo oscuro
- ✅ Variables CSS personalizables
- ✅ Animaciones y transiciones
- ✅ Consistencia visual entre proyectos
- ✅ Estilos forzados con `!important` para evitar conflictos

**Nota:** Los estilos se importan automáticamente al usar `UserSettingsWithGlobalStyles`.

## Mapeo de Datos

La librería requiere mapear los datos de BetterAuth a los tipos esperados:

### Sessions

```tsx
const sessions: Session[] = sessionsData.map((session) => ({
  id: session.id,
  token: session.token,
  userAgent: session.userAgent || "Unknown",
  createdAt: session.createdAt.toISOString(),
  expiresAt: session.expiresAt.toISOString(),
}));
```

### Passkeys

```tsx
const passkeys: Passkey[] = passkeysData.map((passkey) => ({
  id: passkey.id,
  name: passkey.name || `Passkey ${passkey.id.slice(0, 8)}`,
  createdAt: passkey.createdAt.toISOString(),
  deviceType: passkey.deviceType,
}));
```

## Estados de Carga

El componente maneja estados de carga individuales:

```tsx
isUpdatingName = { isUpdatingName };
isRevokingSession = { isRevokingSession };
isRevokingAllSessions = { isRevokingAllSessions };
isRegisteringPasskey = { isRegisteringPasskey };
isDeletingPasskey = { isDeletingPasskey };
isDeletingAccount = { isDeletingAccount };
```

## Manejo de Errores

Los errores se manejan automáticamente con toast notifications:

```tsx
import { showError, showSuccess } from "@/app/_components/alerts/toast/ToastMessages";

// En las acciones
onSuccess: () => {
  showSuccess("Operación exitosa");
},
onError: (error) => {
  showError("Error en la operación");
  console.error("Error details:", error);
}
```

## Próximos Pasos

1. **Probar la implementación** - Verifica que todas las funciones trabajen correctamente
2. **Personalizar estilos** - Ajusta los estilos según tu diseño
3. **Agregar validaciones** - Implementa validaciones adicionales si es necesario
4. **Optimizar performance** - Considera lazy loading para secciones pesadas

## Troubleshooting

### Error de tipos

Si hay errores de tipos entre BetterAuth y la librería, verifica el mapeo de datos en las funciones de transformación.

### Errores de importación

Asegúrate de que la librería esté correctamente enlazada con `npm link`.

### Problemas de cache

Si los datos no se actualizan, verifica que las queries se invaliden correctamente después de las mutaciones.
