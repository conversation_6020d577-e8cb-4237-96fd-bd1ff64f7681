import {events, params} from "@ampt/sdk";
import {completeAccountingTask} from "../tasks/accountingTasks.js";
import delay from "../../../app/_lib/nova/functions/utils/delay.js";

const timeOut = params('completeAccountingEventTimeout') ? params('completeAccountingEventTimeout'): 55000;
events.on("completeAccountingEvent", {timeout: 50000}, async ({body}) => {
    console.log('completeAccountingEvent...')
    console.log('body: ', body);
    await delay(3000)

    //Verifica que no haya un proceso corriendo
    const taskResult = await completeAccountingTask.run(body)
    console.log('Task result: ', taskResult);
})
