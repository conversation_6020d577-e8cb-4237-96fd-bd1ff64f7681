import {task} from "@ampt/sdk";
import delay from "../../../app/_lib/nova/functions/utils/delay.js";
import * as taskList from "./tasks.js"
import adminConnect from "../../../app/_lib/mongo/admin/adminConnect.js";

const queueTasks = task("queueTasks", {timeout: 50000}, async (event, context) => {
    const {db} = await adminConnect();
    await delay(1000)
    console.log('queueTasks#event: ', event);
    const activeQueue = await db.collection("queues").findOne({status: 'in_progress'})
    const pendingQueue = await db.collection("queues").findOne({status: 'pending'})
    try {
        if (activeQueue) {
            console.log('A queue are in progress...')
            return;
        }
        if (pendingQueue) {
            console.log('A task pending found');
            await db.collection("queues").updateOne({_id: pendingQueue._id}, {
                $set: {
                    status: 'in_progress'
                }
            })
            console.log('QueueId:', pendingQueue._id);
            const queueTask = pendingQueue.task;
            const taskToExecute = taskList[queueTask];
            if (taskToExecute) {
                taskToExecute.run({payload: pendingQueue.payload, queueId: pendingQueue._id})
            } else {
                console.error(`Task ${queueTask} not found`)
            }
        }
    } catch (error) {
        console.log('Error', error)
        await db.collection("queues").updateOne({_id: pendingQueue._id}, {
            $set: {
                status: 'error'
            }
        })
        if (typeof error === 'object' && pendingQueue.payload.rvnExpId) {
            await db.collection("rvn_exp").updateOne({_id: pendingQueue.payload.rvnExpId}, {
                $set: {
                    status_task: 'error',
                    error: JSON.stringify(error)
                }
            })
        } else {
            await db.collection("rvn_exp").updateOne({_id: pendingQueue.payload.rvnExpId}, {
                $set: {
                    status_task: 'error',
                    error: error
                }
            })
        }
    }
});

export default queueTasks;
