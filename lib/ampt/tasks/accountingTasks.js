import {task} from "@ampt/sdk";
import generateAccounting from "../../../app/_lib/nova/functions/accounting/generateAccounting.js";

export const completeAccountingTask = task("completeAccountingTask", {timeout: 50000}, async (event, context) => {
    console.log('completeAccountingTask#event: ', event);
    console.log('Event', event)
    console.log('Body', event.body)
    const {payload} = event.body;
    console.log('rvnExpId: ', payload.rvnExpId);
    await generateAccounting(event.body);
});

