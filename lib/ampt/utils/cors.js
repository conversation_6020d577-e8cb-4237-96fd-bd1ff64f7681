import { params as p } from "@ampt/sdk";
const params = p().list();
export default function cors(event) {
  const { headers } = event.request;

  if (params("ENVIRONMENT_TYPE") === "personal") {
    event.response.headers.append(
      "Access-Control-Allow-Origin",
      "http://localhost:3000"
    );

    event.response.headers.append(
      "Access-Control-Allow-Methods",
      "GET, POST, PATCH, DELETE, OPTIONS"
    );

    if (headers.get("Access-Control-Request-Headers"))
      event.response.headers.append(
        "Access-Control-Allow-Headers",
        headers.get("Access-Control-Request-Headers")
      );
  }
}
