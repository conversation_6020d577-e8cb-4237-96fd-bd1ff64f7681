import { params as p  } from "@ampt/sdk";
import { data } from "@ampt/data";
const params = p().list();

export default async function auth(req, res, cb, authorize, onlyAdmin = false) {
	const maintenance = params.maintenance ? params.maintenance : false;
	if (maintenance !== 'true' && maintenance !== true) {
			let user = false
			if (authorize) {
					const {auth} = req.headers // 387209830923409
					const callBackName = cb?.name ?? "noCallback"
					if (!auth) {
							return res.status(401).send({error: "Authorization missing"})
					}
					user = await data.get(auth);
					if (!user) {
							return res.status(401).send({error: "Authorization failed"})
					}
					if (onlyAdmin) {
							if (!(_.has(user, "admin") && user.admin)) {
									return res.status(401).send({error: "Authorization not allowed, only admins"})
							}
					}
					if (!_.has(user, "admin")) {
							if (!_.has(user, callBackName)) {
									return res.status(401).send({error: "Authorization not allowed"})
							}
					}
			}
			// Callback
			cb(req, res, user)
	} else {
			res.status(400).send("Maintenance");
	}
}
