import connect from "../app/_lib/mongo/connect.js";
import _ from "lodash";

const {db} = await connect();

// Versiones de originalMenus
const originalMenusV2 = {
    "dashboard": ["showMenu"],
    "clients": ["showMenu"],
    "leads": ["showMenu", "reporting"],
    "postbacks": ["showMenu", "reporting"],
    "rvn_exp": ["showMenu"],
    "tags": ["showMenu"],
    "transfers": ["showMenu", "reporting"],
    "vendors": ["showMenu"]
};
const campaigns = []
const originalMenusV1 = {
    "tags": ["*"],
    "calls": ["*"],
    "leads": ["*"],
    "clients": ["*"],
    "rvn_exp": ["*"],
    "vendors": ["*"],
    "dashboard": ["*"],
    "postbacks": ["*"],
    "transfers": ["*"],
    "accounting": ["*"],
    "call_scripts": ["*"],
    "call_campaigns": ["*"]
};

const menu3 = {
    "dashboard": [
        "showMenu"
    ],
    "clients": [
        "showMenu"
    ],
    "leads": [
        "showMenu"
    ],
    "postbacks": [
        "showMenu"
    ],
    "rvn_exp": [
        "showMenu"
    ],
    "tags": [
        "showMenu"
    ],
    "transfers": [
        "showMenu"
    ],
    "vendors": [
        "showMenu"
    ]
}

const menu4 = {
    "dashboard": false,
    "clients": false,
    "leads": false,
    "postbacks": false,
    "rvn_exp": false,
    "tags": false,
    "transfers": false,
    "vendors": false
}

// Usar la versión actual
let originalMenus = menu4;

let isAdmin = false;
const configuration = [
    {
        "id": "leads",
        "name": "Leads",
        "visible": true,
        "expanded": false,
        "tools": [
            {
                "id": "new",
                "name": "New",
                "enabled": false
            },
            {
                "id": "reporting",
                "name": "Generate report",
                "enabled": false
            },
            {
                "id": "update",
                "name": "Update",
                "enabled": false
            },
            {
                "id": "filterDebug",
                "name": "Debug",
                "enabled": false
            }
        ],
        "subMenus": [],
        "route": "leads"
    },
    {
        "id": "transfers",
        "name": "Transfers",
        "visible": true,
        "expanded": false,
        "tools": [
            {
                "id": "reporting",
                "name": "Generate report",
                "enabled": false
            },
            {
                "id": "update",
                "name": "Update",
                "enabled": false
            },
            {
                "id": "filterDebug",
                "name": "Debug",
                "enabled": false
            }
        ],
        "subMenus": [],
        "route": "transfers"
    },
    {
        "id": "postbacks",
        "name": "Postbacks",
        "visible": true,
        "expanded": false,
        "tools": [
            {
                "id": "reporting",
                "name": "Generate report",
                "enabled": false
            },
            {
                "id": "update",
                "name": "Update",
                "enabled": false
            },
            {
                "id": "filterDebug",
                "name": "Debug",
                "enabled": false
            }
        ],
        "subMenus": [],
        "route": "postbacks"
    },
    {
        "id": "reports",
        "name": "Reports",
        "visible": true,
        "expanded": false,
        "tools": [],
        "route": "reports",
        "subMenus": [
            {
                "id": "reports-vendors-performance",
                "name": "Vendor performance",
                "enabled": true,
                "route": "reports/vendorPerformance",
                "tools": []
            }
        ]
    },
    {
        "id": "call-center",
        "name": "Call center",
        "visible": true,
        "expanded": false,
        "tools": [],
        "route": "call-center",
        "subMenus": [
            {
                "id": "call-center-calls",
                "name": "Calls",
                "enabled": true,
                "route": "call-center/calls",
                "tools": [
                    {
                        "id": "filterDebug",
                        "name": "Debug",
                        "enabled": false
                    }
                ]
            },
            {
                "id": "call-center-scripts",
                "name": "Scripts",
                "enabled": true,
                "route": "call-center/scripts",
                "tools": []
            },
            {
                "id": "call-center-scripts",
                "name": "Scripts",
                "enabled": true,
                "route": "call-center/scripts",
                "tools": []
            }
        ]
    },
    {
        "id": "accounting",
        "name": "Accounting",
        "visible": true,
        "expanded": false,
        "tools": [],
        "route": "accounting",
        "subMenus": [
            {
                "id": "accounting-unassigned",
                "name": "Unassigned",
                "enabled": true,
                "route": "accounting/unassigned",
                "tools": [
                    {
                        "id": "filterDebug",
                        "name": "Debug",
                        "enabled": false
                    }
                ]
            },
            {
                "id": "accounting-client",
                "name": "Client",
                "enabled": true,
                "route": "accounting/client",
                "tools": [
                    {
                        "id": "filterDebug",
                        "name": "Debug",
                        "enabled": false
                    }
                ]
            },
            {
                "id": "accounting-vendor",
                "name": "Vendor",
                "enabled": true,
                "route": "accounting/vendor",
                "tools": [
                    {
                        "id": "filterDebug",
                        "name": "Debug",
                        "enabled": false
                    }
                ]
            },
            {
                "id": "accounting-reports",
                "name": "Invoice summary",
                "enabled": true,
                "route": "accounting/reports",
                "tools": [
                    {
                        "id": "filterDebug",
                        "name": "Debug",
                        "enabled": false
                    }
                ]
            },
            {
                "id": "accounting-invoices",
                "name": "Invoices",
                "enabled": true,
                "route": "accounting/invoices",
                "tools": [
                    {
                        "id": "filterDebug",
                        "name": "Debug",
                        "enabled": false
                    },
                    {
                        "id": "edit",
                        "name": "Editar",
                        "enabled": false
                    }
                ]
            }
        ]
    },
    {
        "id": "entities",
        "name": "Entities",
        "visible": true,
        "expanded": false,
        "tools": [],
        "route": "entities",
        "subMenus": [
            {
                "id": "entities-vendors",
                "name": "Vendors",
                "enabled": true,
                "route": "entities/vendors",
                "tools": [
                    {
                        "id": "edit",
                        "name": "Editar",
                        "enabled": false
                    },
                    {
                        "id": "new",
                        "name": "New",
                        "enabled": false
                    },
                    {
                        "id": "match",
                        "name": "Match",
                        "enabled": false
                    }
                ]
            },
            {
                "id": "entities-clients",
                "name": "Clients",
                "enabled": true,
                "route": "entities/clients",
                "tools": [
                    {
                        "id": "edit",
                        "name": "Editar",
                        "enabled": false
                    },
                    {
                        "id": "new",
                        "name": "New",
                        "enabled": false
                    },
                    {
                        "id": "match",
                        "name": "Match",
                        "enabled": false
                    },
                    {
                        "id": "upload",
                        "name": "Upload",
                        "enabled": false
                    }
                ]
            },
            {
                "id": "entities-tags",
                "name": "Tags",
                "enabled": true,
                "route": "entities/tags",
                "tools": [
                    {
                        "id": "new",
                        "name": "New",
                        "enabled": false
                    }
                ]
            }
        ]
    },
    {
        "id": "admin",
        "name": "Admin",
        "visible": true,
        "expanded": false,
        "tools": [],
        "route": "admin",
        "subMenus": [
            {
                "id": "admin-data-broker-queues",
                "name": "Databroker queues",
                "enabled": true,
                "route": "admin/databrokerQueues",
                "tools": []
            },
            {
                "id": "admin-permissions",
                "name": "Permissions",
                "enabled": true,
                "route": "admin/permissions",
                "tools": []
            }
        ]
    }
]


const entitiesRoutes = ['vendors', 'clients', 'tags'];
const reportsRoutes = ['vendor_performance'];
const callCenterRoutes = ['call_scripts', 'calls', 'call_campaigns', 'call_leads', 'script'];
const accountingRoutes = ['unassigned', 'client', 'vendor', 'reports'];
const admin = ['databroker_queues', 'permissions', 'invoices'];
const rol = 'admin';
const toCamelCase = ['databroker_queues', 'call_leads', 'vendor_performance']
// Definir las tools disponibles por tipo de menú
const availableTools = {
    default: ['new', 'reporting', 'filterDebug', 'upload', 'match'],
    // Puedes agregar más configuraciones específicas si es necesario
};

const transformMenu = (metadataMenus) => {
    const transformedMenus = configuration.map(menuItem => {
        const menuEntry = {
            id: menuItem.id,
            tools: [],
            routes: []
        };

        // Función auxiliar para verificar permisos
        const checkPermissions = (menuKey) => {
            const permissions = metadataMenus[menuKey];

            if (!permissions) {
                return {access: false, tools: []};
            }

            if (permissions.includes("*")) {
                return {access: true, tools: ["*"]};
            }

            const tools = permissions.filter(perm => perm !== "showMenu");
            return {
                access: permissions.includes("showMenu"),
                tools: tools
            };
        };

        // Caso especial para reports
        if (menuItem.id === 'reports') {
            // Solo incluir si es admin Y tiene los permisos necesarios
            const vendorPerformancePerms = checkPermissions('vendor_performance');
            const rvnExpPerms = checkPermissions('rvn_exp');
            if ((vendorPerformancePerms.access || rvnExpPerms.access) && isAdmin) {
                menuEntry.routes.push(menuItem.route);
                menuItem.subMenus?.forEach(subMenu => {
                    if (subMenu.id === 'reports-vendors-performance') {
                        menuEntry.routes.push(subMenu.route);
                    }
                });
            }
        }
        // Caso especial para entities
        else if (menuItem.id === 'entities') {
            const vendorsPerms = checkPermissions('vendors');
            const clientsPerms = checkPermissions('clients');
            const tagsPerms = checkPermissions('tags');

            if (vendorsPerms.access || clientsPerms.access || tagsPerms.access) {
                menuEntry.routes.push(menuItem.route);

                menuItem.subMenus?.forEach(subMenu => {
                    const entityType = subMenu.id.split('-')[1];
                    const entityPerms = checkPermissions(entityType);

                    if (entityPerms.access) {
                        menuEntry.routes.push(subMenu.route);
                        // Agregar tools solo si hay permisos específicos
                        if (entityPerms.tools.includes('*') || entityPerms.tools.length > 0) {
                            subMenu.tools?.forEach(tool => {
                                menuEntry.tools.push({
                                    tool: tool.id,
                                    route: subMenu.route
                                });
                            });
                        }
                    }
                });
            }
        }
        // Lógica normal para otros menús
        else {
            const menuPerms = checkPermissions(menuItem.id);
            if (menuPerms.access) {
                menuEntry.routes.push(menuItem.route);
                if (menuPerms.tools.length > 0) {
                    menuItem.tools?.forEach(tool => {
                        if (menuPerms.tools.includes(tool.id) || menuPerms.tools.includes("*")) {
                            menuEntry.tools.push({
                                tool: tool.id,
                                route: menuItem.route
                            });
                        }
                    });
                }
            }
        }

        return menuEntry;
    }).filter(menu => menu.routes.length > 0);

    // Eliminar duplicados
    transformedMenus.forEach(menu => {
        menu.routes = [...new Set(menu.routes)];
        menu.tools = menu.tools.filter((tool, index, self) =>
                index === self.findIndex(t =>
                    t.tool === tool.tool && t.route === tool.route
                )
        );
    });

    return transformedMenus;
}


const roles = await db.collection('roles').find().toArray();
for (const role of roles) {
    console.log('----------------------------------------------')
    console.log('Role: ', role.role);
    let cloneRole = _.cloneDeep(role);
    const metaMenu = role.permissions['menus'];
    console.log('meta: ', metaMenu);
    const finalMenu = transformMenu(metaMenu);
    console.log('Final: ', finalMenu);
    cloneRole.permissions['menus'] = finalMenu;
    const permissions = cloneRole.permissions;
    await db.collection('roles_test').updateOne(
        {_id: role._id},
        {$set: {permissions: permissions}}
    );
    console.log('----------------------------------------------')

}

// Convertir permisos de roles a nueva estructura
// Convertir estructura de permisos de usuarios
