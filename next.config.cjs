import { params } from "@ampt/sdk";

const nextConfig = {
    reactStrictMode: true,
    env: {
        NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: params('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY'),
        CLERK_SECRET_KEY: params('CLERK_SECRET_KEY'),
    },
    experimental: {
        serverComponentsExternalPackages: ['vectra']
    },
    webpack: (config, { isServer }) => {
        // Add vectra to the list of externals to prevent bundling issues
        if (isServer) {
            const nodeExternals = ['vectra'];

            // Find the externals function or array in the webpack config
            const externals = config.externals || [];

            if (Array.isArray(externals)) {
                // If it's an array, push the new externals
                nodeExternals.forEach(ext => {
                    if (!externals.includes(ext)) {
                        externals.push(ext);
                    }
                });
                config.externals = externals;
            } else if (typeof externals === 'function') {
                // If it's a function, wrap it
                config.externals = async (ctx, req, cb) => {
                    const original = await externals(ctx, req, cb);
                    if (nodeExternals.includes(req)) {
                        return req;
                    }
                    return original;
                };
            } else {
                // If it's not an array or function, make it an array
                config.externals = nodeExternals;
            }
        }

        // Fix for npm link React issues - ensure single React instance
        config.resolve = config.resolve || {};
        config.resolve.alias = config.resolve.alias || {};

        // Force all React imports to use the same instance
        config.resolve.alias['react'] = require.resolve('react');
        config.resolve.alias['react-dom'] = require.resolve('react-dom');

        // Ensure React hooks work correctly with linked packages
        config.resolve.alias['react/jsx-runtime'] = require.resolve('react/jsx-runtime');
        config.resolve.alias['react/jsx-dev-runtime'] = require.resolve('react/jsx-dev-runtime');

        return config;
    },
    async headers() {
        return [
            {
                source: "/(.*)",
                headers: [
                    {
                        key: "Access-Control-Allow-Origin",
                        value: "https://accounts.aurionx.ai, https://outgoing-pony-66.accounts.dev, https://accounts.dev.aurionx.ai, https://dev.aurionx.ai",
                    },
                    {
                        key: "Access-Control-Allow-Methods",
                        value: "GET, POST, PUT, DELETE, PATCH, OPTIONS",
                    },
                    {
                        key: "Access-Control-Allow-Headers",
                        value: "X-Requested-With, Content-Type, Authorization",
                    },
                ],
            },
        ];
    }
};

module.exports = nextConfig;
