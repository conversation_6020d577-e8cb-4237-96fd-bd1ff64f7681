import {TelnyxUtils} from "@/app/_lib/utils/phoneServices/telnyx/TelnyxUtils";

const mockTelnyxInstance = {
    availablePhoneNumbers: {
        list: jest.fn()
    },
    numberOrders: {
        create: jest.fn()
    },
    phoneNumbers: {
        del: jest.fn()
    }
};

jest.mock('telnyx', () => {
    return jest.fn().mockImplementation(() => mockTelnyxInstance);
});

describe('TelnyxUtils', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        TelnyxUtils.initialize(mockTelnyxInstance, '2693841595442137027');
    });

    describe('searchAvailableNumbers', () => {
        it('should return phone number when API call is successful', async () => {
            const mockResponse = {
                data: [
                    {
                        "record_type": "available_phone_number",
                        "phone_number": "+19705555123",
                        "country_code": "US",
                        "features": ["voice", "sms"]
                    }
                ]
            };

            mockTelnyxInstance.availablePhoneNumbers.list.mockResolvedValue(mockResponse);
            const result = await TelnyxUtils.searchAvailableNumbers();

            expect(mockTelnyxInstance.availablePhoneNumbers.list).toHaveBeenCalledWith({
                "filter[country_code]": "US",
                "filter[limit]": 1,
                "filter[features]": ["voice"],
                "filter[phone_number_type]": "local"
            });

            expect(result).toBe("+19705555123");
        });

        it('should throw error when API returns empty data array', async () => {
            mockTelnyxInstance.availablePhoneNumbers.list.mockResolvedValue({
                data: []
            });

            await expect(TelnyxUtils.searchAvailableNumbers()).rejects.toThrow(
                "No phone numbers found matching the criteria"
            );
        });

        it('should throw error when phone number is missing', async () => {
            mockTelnyxInstance.availablePhoneNumbers.list.mockResolvedValue({
                data: [
                    {
                        "record_type": "available_phone_number",
                        "phone_number": "",
                        "country_code": "US"
                    }
                ]
            });

            await expect(TelnyxUtils.searchAvailableNumbers()).rejects.toThrow(
                "Phone number is missing or empty in the response"
            );
        });

        it('should retry on failure and succeed on second attempt', async () => {
            mockTelnyxInstance.availablePhoneNumbers.list
                .mockResolvedValueOnce({data: []}) // Primer intento: falla
                .mockResolvedValueOnce({             // Segundo intento: éxito
                    data: [
                        {
                            "record_type": "available_phone_number",
                            "phone_number": "+19705555098",
                            "country_code": "US"
                        }
                    ]
                });

            const result = await TelnyxUtils.searchAvailableNumbers();
            expect(mockTelnyxInstance.availablePhoneNumbers.list).toHaveBeenCalledTimes(2);
            expect(result).toBe("+19705555098");
        });
    });

    describe('purchaseNumber', () => {
        it('should return order details when purchase is successful', async () => {
            const mockResponse = {
                "data": {
                    "id": "12ade33a-21c0-473b-b055-b3c836e1c292",
                    "record_type": "number_order",
                    "phone_numbers_count": 1,
                    "connection_id": "346789098765567",
                    "messaging_profile_id": "abc85f64-5717-4562-b3fc-2c9600",
                    "billing_group_id": "abc85f64-5717-4562-b3fc-2c9600",
                    "phone_numbers": [
                        {
                            "id": "dc8e4d67-33a0-4cbb-af74-7b58f05bd494",
                            "record_type": "number_order_phone_number",
                            "phone_number": "+19705555098",
                            "order_request_id": "dc8e4d67-33a0-4cbb-af74-7b58f05bd495",
                            "sub_number_order_id": "dc8e4d67-33a0-4cbb-af74-7b58f05bd496",
                            "country_code": "US",
                            "phone_number_type": "local",
                            "regulatory_requirements": [
                                {
                                    "record_type": "phone_number_regulatory_requirement",
                                    "requirement_id": "8ffb3622-7c6b-4ccc-b65f-7a3dc0099576",
                                    "field_type": "address",
                                    "field_value": "45f45a04-b4be-4592-95b1-9306b9db2b21"
                                }
                            ],
                            "requirements_met": true,
                            "status": "pending",
                            "bundle_id": null,
                            "locality": "San Francisco",
                            "deadline": "024-05-31T11:14:00+00:00",
                            "requirements_status": "pending",
                            "is_block_number": false
                        }
                    ],
                    "sub_number_orders_ids": [
                        "string"
                    ],
                    "status": "pending",
                    "customer_reference": "MY REF 001",
                    "created_at": "2018-01-01T00:00:00.000000Z",
                    "updated_at": "2018-01-01T00:00:00.000000Z",
                    "requirements_met": true
                }
            };
            mockTelnyxInstance.numberOrders.create.mockResolvedValue(mockResponse);
            const result = await TelnyxUtils.purchaseNumber("+19705555098");

            expect(mockTelnyxInstance.numberOrders.create).toHaveBeenCalledWith({
                phone_numbers: [{"phone_number": "+19705555098"}],
                connection_id: '2693841595442137027'
            });

            expect(result).toEqual({
                numberOrderId: '12ade33a-21c0-473b-b055-b3c836e1c292',
                numberOrderPhone: 'dc8e4d67-33a0-4cbb-af74-7b58f05bd494',
                status: 'pending'
            });
        });

        it('should throw error when phone number is empty', async () => {

            await expect(TelnyxUtils.purchaseNumber('')).rejects.toThrow(
                "Phone number is required for purchase"
            );

            expect(mockTelnyxInstance.numberOrders.create).not.toHaveBeenCalled();
        });

        it('should throw error when API returns invalid response', async () => {

            mockTelnyxInstance.numberOrders.create.mockResolvedValue({
                data: {} // Sin phone_numbers
            });

            await expect(TelnyxUtils.purchaseNumber("+19705555098")).rejects.toThrow(
                "No phone numbers found in the response"
            );
        });
    });

    describe("releaseNumber", () => {
        it('should return release details when release is successful', async () => {
            const mockResponse = {
                "data": {
                    "id": "1293384261075731499",
                    "record_type": "phone_number",
                    "phone_number": "+19705555098",
                    "status": "deleted",
                    "tags": [
                        "tag_1",
                        "tag_2"
                    ],
                    "external_pin": "1234",
                    "connection_id": "1293384261075731499",
                    "connection_name": "connection-name",
                    "customer_reference": "customer-reference",
                    "messaging_profile_id": "abc85f64-5717-4562-b3fc-2c9600000000",
                    "messaging_profile_name": "regional-customers",
                    "billing_group_id": "86f58db9-0fe3-4adc-9d1f-46e66e6e9323",
                    "emergency_enabled": true,
                    "emergency_address_id": "1315261609962112019",
                    "call_forwarding_enabled": true,
                    "cnam_listing_enabled": true,
                    "caller_id_name_enabled": true,
                    "call_recording_enabled": true,
                    "t38_fax_gateway_enabled": true,
                    "phone_number_type": "local",
                    "purchased_at": "2019-10-23T18:10:00.000Z",
                    "created_at": "2019-10-23T18:10:00.000Z",
                    "updated_at": "2019-10-24T18:10:00.000Z",
                    "hd_voice_enabled": true
                }
            }
            mockTelnyxInstance.phoneNumbers.del.mockResolvedValue(mockResponse);
            const result = await TelnyxUtils.releasePhoneNumber("1293384261075731499");
            expect(result).toEqual({
                operationId: '1293384261075731499'
            })
        })

        it("should result is empty or null", async () => {
            mockTelnyxInstance.phoneNumbers.del.mockResolvedValue(null);
            await expect(TelnyxUtils.releasePhoneNumber('1293384261075731499')).rejects.toThrow(
                "No response received from Telnyx API when releasing phone number"
            );
        })

        it("should return phone number not deleted", async () => {
            const mockResponse = {
                "data": {
                    "id": "1293384261075731499",
                    "record_type": "phone_number",
                    "phone_number": "+19705555098",
                    "status": "error",
                }
            }
            mockTelnyxInstance.phoneNumbers.del.mockResolvedValue(mockResponse);
            await expect(TelnyxUtils.releasePhoneNumber('1293384261075731499')).rejects.toThrow(
                "Status is not deleted in the response"
            );
        })

    })
});
