import {NextResponse} from "next/server";
import {getSessionCookie} from "better-auth/cookies";

const PUBLIC_ROUTES = {
    CORE: ['/', '/sign-up', '/sign-in', '/error', '/test', '/not-found', '/accept-invitation', '/maintenance'],
    API: [
        '/api/public',
        '/api/betterAuth/sendMailRequestAccess',
        '/api/organization'
    ],
    AUTH: '/api/auth'
};

export async function middleware(request) {
    const pathname = request.nextUrl.pathname;
    if (isPublicRoute(pathname) || pathname.startsWith('/api/')) return baseResponse(request);

    const sessionCookie = getSessionCookie(request);
    if (!sessionCookie) return redirectToSignIn(request);
    return baseResponse(request);
}

function isPublicRoute(pathname) {
    if (!pathname) return false;

    return (
        PUBLIC_ROUTES.CORE.some((route) => matchRoute(pathname, route)) ||
        PUBLIC_ROUTES.API.some((route) => matchRoute(pathname, route)) ||
        pathname.startsWith(PUBLIC_ROUTES.AUTH)
    );
}

function matchRoute(pathname, route) {
    return pathname === route || pathname.startsWith(`${route}/`);
}

function baseResponse(request) {
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('x-pathname', request.nextUrl.pathname);
    return NextResponse.next({
        request: {
            headers: requestHeaders,
        },
    });
}

function redirectToSignIn(request) {
    return NextResponse.redirect(new URL('/sign-in', request.url));
}

export const config = {
    matcher: [
        '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
        '/(api|trpc)(.*)'
    ]
};
