<p align="center">
    <div width="100%" align="center">
        <h1>Ampt + Next.js Template</h1>
    </div>
    <p align="center">
        <img src="https://ampt.dev/public/templates/ampt-api.svg" alt="ampt-logo" width="180" height="150" />
        <img src="https://ampt.dev/public/templates/nextjs.svg" alt="nuxt-logo" width="180"height="150" />
    </p>
</p>

## Welcome to Ampt!

[<img src="https://getampt.com/button"/>](https://ampt.dev/start?template=nextjs)

To run this app locally, simply clone down this repository and run `ampt`. You may need to login to the Ampt CLI if you
haven't already (you can install this by running `npm install -g @ampt/cli`). To start the local development server,
run `ampt dev` or `dev` in the Ampt shell.

Happy coding!

##### AMPT APP PARAMS

NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=

CLERK_SECRET_KEY=

NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up

NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/

## Table Configurations

### FilterSelectComponent for external table

```
{
    "config": {
        "editable": false,
        "show": true,
        "select": "external",
        "selectTable": "clients", //table to join
        "foreignSelectLabel": "client", //column for select label
        "foreignSelectId": "_id", //column to use in filter
        "localFilterId": "client_id" //column in local table for filter 
        },
    "header": "Client name",
    "accessorKey": "client_name",
    "filterComponent": "SelectFilterComponent"
}
```

### Global search

```
{
    "global_search": {
        "show": true, // if the GS can be show
        "key": "lead_id", // if the value is uuid, search by
        "fields": [ // all fields from compund index
            "Lead id", 
            "Consumer phone",
            "Email",
            "Vendor name",
            "Transfer to"
        ]
    }
}
```

### Summary Report

```
{
    "summary_conf": {
   
        "select_keys": [  // Options who can be set on report
          {
            "key": "vendor_id", // local key collection
            "foreign": "vendors", // go to other collection for select
            "show": "vendor", // key for show, this key are from coll selected
            "replace": "XXXXXXX" // id or another value for replace if not exits relation 
          },
          {
            "key": "",
            "foreign": "",
            "show": "",
            "replace": ""
          }
        ],
        "required_key": "duration", // the key for set the data search
        "options": [ // the option for evaluate the aggregation
          {
            "name": ">=120s", // name of column in the report
            "value": 120, // value for evaluate in aggregation
            "func": "gte" // mongo func for evaluate
          },
          {
            "name": "<=120s",
            "value": 119,
            "func": "lte"
          }
        ],
        "filter": "completed_date" // key filter by 
  }

```

### Match report

This item is an array of objects that define the matching rules for the report.

```
{
    "foreign": Collection to query for matching data.
    "f_key": Field in the foreign collection to compare.
    "f_key2": Secondary key in the foreign collection to use if it matches.
    "local_key": Local field to compare with f_key.
    "show": Field to display on the front end.
    "replace": Default value if the matched field is null.
    "drill_down": Configuration for the second-level report:
    "label": Display name for the drill-down field.
    "key": Field to use as the drill-down key.
    "value": Field to use as the drill-down value.
}
```

### Add new register

This conf can be added inside conf of keys collection

```
{
    "config": {
        // sets if the key are like vendo_name or vendor_id
        // this key can merge this fields and set one global for mapping after    
        "reference": "vendor"
        
        // sets if the key can be added on new reg
        // if this key are false, put the field default on null 
        "newReg": true
        
        // sets if the key can be added more than one apart from select's options
        "canAdd": true
        
        // sets if the key type DATE take the day created new reg
        "dateNow": true
    }
}
```

### Editable register

This conf can be editable the register

```
{
    // if the register is editable
    "editable_data": true / false 
}
```

### Check Keys Restricted

This conf can be checking if this table need restricted keys

```
{
    // if check keys restricted
    "check_keys_restricts": true / false 
}
```

### Tools

```
{
    // view tools all users, not only one
    // ALL true / false
    "accounting": true,
    "new": true,
    "upload": true,
    "edit": true,
    "match": true,
    "reporting": true 
}
```
